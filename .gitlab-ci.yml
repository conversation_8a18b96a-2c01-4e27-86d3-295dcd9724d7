#proxy-variables: &proxy-variables
#  PROXY: "http://proxygate2.ctripcorp.com:8080"
#  HTTP_PROXY: $PROXY
#  HTTPS_PROXY: $PROXY
#  NO_PROXY: ".ctripcorp.com"

stages:          # List of stages for jobs, and their order of execution
  - build

build-job:
  image: hub.cloud.ctripcorp.com/python3.9-base/slim@sha256:30de029e44d58ef76c39fdaaad2ced2983a6e5826000fd219c5670ce4f50059e
  stage: build

  only:
    - hotel
    # - feat/dongmingxu/developent

  before_script:
    - export PROXY="http://proxygate2.ctripcorp.com:8080"
    - export HTTP_PROXY="http://proxygate2.ctripcorp.com:8080"
    - export HTTPS_PROXY="http://proxygate2.ctripcorp.com:8080"
    - export NO_PROXY=".ctripcorp.com"
    - echo 'Acquire::http::Proxy "http://proxygate2.ctripcorp.com:8080";' > /etc/apt/apt.conf.d/01proxy
    - apt-get update
    - apt-get install -y libmariadb-dev libmariadb-dev-compat pkg-config gcc
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ mysqlclient
    - echo "安装环境依赖"
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ --upgrade pip
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ pbr
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ wheel
#    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ setuptools==57.5.0
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/  opencv-contrib-python==********
    - pip uninstall -y numpy
    - pip install --index-url http://artifactory.release.ctripcorp.com/artifactory/api/pypi/trip-pypi-prod/simple --trusted-host artifactory.release.ctripcorp.com -r requirements.txt
    - pip install --trusted-host mirrors.ops.ctripcorp.com -i http://mirrors.ops.ctripcorp.com/pypi-latest/web/simple/ twine==3.5.0

  script:
    - echo "开始打包"
    - python setup.py sdist bdist_wheel
    - |
      echo "[distutils]" > ~/.pypirc
      echo "index-servers = local" >> ~/.pypirc
      echo "[local]" >> ~/.pypirc
      echo "repository = http://artifactory.release.ctripcorp.com/artifactory/api/pypi/trip-pypi-prod-shanghai" >> ~/.pypirc
      echo "username = <EMAIL>" >> ~/.pypirc
      echo "password = cmVmdGtuOjAxOjAwMDAwMDAwMDA6a0xBNE45WnNrVTNZWks2aEFUQk1BTzJiWUVB" >> ~/.pypirc
    - echo "执行上传"
    - twine upload --repository local dist/*
