#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试边界框绘制优化的脚本
"""

import sys
import os
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import random
import colorsys

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_random_color(seq_index):
    """根据seq_index生成随机但一致的颜色"""
    # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
    random.seed(hash(str(seq_index)) % 2147483647)
    
    # 生成高饱和度、中等亮度的颜色，确保视觉效果好
    hue = random.random()  # 色相：0-1
    saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
    lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7
    
    # 转换HSL到RGB
    rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
    return tuple(int(c * 255) for c in rgb)

def get_contrasting_text_color(bg_color):
    """根据背景颜色获取对比度高的文本颜色"""
    # 计算背景颜色的亮度
    r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
    brightness = (r * 299 + g * 587 + b * 114) / 1000
    
    # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
    return (255, 255, 255) if brightness < 128 else (0, 0, 0)

def create_test_image():
    """创建一个测试图像来演示边界框绘制效果"""
    # 创建一个800x600的白色背景图像
    width, height = 800, 600
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img, 'RGBA')
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("Arial", 14)
    except:
        font = ImageFont.load_default()
    
    # 模拟一些UI元素的边界框
    test_elements = [
        {"seq_index": 0, "x1": 50, "y1": 50, "x2": 200, "y2": 100, "text": "按钮"},
        {"seq_index": 1, "x1": 250, "y1": 50, "x2": 400, "y2": 100, "text": "输入框"},
        {"seq_index": 2, "x1": 450, "y1": 50, "x2": 600, "y2": 100, "text": ""},
        {"seq_index": 3, "x1": 50, "y1": 150, "x2": 300, "y2": 200, "text": "长文本标签示例"},
        {"seq_index": 4, "x1": 350, "y1": 150, "x2": 500, "y2": 200, "text": "图片"},
        {"seq_index": 5, "x1": 50, "y1": 250, "x2": 750, "y2": 350, "text": "列表容器"},
        {"seq_index": 6, "x1": 100, "y1": 280, "x2": 200, "y2": 320, "text": "项目1"},
        {"seq_index": 7, "x1": 250, "y1": 280, "x2": 350, "y2": 320, "text": "项目2"},
        {"seq_index": 8, "x1": 400, "y1": 280, "x2": 500, "y2": 320, "text": "项目3"},
        {"seq_index": 9, "x1": 50, "y1": 400, "x2": 200, "y2": 500, "text": "底部按钮"},
    ]
    
    # 重置随机种子，确保颜色生成的一致性
    random.seed(42)
    
    # 绘制每个元素的边界框
    for element in test_elements:
        seq_index = element["seq_index"]
        x1, y1, x2, y2 = element["x1"], element["y1"], element["x2"], element["y2"]
        text_content = element["text"]
        
        # 生成随机颜色
        border_color = generate_random_color(seq_index)
        
        # 绘制边界框
        draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)
        
        # 构建标签文本
        label_text = f"{seq_index}"
        
        # 绘制标签
        if label_text:
            try:
                # 限制标签文本总长度
                if len(label_text) > 25:
                    label_text = label_text[:22] + "..."
                
                # 计算文本大小
                try:
                    bbox = draw.textbbox((0, 0), label_text, font=font)
                    text_width = bbox[2] - bbox[0] + 6
                    text_height = bbox[3] - bbox[1] + 4
                except:
                    text_width = len(label_text) * 8 + 6
                    text_height = 16
                
                # 确保标签不会超出屏幕边界
                label_x = x1
                if label_x + text_width > width:
                    label_x = width - text_width
                if label_x < 0:
                    label_x = 0
                
                # 确保标签不会超出屏幕顶部
                label_y = y1 - text_height
                if label_y < 0:
                    label_y = y2
                    if label_y + text_height > height:
                        label_y = height - text_height
                
                # 创建标签背景颜色
                bg_color = border_color + (200,)
                
                # 绘制标签背景
                draw.rectangle(
                    [label_x, label_y, label_x + text_width, label_y + text_height],
                    fill=bg_color
                )
                
                # 获取对比度高的文本颜色
                text_color = get_contrasting_text_color(border_color)
                
                # 绘制文本
                draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)
                
            except Exception as e:
                print(f"绘制标签失败: {str(e)}")
    
    return img

def main():
    """主函数"""
    print("开始测试边界框绘制优化...")
    
    # 创建测试图像
    test_img = create_test_image()
    
    # 确保output目录存在
    if not os.path.exists("output"):
        os.makedirs("output")
    
    # 保存测试图像
    output_path = "output/test_bounding_boxes.png"
    test_img.save(output_path)
    
    print(f"测试图像已保存到: {output_path}")
    print("优化特性:")
    print("1. ✅ 每个边界框使用不同的随机颜色")
    print("2. ✅ 标签包含seq_index和文本内容")
    print("3. ✅ 标签背景颜色与边界框颜色匹配")
    print("4. ✅ 文本颜色根据背景自动调整对比度")
    print("5. ✅ 标签位置智能调整，避免超出屏幕边界")
    print("6. ✅ 边界框线条加粗，提高可见性")
    
    # 测试颜色生成的一致性
    print("\n测试颜色生成一致性:")
    for i in range(5):
        color1 = generate_random_color(i)
        color2 = generate_random_color(i)
        print(f"seq_index {i}: {color1} == {color2} -> {color1 == color2}")

if __name__ == "__main__":
    main()
