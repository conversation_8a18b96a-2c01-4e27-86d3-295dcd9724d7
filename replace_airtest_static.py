#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
airtest ADB管理工具 - 解决ADB版本不匹配问题

主要功能：
1. 检查系统ADB和airtest内置ADB的版本
2. 使用系统ADB替换airtest内置ADB（单个文件）
3. 替换airtest的static/adb目录（不影响其他静态资源）

使用方式：
- 作为模块导入: 
  from replace_airtest_static import check_adb_version, replace_adb, replace_airtest_adb_dir
  
- 命令行使用:
  检查版本: python replace_airtest_static.py -c
  替换单个ADB: python replace_airtest_static.py -a
  替换整个adb目录: python replace_airtest_static.py
  使用指定目录替换: python replace_airtest_static.py -p /path/to/your/adb_dir
"""

import os
import sys
import shutil
import argparse
import subprocess
import re
import platform

def get_airtest_paths():
    """
    获取airtest相关路径
    
    Returns:
        tuple: (airtest_adb_path, airtest_adb_dir, static_dir)
        如果未找到airtest库，则返回(None, None, None)
    """
    try:
        import airtest.core.android.constant as constant
        # 获取constant.py所在的目录
        constant_dir = os.path.dirname(os.path.abspath(constant.__file__))
        static_dir = os.path.join(constant_dir, "static")
        airtest_adb_dir = os.path.join(static_dir, "adb")
        
        # 根据平台获取adb路径
        if sys.platform.startswith('win'):
            airtest_adb_path = constant.DEFAULT_ADB_PATH["Windows"]
        elif sys.platform == 'darwin':
            airtest_adb_path = constant.DEFAULT_ADB_PATH["Darwin"]
        elif sys.platform.startswith('linux'):
            machine = platform.machine()
            if machine == 'armv7l':
                airtest_adb_path = constant.DEFAULT_ADB_PATH["Linux-armv7l"]
            else:
                airtest_adb_path = constant.DEFAULT_ADB_PATH["Linux-x86_64"]
        
        return airtest_adb_path, airtest_adb_dir, static_dir
    except ImportError:
        print("错误: 找不到airtest库，请确保已安装")
        return None, None, None

def get_system_adb_info():
    """
    获取系统ADB信息
    
    Returns:
        tuple: (system_adb_version, system_adb_path)
        如果未找到系统ADB，则返回(None, None)
    """
    system_adb_version = None
    system_adb_path = None
    
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            match = re.search(r'Android Debug Bridge version (\d+\.\d+\.\d+)', result.stdout)
            if match:
                system_adb_version = match.group(1)
                
                # 获取系统ADB路径
                if sys.platform.startswith('win'):
                    result = subprocess.run(['where', 'adb'], capture_output=True, text=True)
                else:
                    result = subprocess.run(['which', 'adb'], capture_output=True, text=True)
                
                if result.returncode == 0:
                    system_adb_path = result.stdout.strip().split('\n')[0]
    except FileNotFoundError:
        pass
    
    return system_adb_version, system_adb_path

def get_airtest_adb_version():
    """
    获取airtest内置ADB版本
    
    Returns:
        str: airtest内置ADB版本，如果获取失败则返回None
    """
    airtest_adb_path, _, _ = get_airtest_paths()
    if not airtest_adb_path or not os.path.exists(airtest_adb_path):
        return None
    
    try:
        # 确保执行权限
        if not sys.platform.startswith('win'):
            os.chmod(airtest_adb_path, 0o755)
        
        result = subprocess.run([airtest_adb_path, 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            match = re.search(r'Android Debug Bridge version (\d+\.\d+\.\d+)', result.stdout)
            if match:
                return match.group(1)
    except Exception:
        pass
    
    return None

def check_adb_version(verbose=True):
    """
    检查系统ADB和airtest内置ADB的版本
    
    Args:
        verbose: 是否输出详细信息
        
    Returns:
        tuple: (系统ADB版本, 系统ADB路径, airtest内置ADB路径, airtest内置ADB版本)
    """
    system_adb_version, system_adb_path = get_system_adb_info()
    airtest_adb_path, _, _ = get_airtest_paths()
    airtest_adb_version = get_airtest_adb_version()
    
    if verbose:
        if system_adb_version:
            print(f"系统ADB版本: {system_adb_version}")
            if system_adb_path:
                print(f"系统ADB路径: {system_adb_path}")
        else:
            print("系统中未找到ADB命令")
        
        if airtest_adb_path and os.path.exists(airtest_adb_path):
            print(f"Airtest内置ADB路径: {airtest_adb_path}")
            if airtest_adb_version:
                print(f"Airtest内置ADB版本: {airtest_adb_version}")
            else:
                print("无法获取Airtest内置ADB版本")
        else:
            print("未找到Airtest内置ADB")
        
        # 检查版本是否匹配
        if system_adb_version and airtest_adb_version:
            if system_adb_version == airtest_adb_version:
                print("✓ ADB版本匹配")
            else:
                print(f"⚠ ADB版本不匹配: 系统({system_adb_version}) vs Airtest({airtest_adb_version})")
    
    return system_adb_version, system_adb_path, airtest_adb_path, airtest_adb_version

def replace_adb(system_adb_path=None, airtest_adb_path=None, verbose=True):
    """
    用系统ADB替换airtest内置ADB (单个文件)
    
    Args:
        system_adb_path: 系统ADB路径，如果为None则自动检测
        airtest_adb_path: airtest内置ADB路径，如果为None则自动检测
        verbose: 是否输出详细信息
        
    Returns:
        bool: 替换成功返回True，否则返回False
    """
    # 如果未提供路径，则自动检测
    if system_adb_path is None or airtest_adb_path is None:
        system_adb_version, system_adb_path, airtest_adb_path, _ = check_adb_version(verbose=False)
    
    if not system_adb_path or not os.path.exists(system_adb_path):
        if verbose:
            print("错误: 系统ADB路径无效")
        return False
    
    if not airtest_adb_path or not os.path.exists(airtest_adb_path):
        if verbose:
            print("错误: Airtest内置ADB路径无效")
        return False
    
    try:
        # 备份原始ADB
        airtest_adb_dir = os.path.dirname(airtest_adb_path)
        backup_path = os.path.join(airtest_adb_dir, "adb.backup")
        
        if os.path.exists(backup_path):
            os.remove(backup_path)
        
        if verbose:
            print(f"备份内置ADB: {airtest_adb_path} -> {backup_path}")
        shutil.copy2(airtest_adb_path, backup_path)
        
        # 复制系统ADB到airtest
        if verbose:
            print(f"复制系统ADB: {system_adb_path} -> {airtest_adb_path}")
        shutil.copy2(system_adb_path, airtest_adb_path)
        
        # 确保文件有执行权限
        if not sys.platform.startswith('win'):
            os.chmod(airtest_adb_path, 0o755)
        
        if verbose:
            print("ADB替换完成！")
        return True
    except Exception as e:
        if verbose:
            print(f"替换ADB过程中发生错误: {str(e)}")
        return False

def replace_airtest_adb_dir(project_adb_path=None, verbose=True, force=False):
    """
    替换airtest库中的adb目录，不影响其他静态资源
    
    Args:
        project_adb_path: 指定的adb目录路径，如果为None则使用当前目录下的adb
        verbose: 是否输出详细信息
        force: 是否强制替换，即使目录结构不完整
        
    Returns:
        bool: 替换成功返回True，否则返回False
    """
    try:
        # 获取项目adb目录
        if project_adb_path is None:
            project_dir = os.getcwd()
            project_adb_dir = os.path.join(project_dir, 'adb')  # 默认查找当前目录下的adb目录
        else:
            project_adb_dir = os.path.abspath(project_adb_path)
        
        # 检查项目adb目录是否存在
        if not os.path.exists(project_adb_dir):
            if verbose:
                print(f"错误: 项目adb目录不存在: {project_adb_dir}")
            return False
        
        # 获取airtest路径
        airtest_adb_path, airtest_adb_dir, static_dir = get_airtest_paths()
        if not airtest_adb_dir:
            return False
        
        if verbose:
            print(f"找到airtest adb目录: {airtest_adb_dir}")
        
        # 检查项目adb目录结构是否合适
        required_dirs = ["linux", "linux_arm", "mac", "windows"]
        is_valid_structure = False
        
        # 检查是否为包含所有平台的完整adb目录
        if os.path.isdir(project_adb_dir):
            subdirs = [d for d in os.listdir(project_adb_dir) if os.path.isdir(os.path.join(project_adb_dir, d))]
            has_platform_dirs = all(platform_dir in subdirs for platform_dir in required_dirs)
            if has_platform_dirs:
                is_valid_structure = True
        
        if not is_valid_structure and not force:
            if verbose:
                print(f"警告: 项目adb目录结构不完整，应包含这些子目录: {required_dirs}")
                print("替换已取消。使用force=True参数可以强制替换。")
            return False
        
        # 备份原始adb目录
        backup_dir = os.path.join(static_dir, "adb_backup")
        if os.path.exists(backup_dir):
            if verbose:
                print(f"删除已存在的备份目录: {backup_dir}")
            shutil.rmtree(backup_dir)
        
        if verbose:
            print(f"备份原始adb目录: {airtest_adb_dir} -> {backup_dir}")
        shutil.copytree(airtest_adb_dir, backup_dir)
        
        # 删除原有adb目录
        if verbose:
            print(f"删除原有adb目录: {airtest_adb_dir}")
        shutil.rmtree(airtest_adb_dir)
        
        # 复制项目adb目录到airtest
        if verbose:
            print(f"从 {project_adb_dir} 复制到: {airtest_adb_dir}")
        shutil.copytree(project_adb_dir, airtest_adb_dir)
        
        # 确保所有adb文件都有执行权限
        if not sys.platform.startswith('win'):
            for root, dirs, files in os.walk(airtest_adb_dir):
                for file in files:
                    if file == 'adb' or file == 'adb.exe':
                        file_path = os.path.join(root, file)
                        if verbose:
                            print(f"添加执行权限: {file_path}")
                        os.chmod(file_path, 0o755)
        
        if verbose:
            print("adb目录替换完成！")
        return True
    
    except Exception as e:
        if verbose:
            print(f"替换过程中发生错误: {str(e)}")
        return False

def is_adb_version_mismatch():
    """
    检查系统ADB和airtest内置ADB的版本是否不匹配
    
    Returns:
        bool: 如果版本不匹配返回True，否则返回False
        如果无法获取任一版本，则返回None
    """
    system_adb_version, _, _, airtest_adb_version = check_adb_version(verbose=False)
    
    if system_adb_version and airtest_adb_version:
        return system_adb_version != airtest_adb_version
    
    return None

def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='替换airtest库中的adb目录或单个ADB文件，解决ADB版本不匹配问题')
    parser.add_argument('-p', '--path', help='指定项目adb目录的路径，默认使用当前目录下的adb')
    parser.add_argument('-c', '--check', action='store_true', help='仅检查ADB版本，不进行替换')
    parser.add_argument('-a', '--adb', action='store_true', help='使用系统ADB替换airtest内置ADB，仅替换单个文件')
    parser.add_argument('-f', '--force', action='store_true', help='强制替换，即使目录结构不完整')
    args = parser.parse_args()
    
    if args.check:
        # 检查ADB版本
        check_adb_version()
        return 0
    
    if args.adb:
        # 使用系统ADB替换airtest内置ADB
        success = replace_adb()
        return 0 if success else 1
    else:
        # 替换airtest adb目录
        success = replace_airtest_adb_dir(args.path, force=args.force)
        return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 