# 调试工具设计文档

## 项目概述

这是一个基于Python和Tkinter开发的调试工具UI，旨在简化包管理、仓库操作和任务触发等开发测试流程。该工具提供了直观的图形界面，支持多种环境配置选项，并集成了多个自动化功能。

## 功能需求

### 1. 环境配置

#### 1.1 Python环境选择
- 支持用户选择使用系统Python或Conda环境
- 当选择Conda时，允许用户指定具体的Conda环境名称
- 显示当前Python环境信息（版本号、pip版本、环境路径）

### 2. 包管理功能

#### 2.1 本地包构建
- 使用setup-test.py构建包
- 构建前自动重置Python环境
- 自动清理之前的构建目录（dist和build）
- 支持在不同Python环境下构建

#### 2.2 包安装管理
- 支持本地安装刚构建的whl包
- 安装前自动重置Python环境
- 提供包版本查看功能

#### 2.3 包发布功能
- 支持将构建的whl包上传到测试环境
- 上传前自动重新安装twine
- 提供上传状态和结果反馈

### 3. 仓库操作功能

#### 3.1 UI测试仓库管理
- 仓库：hotel-order-detail-ui-test
- 分支：feat/yangyangpeng/test
- 支持查看当前依赖版本
- 自动更新requirements.txt中的包版本

#### 3.2 AI生成仓库管理
- 仓库：aigenerate-test
- 分支：main
- 支持查看当前依赖版本
- 自动更新requirements.txt中的包版本

### 4. 任务管理功能

#### 4.1 MPAAS任务操作
- 支持多个预设任务的选择和触发：
  - Trip-Online-UiLabTestjob (464)
  - Trip-H5-UiLabTestjob (463)
  - UiLabTestjob (454)
- 提供任务执行状态实时查看
- 支持任务结果轮询功能
- 提供任务报告链接快速访问

#### 4.2 任务监控功能
- 支持查看所有已触发任务的列表
- 提供任务详细信息显示：
  - 任务ID
  - 任务名称
  - 执行状态
  - 执行结果
  - 成功率统计
  - 报告链接
- 支持任务列表实时刷新
- 支持通过双击快速打开任务报告

### 5. 日志管理功能
- 提供实时日志输出显示
- 支持日志清理功能
- 日志内容包含：
  - 操作执行状态
  - 错误信息提示
  - 任务执行进度
  - 结果反馈信息

## 技术架构

### 1. 前端界面
- 基于Tkinter构建GUI界面
- 采用网格布局（Grid）实现灵活的界面排版
- 使用ttk主题优化界面外观

### 2. 后端实现
- 使用Python实现核心功能
- 多线程处理耗时操作
- 模块化设计，核心功能封装在DebugToolUtils类中

### 3. 数据交互
- 使用JSON格式处理API响应
- 支持文件系统操作
- 集成Git操作功能

## 安全性考虑

### 1. 环境安全
- 操作前进行环境检查
- 提供环境重置功能
- 防止误操作导致环境污染

### 2. 数据安全
- 使用token进行仓库认证
- 避免敏感信息硬编码
- 操作前进行必要的权限验证

## 使用建议

### 1. 环境准备
- 确保已安装Python环境
- 如需使用Conda，请提前安装并创建所需环境
- 确保网络连接正常

### 2. 操作流程
1. 选择合适的Python环境
2. 执行包构建和安装
3. 进行仓库更新操作
4. 触发并监控任务执行
5. 查看任务执行结果

### 3. 注意事项
- 操作前请确认环境配置正确
- 重要操作建议先查看依赖版本
- 任务执行时建议开启轮询监控
- 及时查看日志输出信息

## 错误处理

### 1. 常见错误
- 环境配置错误
- 包构建失败
- 上传权限问题
- 任务执行超时

### 2. 处理建议
- 查看日志确认错误原因
- 检查环境配置是否正确
- 确认网络连接状态
- 必要时清理环境重新操作

## 维护建议

### 1. 日常维护
- 定期清理构建目录
- 更新依赖包版本
- 检查任务执行状态

### 2. 问题排查
- 查看日志记录
- 检查环境配置
- 确认网络状态
- 验证权限设置 