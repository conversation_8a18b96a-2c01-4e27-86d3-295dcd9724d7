from labuiframe.lib.utils.ProcessUtils import ProcessUtils


def dictExchangeStr(dict):
    print("testProcess")
    str = """{"acc_touchablewithoutfeedback_components_plusminusbtn_0":{"desc":"房间数量减少按钮","component_name":"room_count_decrease_btn","operation":["click"]},"acc_Link_components_plusminusbtn_1":{"desc":"房间数量增加按钮","component_name":"room_count_increase_btn","operation":["click"]},"acc_touchableopacity_roomsingle_roomtitle_0_e2e":{"desc":"住客姓名点击区域","component_name":"guest_name_click_area","operation":["click"]},"acc_i_CnName_0-0-0":{"desc":"住客姓名输入框","component_name":"guest_name_input","operation":["get","set"]},"htl_c_app_fill_checkinNameSelectLayer_click":{"desc":"选择入住人按钮","component_name":"select_checkin_person_btn","operation":["click"]},"acc_touchableopacity_roomguestsphone_phonetitle_0_e2e":{"desc":"联系电话区号点击区域","component_name":"phone_area_code_click_area","operation":["click"]},"acc_i_phone_0":{"desc":"联系电话输入框","component_name":"phone_input","operation":["get","set"]},"htl_c_app_fill_contactPhoneLayerEntrace_click":{"desc":"联系电话右侧图标点击区域","component_name":"phone_icon_click_area","operation":["click"]},"acc_commonicon_roomphonetips_roomphonetips_0_e2e":{"desc":"联系电话提示关闭按钮","component_name":"phone_tips_close_btn","operation":["click"]},"acc_i_email_0":{"desc":"邮箱输入框","component_name":"email_input","operation":["get","set"]}}"""
    res = ProcessUtils.getDictDataToElement(str)
    print(res)


# str数据转字典数据
def strExchangeDict():
    val = """# 房间数量减少按钮[click]
    room_count_decrease_btn = ViewId('acc_touchablewithoutfeedback_components_plusminusbtn_0')

    # 房间数量增加按钮[click]
    room_count_increase_btn = ViewId('acc_Link_components_plusminusbtn_1')

    # 住客姓名点击区域[click]
    guest_name_click_area = ViewId('acc_touchableopacity_roomsingle_roomtitle_0_e2e')

    # 住客姓名输入框[get, set]
    guest_name_input = ViewId('acc_i_CnName_0-0-0')

    # 选择入住人按钮[click]
    select_checkin_person_btn = ViewId('htl_c_app_fill_checkinNameSelectLayer_click')

    # 联系电话区号点击区域[click]
    phone_area_code_click_area = ViewId('acc_touchableopacity_roomguestsphone_phonetitle_0_e2e')

    # 联系电话输入框[get, set]
    phone_input = ViewId('acc_i_phone_0')

    # 联系电话右侧图标点击区域[click]
    phone_icon_click_area = ViewId('htl_c_app_fill_contactPhoneLayerEntrace_click')

    # 联系电话提示关闭按钮[click]
    phone_tips_close_btn = ViewId('acc_commonicon_roomphonetips_roomphonetips_0_e2e')

    # 邮箱输入框[get, set]
    email_input = ViewId('acc_i_email_0')

    """

    res = ProcessUtils.getValToDict(val)
    print(str(res))

# 填写存量的数据
val = """{'acc_rainbowtext_roomsingle_roomtitle_0_e2e': {'desc': ' 住客姓名浮层弹框入口icon ', 'component_name': 'guestNameLayerIcon', 'operation': ['click']}, 'acc_text_components_icon_0': {'desc': ' 住客姓名说明浮层关闭ICON  ', 'component_name': 'guestNameLayerCloseIcon', 'operation': ['click']}, 'acc_view_components_labeltextinput_3': {'desc': ' 住客姓名输入框 外层 ', 'component_name': 'guestNameInputBox', 'operation': ['set']}, 'acc_Link_components_plusminusbtn_1': {'desc': ' 房间数增加按钮 ', 'component_name': 'roomCountAdd', 'operation': ['click']}, 'acc_commonicon_roomguestsperson_personpassengerchooser_0_e2e_': {'desc': ' 常旅住客列表入口 ', 'component_name': 'guestListIcon', 'operation': ['click']}, 'acc_hotelicon_roomguestsphone_phoneright_0_e2e': {'desc': ' 通讯录入口ICON ', 'component_name': 'addressBookIcon', 'operation': ['click']}, 'acc_rainbowtext_roomguestsheader_index_0_e2e': {'desc': ' 填写订房信息文案元素id ', 'component_name': 'bookingRoomInfoName', 'operation': ['click']}, ' acc_i_phone_0': {'desc': ' 填写页面手机号输入框 ', 'component_name': 'phoneInput', 'operation': ['set', 'get']}, 'acc_rainbowtext_roomguestsphone_phonetitle_0_e2e': {'desc': ' 填写页面手机号区号列表入口 ', 'component_name': 'phoneAreaCodeInput', 'operation': ['click']}, 'ctrip.android.view:id/country_no': {'desc': ' 手机区号列表，区号list ', 'component_name': 'phoneAreaCodeList', 'operation': ['select']}, 'acc_i_email_0': {'desc': ' 填写页面邮箱输入 ', 'component_name': 'emailInput', 'operation': ['set', 'get']}, 'acc_rainbowtext_roomguestsemail_index_0_e2e': {'desc': ' 填写页面邮件输入控件 ', 'component_name': 'emailInputBox', 'operation': ['exists']}, 'acc_commonicon_components_layerscrollcontent_0_e2e': {'desc': ' 住客姓名浮层关闭按钮 ', 'component_name': 'guestNameLayerCloseBtn', 'operation': ['click']}, 'acc_commonicon_components_labeltextinput_0_e2e': {'desc': ' 姓名栏清空输入按钮 ', 'component_name': 'guestNameClearBtn', 'operation': ['click']}, 'acc_rainbowtext_complexbed_complexbedlessitem_0_e2e': {'desc': ' 床型要求选项内容元素I', 'component_name': 'bedTypeRequireContent', 'operation': ['# 床型要求选项内容元素I']}}"""


# 两个字典数据比对替换其中的值
def dictCompare(orid, target):
    for key in orid.keys():
        # 判断新的控件信息key如果和历史的不一致，替换存量数据的key
        if key != target.keys():
            orid[key] = target[key]
    return orid


if __name__ == "__main__":
    nval = """{"acc_touchablewithoutfeedback_components_plusminusbtn_0":{"desc":"房间数量减少按钮","component_name":"room_count_decrease_btn","operation":["click"]},"acc_Link_components_plusminusbtn_1":{"desc":"房间数量增加按钮","component_name":"room_count_increase_btn","operation":["click"]},"acc_touchableopacity_roomsingle_roomtitle_0_e2e":{"desc":"住客姓名点击区域","component_name":"guest_name_click_area","operation":["click"]},"acc_i_CnName_0-0-0":{"desc":"住客姓名输入框","component_name":"guest_name_input","operation":["get","set"]},"htl_c_app_fill_checkinNameSelectLayer_click":{"desc":"选择入住人按钮","component_name":"select_checkin_person_btn","operation":["click"]},"acc_touchableopacity_roomguestsphone_phonetitle_0_e2e":{"desc":"联系电话区号点击区域","component_name":"phone_area_code_click_area","operation":["click"]},"acc_i_phone_0":{"desc":"联系电话输入框","component_name":"phone_input","operation":["get","set"]},"htl_c_app_fill_contactPhoneLayerEntrace_click":{"desc":"联系电话右侧图标点击区域","component_name":"phone_icon_click_area","operation":["click"]},"acc_commonicon_roomphonetips_roomphonetips_0_e2e":{"desc":"联系电话提示关闭按钮","component_name":"phone_tips_close_btn","operation":["click"]},"acc_i_email_0":{"desc":"邮箱输入框","component_name":"email_input","operation":["get","set"]}}"""
    res = dictCompare(val, nval)
    print('得到的结果\n',res)
