# coding=utf-8
import os

from labuiframe.lib.utils.ChatGptUtil import ChatGptUtil
from labuiframe.lib.utils.ProcessUtils import ProcessUtils


def readFile(filePath):
    with open(filePath, 'r', encoding='utf-8') as f:
        return f.read()


def getElementBody(name='element'):
    dir_path = os.path.abspath(__file__)
    parent_path = os.path.dirname(dir_path)

    return readFile(parent_path + os.sep + name)


def getInvoice():
    elementBody = getElementBody()

    hierarchy = eval(elementBody)

    nhirarchy = ProcessUtils.getHierarchy(hierarchy)
    newList = []
    ProcessUtils.getHierarchyParentByAttr(hierarchy, newList, 'name', 'acc_view_components_inputscrollview_1_e2e')
    action = '发票·服务'
    for item in newList[0]:
        if action in str(item):
            print(action, '\n', item)
            desc = ProcessUtils.get_tree_desc(item, 0)

            print("desc:\n", desc)
            chatRes = ChatGptUtil.baseClickElement("酒店订单填写页面", "发票+特殊要求模块", desc)


def getFilling():
    elementBody = getElementBody('fillin')

    hierarchy = eval(elementBody)

    newList = []
    # ProcessUtils.getHierarchyParentByAttr(hierarchy, newList, 'name', 'acc_view_components_inputscrollview_1_e2e')
    ProcessUtils.getHierarchyParentByAttrNew(hierarchy,newList,  'name', 'acc_view_components_inputscrollview_1_e2e')
    # print("newList\n",newList)
    action = '订房信息'
    for item in newList[0].get('children'):
        if action in str(item):
            print(action, '\n', item)
            desc = ProcessUtils.get_tree_desc(item, 0)
            print("desc:\n", desc)
            chatRes = ChatGptUtil.baseInputClickElement("酒店订单填写页面", "预订信息", desc)

            break


if __name__ == "__main__":
    getFilling()
