# coding=utf-8
from labuiframe.lib.case.android_app import AndroidAppCase, CmtLogger

__author__ = "tingxiao"
# def log_result_detail(result,expected,actual,img,url,message):
#     # TODO invoke api to log result
#
#     return

class Assertions():
    def assertEqual(expectedValue,actualValue):
       if expectedValue==actualValue:
           CmtLogger.logStep("expected="+expectedValue+"actualValue"+actualValue+"两个元素相等!")
       else:
           CmtLogger.logStep("expected="+expectedValue+"actualValue"+actualValue+"两个元素不相等!")




