from enum import Enum


class AnalysisReasonEnum(Enum):
    """
    Enum class for the analysis reason.
    与100022252中qconfig---caseManualStatus.json保持统一，后续和小辉一起将该部分逻辑优化收口
    """
    Default = {"code": 0, "name": "默认"}
    BusinessChange = {"code": 1, "name": "业务变更"}
    CodeBug = {"code": 2, "name": "开发代码BUG"}
    LoginError = {"code": 4, "name": "登录报错"}
    UiCaseError = {"code": 5, "name": "业务/bug确认"}
    UiFrameworkError = {"code": 6, "name": "UI框架问题"}
    MockDataError = {"code": 7, "name": "mock失败"}
    IncrementProblem = {"code": 8, "name": "增量失败"}
    NativePackageProblem = {"code": 9, "name": "自动化包问题，测试包无问题"}
    NetworkError = {"code": 10, "name": "网络问题"}
    AppCrashProblem = {"code": 15, "name": "app闪退"}
    CaseProblem = {"code": 16, "name": "UIcase错误"}
    CaseTimeoutError = {"code": 17, "name": "case超时"}
    TaskTimeoutError = {"code": 18, "name": "任务超时"}
    DownloadPackageError = {"code": 19, "name": "下载包失败"}
    NoMockError = {"code": 20, "name": "未mock接口"}
    LackMockError = {"code": 21, "name": "mock接口不全"}
    MockFailError = {"code": 22, "name": "业务变更mock失败"}

    @classmethod
    def get_value(cls, reason: str):
        for status in cls.__members__.values():
            if status.value["name"] == reason:
                return status.value["code"]
        return 0

