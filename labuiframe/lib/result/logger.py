# -*- coding: utf-8 -*-
import tarfile
import zipfile

from labuiframe.lib.result.AnalysisReasonEnum import AnalysisReasonEnum
from labuiframe.lib.utils.AiAgentGenerate import AiAgentGenerate
from labuiframe.lib.utils.cdata_util import Cdata_Util

__author__ = "tingxiao"

import requests
import json
import operator
from enum import Enum
import threading

from labuiframe.lib.utils.cephSwiftService import CephSwiftService
from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.config.labconfig import Labconfig
from airtest.core.api import *
import shutil
from labuiframe.lib.utils.MySqlConnect import MySqlConnect
from requests_toolbelt.multipart.encoder import MultipartEncoder
from labuiframe.lib.utils.flight_mock_util import FlightMockUtil

class CmtLogger():

    server_url = "http://************:8080"
    # server_url = "http://*************:8080"
    ctest_server_url = "http://mobile.test.ctripcorp.com/openApi/server/v1"
    mpass_server_url = "http://mpaas.client.mobile.ctripcorp.com"
    # ctest_server_url = "http://**********:8800/openApi/server/v1"
    case_result_id = 0
    case_result_local = ""
    case_result_bds = ""
    metaInfoConfig = ""
    # 下面为失败原因
    page_check_message_error = "页面未进入"
    get_crn_message_error = "增量获取失败"
    load_crn_message_error = "增量下载失败"
    app_crash_message_error = "app crash"
    page_message_error = "页面红屏"
    init_web_message_error = "初始化web失败(公共方法调用异常)"
    page_message_error2 = "页面黑屏"
    mock_message_error = "mock失败"
    net_message_error = "网络问题"
    page_jump_message_error = "页面未跳转"
    #下面为打标类型--映射100022252配置-caseManualStatus.json，不要随意变更！！！x`
    ui_frame_fail_type_error = "UI框架问题"
    crn_type_error = "增量失败"
    mock_type_error = "mock失败"
    mock_type_incompelete_error = "mock接口不全"
    mock_type_unmock_error = "未mock接口"
    mock_type_business_change_error = "业务变更mock失败"
    ui_case_fail_type_error = "业务/bug确认"
    UIcase_error_type_error = "UIcase错误"
    code_bug_fail_type_error = "开发代码bug"
    app_crash_fail_type_error = "app闪退"
    net_fail_type_error = "网络问题"
    case_overtime_error = "case超时"
    Backend_service_error ="后端服务问题"
    Frontend_service_error = "前端服务问题"
    data_bds_error="数据问题-bds/查服"
    APP_error="APP问题"
    Pay_fail_error="支付问题"
    socket_overtime_error="socket超时"
    

    @classmethod
    def getServiceUrlByRunId(cls):
        #server_url已无用，该部分无意义
        # if cls.isLab():
        #     try:
        #         runId = os.environ['runId']
        #         url = cls.server_url + "/api/cap/getServiceUrlByRunId/" + runId
        #         result = requests.get(url)
        #         print(result)
        #         return result.text
        #     except Exception as e:
        #         print("getServiceUrlByRunId fail:", e)
        return ""

    @classmethod
    def writeToDatabase(self, category, expected, actual, message):
        if self.isLab():
            if Labconfig.get_ctaskid() == 0:
                url = self.server_url+"/api/cap/casestep/record"
                return
                #return逻辑为2022/10/21日新增，原因：server_url已经无用，该逻辑无效，因此直接return
            else:
                return
                url = self.ctest_server_url+"/caseresult/record"
            caseStep = {}
            caseStep['caseResultId'] = int(self.case_result_id)  # no solution to get caseResult id yet
            caseStep['expValue'] = expected
            caseStep['actValue'] = actual
            caseStep['descValue'] = message

            result = 1
            stepType = 52
            if category == LogCategory.assert_error:
                result = 0
                stepType = CaseStepType.assertion.value
            elif category == LogCategory.capture:
                result = 1
                stepType = CaseStepType.capture.value
            elif category == LogCategory.error:
                result = 0
                stepType = CaseStepType.exception.value
            elif category == LogCategory.assert_pass:
                result = 1
                stepType = CaseStepType.assertion.value
            elif category == LogCategory.video:
                result = 1
                stepType = CaseStepType.video.value
            else:
                result = 1
                stepType == CaseStepType.log.value

            caseStep['result'] = result
            caseStep['caseStepTypeId'] = stepType
            # print("***********caseStep:"+str(caseStep))
            headers = {'content-type': 'application/json;charset=utf8'}
            print("[{}]*****start caseresult record*****".format(time.strftime("%H:%M:%S"), time.localtime(time.time())))
            print("request url : " + url)
            print("request data : " + json.dumps(caseStep))
            try:
                resultInfo = requests.post(url, data=json.dumps(caseStep),headers=headers)
                print("[{}]*****caseresult record finished*****".format(time.strftime("%H:%M:%S"),
                                                                     time.localtime(time.time())))
            except Exception as e:
                print("忽略http://mobile.test.ctripcorp.com/openApi/server/v1/caseresult/record异常")

    # # 上传case结果到ctest平台
    # @classmethod
    # def writeBackCtestCaseResult(self, cResult, start_time, end_time, message, firewall_log_url=""):
    #     excute_time = int((end_time - start_time) / 1000)
    #     url = self.ctest_server_url + "/case/report"
    #     caseResult = {}
    #     caseResult["taskId"] = Labconfig.get_ctaskid()
    #     caseResult["caseId"] = self.case_result_id
    #     caseResult["excuteTime"] = excute_time
    #     caseResult["startTime"] = start_time
    #     caseResult["endTime"] = end_time
    #     caseResult["securityUrl"] = firewall_log_url
    #     # caseResult["udid"] = DeviceInfo.getModel() + "##" + DeviceInfo.getDeviceSerialNo()
    #     if DeviceInfo.getPlatform().lower() == 'android':
    #         caseResult["udid"] = "" + "##" + DeviceInfo.getDevice().serialno
    #     elif DeviceInfo.getPlatform().lower() == 'ios':
    #         print("ios udid获取")
    #         caseResult["udid"] = "" + "##" + DeviceInfo.getDeviceName()
    #     # print("device serialno : " + DeviceInfo.getDevice().serialno)
    #     result = 0
    #     if len(cResult.detail_errors) > 0:
    #         result = CtestCaseResultType.failure.value
    #         cResult.detail_errors = []  # 清空每个case detail_error
    #     else:
    #         result = CtestCaseResultType.success.value
    #     caseResult["status"] = result
    #     caseResult["message"] = message
    #     caseResult["custom_config"] = self.getCustomMessage(self)
    #     headers = {'content-type': 'application/json;charset=utf8'}
    #     print(
    #         "[{}]*****start caseresult finish*****".format(time.strftime("%H:%M:%S"), time.localtime(time.time())))
    #     print("request url : " + url)
    #     print("request data : " + json.dumps(caseResult))
    #     resultInfo = requests.post(url, data=json.dumps(caseResult), headers=headers)
    #     print("[{}]*****caseresult finish finished*****".format(time.strftime("%H:%M:%S"),
    #                                                             time.localtime(time.time())))

    # 上传case结果到ctest平台--自动打标
    @classmethod
    def writeBackCtestCaseResultNew(self, ctaskId,cCaseId,cResult, start_time, end_time, message, firewall_log_url = "",failType="", appId="99999999",
                                 cid="", mockId="",customMessage ="", localMockStatus=False, isFlutterPackage=True):
        caseResult = {}
        if len(cResult.detail_errors) > 0:
            result = CtestCaseResultType.failure.value
            mpass_result = MpassCaseResultType.failure.value
            cResult.detail_errors = []  # 清空每个case detail_error
        else:
            result = CtestCaseResultType.success.value
            mpass_result = MpassCaseResultType.success.value
        # 新增判断message是否为页面未进入，如果是则mock问题
        if (message == self.page_check_message_error):
            if mockId != "":
                mockDetailInfo, sucMockServiceInfo = self.getMockInfo(appId, cCaseId, start_time, end_time, cid, mockId, localMockStatus)
                if len(mockDetailInfo) == 0:
                    message = self.mock_message_error
                    failType = self.mock_type_unmock_error
                elif len(sucMockServiceInfo) == 0:
                    message = self.mock_message_error
                    failType = self.mock_type_business_change_error
                elif (len(mockDetailInfo) > len(sucMockServiceInfo)):
                    message = self.mock_message_error
                    failType = self.mock_type_incompelete_error
                else:
                    print("mockId不为空，mock成功，归为uiCase问题！")
                    failType = self.ui_case_fail_type_error
            else:
                print("不是增量问题，不是mock问题，归为uiCase问题！")
                failType = self.ui_case_fail_type_error
        # 仅在发生错误信息时，再进行错误上报原因更正
        if message in [self.page_check_message_error, self.get_crn_message_error, self.load_crn_message_error, self.app_crash_message_error, self.page_message_error,
                       self.page_message_error2, self.mock_message_error, self.net_message_error, self.page_jump_message_error]:
            # ai debug生成场景，同时上报错误信息至灵驹平台--仅在页面未进入场景
            self.update_ai_debug_fail_info(message, failType)
        caseResult["startTime"] = start_time
        caseResult["endTime"] = end_time
        headers = {'content-type': 'application/json;charset=utf8'}
        if Labconfig.get_is_mpass():
            url = self.mpass_server_url + "/mpaas/platform/openapi/task/case/record"
            caseResult["caseRecordId"] = cCaseId
            caseResult["caseRecordStatus"] = mpass_result
            caseResult["startTime"] = start_time
            caseResult["endTime"] = end_time
            caseResult["message"] = message
            caseResult["analysisReason"] = AnalysisReasonEnum.get_value(failType)
            print("failType---:{},analysisReason---:{}".format(failType,AnalysisReasonEnum.get_value(failType)))
            print("[{}]*****start mpass caseresult finish*****".format(time.strftime("%H:%M:%S"), time.localtime(time.time())))
            print("request url : " + url)
            print("request data : " + json.dumps(caseResult))
            resultInfo = requests.post(url, data=json.dumps(caseResult), headers=headers)
            print("[{}]*****caseResult report response :{}*****".format(cCaseId, resultInfo.text))
            if resultInfo.status_code != 200:
                # 等待10s进行重试
                time.sleep(10)
                resultInfo = requests.post(url, data=json.dumps(caseResult), headers=headers)
                print("[{}]*****rerun caseResult report response :{}*****".format(cCaseId, resultInfo.text))
            print("[{}]*****mpass caseresult finish finished*****".format(time.strftime("%H:%M:%S"),
                                                                    time.localtime(time.time())))
            return

        excute_time = int((end_time - start_time) / 1000)
        url = self.ctest_server_url + "/case/report"
        caseResult["taskId"] = ctaskId
        caseResult["caseId"] = cCaseId
        caseResult["timeToFistWakeUp"] = Labconfig.getTimeToFistWakeUp()
        caseResult["excuteTime"] = excute_time
        caseResult["timeSplitToFramework"] = Labconfig.getTimeSplitToFramework()
        caseResult["timeSplitToHotelPage"] = Labconfig.getTimeSplitToHotelPage()
        caseResult["timeSplitToLoadPage"] = Labconfig.getTimeSplitToLoadPage()
        caseResult["timeSplitToCase"] = Labconfig.getTimeSplitToCase()
        caseResult["securityUrl"] = firewall_log_url
        # caseResult["udid"] = DeviceInfo.getModel() + "##" + DeviceInfo.getDeviceSerialNo()
        if DeviceInfo.getPlatform().lower() == 'android':
            caseResult["udid"] = "" + "##" + DeviceInfo.getDevice().serialno
        elif DeviceInfo.getPlatform().lower() == 'ios':
            print("ios udid获取")
            caseResult["udid"] = "" + "##" + DeviceInfo.getDeviceName()
        # print("device serialno : " + DeviceInfo.getDevice().serialno)
        caseResult["status"] = result
        caseResult["message"] = message
        caseResult["failType"] = failType
        caseResult["custom_config"] = customMessage
        print("[{}]*****start caseresult finish*****".format(time.strftime("%H:%M:%S"), time.localtime(time.time())))
        print("request url : " + url)
        print("request data : " + json.dumps(caseResult))
        resultInfo = requests.post(url, data=json.dumps(caseResult), headers=headers)
        print("[{}]*****caseresult finish finished*****".format(time.strftime("%H:%M:%S"),
                                                                time.localtime(time.time())))


    # 获取client 和 启动参数
    def getCustomMessage(self):
        return  "启动参数:" + self.metaInfoConfig

    @classmethod
    def getMockInfo(cls,appId, cCaseId, start_time, end_time, cid, mockId, localMockStatus):
        mockDetailInfo = FlightMockUtil.getFlightMockCaseInfoList(mockId)
        if len(mockDetailInfo) == 0:
            return mockDetailInfo,[]
        sucMockServiceInfo = FlightMockUtil.getMockSuiteLog(start_time,end_time,cid,mockId)
        if not localMockStatus:
            return mockDetailInfo,sucMockServiceInfo
        # mockDetailInfo, sucMockServiceInfo = FlightMockUtil.getFlightMockInfo(start_time, end_time, cid, mockId)
        localMockServiceName = []
        if localMockStatus and len(mockDetailInfo) > 0:
            ## 获取读取本地mock结果
            res = Cdata_Util.getLocalMockRes(appId, cid, start_time, end_time)
            for item in mockDetailInfo:
                mockIdAndServiceId = mockId + "-" + item["serviceName"]
                if mockIdAndServiceId in res:
                    ## 本地mock成功
                    localMockServiceName.append(item["serviceName"])
                    if item not in sucMockServiceInfo:
                        sucMockServiceInfo.append(item)
            if len(localMockServiceName) > 0:
               print(str(cCaseId) + "本地Mock成功服务**" + str(localMockServiceName) + "**")
        return mockDetailInfo,sucMockServiceInfo








    @classmethod
    def writeBackCaseResult(self,cResult, start_time=0, end_time=0,message="", firewall_log_url = "", failType="",clientId="",mockId=""):
        if self.isLab():
            if Labconfig.get_ctaskid() == 0:
                return
                #server_url无效，直接return
                url = self.server_url + "/api/cap/caseresult/finish"
                caseResult = {}
                caseResult['id'] = int(self.case_result_id)
                caseResult['cause'] = ""  # message i think
                result = CaseResultType.processing.value
                if len(cResult.detail_errors) > 0:
                    # CmtLogger.logStep("*************fail")
                    result = CaseResultType.fail.value
                    detail_error = cResult.detail_errors[0][2]
                    caseResult['cause'] = str(detail_error)
                    self.writeToDatabase(LogCategory.error, "", "", str(detail_error))
                    cResult.detail_errors = []  # 清空每个case detail_error
                else:
                    # CmtLogger.logStep("*************pass")
                    result = CaseResultType.epass.value
                caseResult['caseResultTypeId'] = result
                CmtLogger.logStep("result" + str(json.dumps(caseResult)))

                headers = {'content-type': 'application/json;charset=utf8'}
                print("[{}]*****start caseresult finish*****".format(time.strftime("%H:%M:%S"),
                                                                     time.localtime(time.time())))
                print("request url : " + url)
                print("request data : " + json.dumps(caseResult))
                resultInfo = requests.post(url, data=json.dumps(caseResult), headers=headers)
                print("[{}]*****caseresult finish finished*****".format(time.strftime("%H:%M:%S"),
                                                                        time.localtime(time.time())))
            else:
                # self.writeBackCtestCaseResult(cResult, start_time, end_time, message, firewall_log_url)
                ctaskId = Labconfig.get_ctaskid()
                cCaseId = self.case_result_id
                customMessage = self.getCustomMessage(self)
                localMockStatus = Labconfig.getLocalMockStatus()
                isFlutterPackage = Labconfig.getIsFlutterPackage()

                #self.writeBackCtestCaseResultNew(ctaskId,cCaseId,cResult, start_time, end_time, message, firewall_log_url,failType,
                #                                                                 Labconfig.getAppid(),clientId,crnChannelCode,needLoadCrnChannelCode,failLoadCrnChannelCode,mockId,customMessage,localMockStatus, isFlutterPackage)
                t = threading.Thread(target=self.writeBackCtestCaseResultNew, args=(ctaskId,cCaseId,cResult, start_time, end_time, message, firewall_log_url,failType,
                                                                               Labconfig.getAppid(),clientId,mockId,customMessage,localMockStatus, isFlutterPackage))
                t.setDaemon(True)
                t.start()


    @classmethod
    def update_ai_debug_fail_info(cls,msg, fail_type):
        # ai debug调试场景同步将错误原因写入灵驹
        if Labconfig.get_ai_agent_debug() and Labconfig.get_trace_log_id() is not None and len(Labconfig.get_trace_log_id()) > 0:
            # 仅入库指定几类原因
            if fail_type in [cls.app_crash_fail_type_error,cls.code_bug_fail_type_error]:
                #客户端问题 errType=6
                AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 6, "闪退/红屏")
                return
            if fail_type in [cls.net_fail_type_error]:
                AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 7, "网络问题")
                return
            if fail_type in [cls.ui_frame_fail_type_error]:
                AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 8, "UI框架问题")
                return
            elif fail_type in [cls.mock_type_error,cls.mock_type_business_change_error,cls.mock_type_incompelete_error,cls.mock_type_unmock_error]:
                AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 9, "mock问题")
                return
            # elif fail_type in [cls.crn_type_error]:
            #     AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 10, "增量失败")
            else:
                AiAgentGenerate.updateAiCodeDetail(Labconfig.get_trace_log_id(), Labconfig.get_case_platform(), msg, 2, fail_type)
                return



    @classmethod
    def getClassName(cls,class_name):
        list = class_name.split('.')
        if len(list) >0:
            return list[len(list)-1]
        return ''

    @classmethod
    def getPackageName(cls,class_name):
        index = class_name.rindex('.')
        return class_name[0:index]

    @classmethod
    def addAllCaseInfo(self, pocoTestSuite):
        if self.isLab() and Labconfig.get_ctaskid() == 0:
            return
            #server_url无效，直接return
            # getTask
            taskId = 0
            ctaskId = 0
            udid = ""
            caseInfoList = []
            url = self.server_url + "/api/cap/caseresult/batchStart"

            try:
                taskId = os.environ['taskId']
            except Exception as e:
                print("taskId Exception:", e)
                ctaskId = Labconfig.get_ctaskid()
                print("Set CtaskId:" + str(ctaskId))

            for item in pocoTestSuite:
                caseName = item.getMetaInfo()['casename']
                caseInfo = {}
                caseInfo['taskId'] = taskId  # 获得jobID
                caseInfo['ctaskId'] = ctaskId
                caseInfo['udid'] = udid

                casePackageName = item.test_case_dir
                projectRoot = item.project_root
                casePackageName = casePackageName.replace(projectRoot + os.sep,"")
                casePackageName = casePackageName.replace(os.sep,".")

                caseClassName = item.testcase_name
                caseInfo['casePackageName'] = casePackageName
                caseInfo['caseClassName'] = caseClassName
                caseInfo['name'] = caseName  # caseName
                caseInfoList.append(caseInfo)

            headers = {'content-type': 'application/json;charset=utf8'}
            dataInfo = requests.post(url, data=json.dumps(caseInfoList), headers=headers)

    @classmethod
    def getCtestCaseId(cls, metaInfo, class_name = ""):
        test_case_list_path = os.path.dirname(Labconfig.get_config_file_path()) + os.sep + "uiAutoTest.json"
        with open(test_case_list_path, 'r',encoding='utf-8') as f:
            case_info = f.read()
            case_list = json.loads(case_info)
            for case in case_list:
                local = case.get("local", "")
                bds = case.get("bds", "")
                if local == "" and bds=="" and cls.getPackageName(class_name) == case["packagename"] and cls.getClassName(class_name) == case["classname"]:
                    return (case["id"], "","")
                elif local != "" and cls.getPackageName(class_name) == (case["packagename"] + "." + case["local"]) and cls.getClassName(class_name) == case["classname"]:
                    return (case["id"], case["local"],"")
                elif bds !="" and cls.getPackageName(class_name) == (case["packagename"] + "." + case["bds"].replace("|","-")) and cls.getClassName(class_name) == case["classname"]:
                    return (case["id"],"", case["bds"])
        return (0, "","")

    @classmethod
    def createMetaConfig(cls,metaInfo):
        cls.metaInfoConfig = metaInfo.get('config',"")

    @classmethod
    def createCaseInfo(self,metaInfo,class_name=""):
        if self.isLab():
            # getTask
            caseName = metaInfo['casename']
            taskId = 0
            ctaskId = 0
            try:
                taskId = os.environ['taskId']
            except Exception as e:
                print("taskId Exception:", e)
                ctaskId = Labconfig.get_ctaskid()
                print("Set CtaskId:" + str(ctaskId))

            if Labconfig.get_ctaskid() == 0:
                #server_url无效，直接return
                return
                url = self.server_url + "/api/cap/caseresult/startCase"
                caseInfo = {}
                caseInfo['taskId'] = taskId  # 获得jobID
                caseInfo['ctaskId'] = ctaskId
                caseInfo['casePackageName'] = self.getPackageName(class_name)
                caseInfo['caseClassName'] = self.getClassName(class_name)
                platform = DeviceInfo.getPlatform()
                if platform.lower() == "android":
                    # caseInfo['udid'] = DeviceInfo.getModel() + "##" + DeviceInfo.getDeviceSerialNo()  # model##serialno
                    caseInfo['udid'] = "" + "##" + DeviceInfo.getDeviceSerialNo()
                elif platform.lower() == "ios":
                    caseInfo['udid'] = DeviceInfo.getIOSProductType() + "##" + DeviceInfo.getIOSudid()
                caseInfo['name'] = caseName  # caseName

                headers = {'content-type': 'application/json;charset=utf8'}
                print("[{}]*****call caseresult start api*****".format(
                    time.strftime("%H:%M:%S", time.localtime(time.time()))))
                print("request url: " + url)
                print("request data: " + json.dumps(caseInfo, ensure_ascii=False))
                dataInfo = requests.post(url, data=json.dumps(caseInfo), headers=headers)
                print("case result id is : " + dataInfo.text)
                self.case_result_id = dataInfo.text
            else:
                case_result = self.getCtestCaseId(metaInfo, class_name)
                self.case_result_id = case_result[0]
                print("caseId is " + str(self.case_result_id))
                self.case_result_local = ""
                self.case_result_bds = ""
                if case_result[1]:
                    self.case_result_local = case_result[1]
                    print("local is " + str(self.case_result_local))
                elif case_result[2]:
                    self.case_result_bds = case_result[2]
                    print("bdsdata is " + str(self.case_result_bds))



    @classmethod
    def isLab(self):
        #如果ctaskId > 0，为ctest平台触发，返回True
        try:
            ctaskId = Labconfig.get_ctaskid()
        except Exception as e:
            ctaskId = 0

        try:
            run_id = os.environ['runId']
        except Exception:
            run_id = 0

        if int(ctaskId) > 0 or run_id > 0:
             return True

        ####当前已经没有cap/cap.properties文件，该逻辑已无效，注释###
        # elif Labconfig.getIsLogLab() == "True":
        #     user_path = '{}{}{}'.format(os.path.expanduser('~'),os.sep,'.cap')
        #     config_file = '{}{}{}'.format(user_path,os.sep,'cap.properties')
        #     try:
        #         with open(config_file,'r') as f:
        #             runmode = f.readline()
        #             if operator.eq('lab',runmode.split('=')[1].replace("\n","")):
        #                 return True
        #     except Exception as e:
        #         return False
        #     return False
        else:
            return False

    @classmethod
    def upload_safeinfo_log(cls, log_type, log_path):
        if cls.isLab() and log_path is not None:
            print("[{}]*****start to upload mthemis_log report*****".format(time.strftime("%H:%M:%S", time.localtime(time.time()))))
            url = "http://privacy-firewall-log-collector.infosec.ctripcorp.com/api/collect/log"
            print("***upload {} Url:{}".format(log_type, url))
            file = open(log_path, "rb")
            multipart_encoder = MultipartEncoder(
                fields={
                    "logType": log_type,
                    "file": (os.path.basename(log_path), file),
                    "jobId": Labconfig.get_ctaskid(),
                    "caseId": str(cls.case_result_id)
                },
                boundary='----------%s' % hex(int(time.time() * 1000))
            )
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": multipart_encoder.content_type
            }
            response = requests.post(url, data=multipart_encoder, headers=headers)
            print("[{}]***{} result (caseId：{}): {}".format(time.strftime("%H:%M:%S", time.localtime(time.time())), log_type, cls.case_result_id,
                                                            response.text))
            file.close()
            try:
                resp = json.loads(response.text)
                if resp["message"] == 'Success':
                    return resp["result"]
                else:
                    return None
            except Exception:
                return None
            finally:
                if os.path.exists(log_path):
                    os.remove(log_path)

    @classmethod
    def uploadCaseVideo(self,screen_record):
        if self.isLab() and DeviceInfo.getPlatform().lower() == "android":
            if int(Labconfig.get_ctaskid()) > 0:
                return
            print("********Upload Vedio**************")
            print("******device:"+DeviceInfo.getDevice().serialno)
            if DeviceInfo.getDevice().serialno != '':
                file_path = screen_record.record_filepaths[DeviceInfo.getDevice().serialno]
            else:
                file_path = screen_record.record_filepaths[device().uuid]
            print("********Video:"+file_path)
            file_name = os.path.split(file_path)[1]
            file_names = os.path.splitext(file_name)
            name = file_names[0]
            t = time.time()
            file_name = name +"-" + str(int(t)) + ".mp4"
            print("****"+file_name+":**********filename:"+ file_name)

            ceph_client = CephSwiftService()
            ceph_client.uploadFile(file_path,file_name)

            self.writeToDatabase(LogCategory.video,"","",file_name)

    def make_targz(self,output_filename, source_dir):
        with tarfile.open(output_filename, "w:gz") as tar:
            tar.add(source_dir, arcname=os.path.basename(source_dir))

    def zip_dir(self,dir_path, zip_path):
        '''

        :param dir_path: 目标文件夹路径
        :param zip_path: 压缩后的文件夹路径
        :return:
        '''
        zip = zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED)
        for root, dirnames, filenames in os.walk(dir_path):
            file_path = root.replace(dir_path, '')  # 去掉根路径，只对目标文件夹下的文件及文件夹进行压缩
            # 循环出一个个文件名
            for filename in filenames:
                zip.write(os.path.join(root, filename), os.path.join(file_path, filename))
        zip.close()

    @classmethod
    def uploadWebReport(cls, path, case_result_id):
        """
        上传报告至mpaas
        """
        if cls.isLab():
            print("[{}][{}]*****uploadReport上传报告至mpaas*****".format(
                time.strftime("%H:%M:%S", time.localtime(time.time())), case_result_id))
            # 将path打包
            # report_file = path + "/report.zip"
            # cls.zip_dir(cls, path, report_file)
            try:
                report_file = shutil.make_archive(path, 'zip', path)
            except Exception as e:
                print("********打包报告时发生异常**************")
                print("异常信息：{}".format(str(e)))
            print("[{}][{}]***report dir: ".format(time.strftime("%H:%M:%S", time.localtime(time.time())), case_result_id) + report_file)
            multipart_encoder = MultipartEncoder(
                fields={
                    "file": (os.path.basename(report_file), open(report_file, "rb"))
                },
                boundary='----------%s' % hex(int(time.time() * 1000))
            )
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": multipart_encoder.content_type
            }
            url = cls.mpass_server_url + "/mpaas/platform/openapi/uploadMutiFile/" + str(case_result_id)
            response = requests.post(url, data=multipart_encoder, headers=headers)
            print("[{}]***UploadReport Response: {}".format(time.strftime("%H:%M:%S", time.localtime(time.time())),
                                                            response.text))
            try:
                if os.path.exists(path):
                    shutil.rmtree(path)
            except Exception:
                pass

    @classmethod
    def uplodeReport(cls, path):
        if cls.isLab():
            print("[{}]*****uploadReport上传报告至ctest*****".format(time.strftime("%H:%M:%S", time.localtime(time.time()))))
            file_name = "report.zip"
            file_path = path + os.sep + file_name
            log_path = path + os.sep + "export/log.log"
            if os.path.exists(file_path):
                os.remove(file_path)

            cls.zip_dir(cls, log_path,file_path)
            print("***log dir: " + log_path)
            multipart_encoder = MultipartEncoder(
                fields={
                    "file": (os.path.basename(file_path), open(file_path, "rb"))
                },
                boundary='----------%s' % hex(int(time.time() * 1000))
            )
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": multipart_encoder.content_type
            }
            if Labconfig.get_is_mpass():
                url = cls.mpass_server_url + "/mpaas/platform/openapi/uploadMutiFile/" + str(cls.case_result_id)
            else:
                url = "http://mobile.test.ctripcorp.com/openApi/testFiles/uploadMutiFile/" + str(Labconfig.get_ctaskid()) + "/" + str(cls.case_result_id) + ".json"
            print("***uploadReport Url: " + url)
            response = requests.post(url, data=multipart_encoder, headers=headers)
            print("[{}]***UploadReport Response: {}".format(time.strftime("%H:%M:%S", time.localtime(time.time())),
                                                            response.text))
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                if os.path.exists(path):
                    shutil.rmtree(path)
            except Exception:
                pass

    @classmethod
    def logImage(self,pic_path):
        if self.isLab() or Labconfig().is_watcher_debug() == "True":
            file_name = os.path.split(pic_path)[1]
            t = time.time()
            file_name =  str(int(t))+ "_" + file_name
            # print("file_name:" + file_name)
            ceph_client = CephSwiftService()
            ceph_client.uploadFile(pic_path, file_name)

            self.writeToDatabase(LogCategory.capture,"","",file_name)
            return file_name

    # log step日志
    @classmethod
    def logStep(self,detail):
        # os.system('echo ' + str(detail))
        print(str(detail))
        log(str(detail))
        if self.isLab():
            self.writeToDatabase(LogCategory.step, "", "", str(detail))

    @classmethod
    def setCoverage(self):
        if Labconfig.is_coverage():
            print(" ")
            # keyevent('BACK')
            # sleep(3)

    @classmethod
    def uplodeCoverage(cls):
        # print("Test Class End ----->")
        # print("tearDownClass")
        # print(Labconfig.is_coverage())
        if Labconfig.is_coverage():
            # print("----coverage----")
            base = os.getcwd()
            report_dir = "{}{}{}".format(base, os.sep, "report_data")
            if not os.path.exists(report_dir):
                os.system("mkdir "+report_dir)
            try:
                pullfile = 'pull /sdcard/coverage.ec ' + report_dir
                adb = device().adb
                adb.pull("/sdcard/coverage.ec",report_dir)
            except Exception as e:
                print("Pull Coverage fail:", e)

            coverage_file = '{}{}{}'.format(report_dir, os.sep, 'coverage.ec')
            print(Labconfig.getPackage())
            names = Labconfig.getPackage().split("/")
            print(names[-1])
            print(coverage_file)
            print(os.path.exists(coverage_file))
            runId = 0
            try:
                runId = os.environ['runId']
            except Exception as e:
                print("Clear Coverage File fail:", e)
            if os.path.exists(coverage_file):
                # print("----exists coverage----")
                datas = {'runid': runId,
                         "version": names[-1],
                         "type": 2}
                files = {'file': open(coverage_file, "rb")}
                url = "http://************:8080/api/cap/addCoverage"
                # url = "http://10.32.150.33:8080/api/cap/addCoverage"
                try:
                    r = requests.post(url, data=datas, files=files)
                    # print("**********Response:" + str(r.text))
                    shell(['rm', '/sdcard/coverage.ec'])
                    # shell('rm /sdcard/coverage.ec')
                except Exception as e:
                    print("Clear Coverage File fail:", e)
                os.system("sudo rm -rf "+coverage_file)

    @classmethod
    def uploadWatcherUrl(self, run_id, suite_name):
        request_url = "http://mobile.test.ctripcorp.com/openApi/testFiles/watcher/" + str(run_id)
        watcher_url = "http://watcher.fat2.qa.nt.ctripcorp.com/home#/test-result?runId={}&isSeeMe=false&isTile=false&suiteName={}&type=all".format(str(run_id), suite_name)
        headers = {'content-type': 'application/json'}
        data = {}
        data["watcherUrl"] = watcher_url
        print("*****call uploadWatcherUrl api*****")
        print("request url: " + request_url)
        print("request data: " + json.dumps(data))
        dataInfo = requests.post(request_url, data=json.dumps(data), headers=headers)
        print("uploadWatcherUrl response:" + dataInfo.text)

    @classmethod
    def insertCaseDetail(self, scene_name, hotel_id, room_id, order_id, status, remark):
        mysqlConnect = MySqlConnect()
        runby = ""
        if self.isLab():
            caseresult_id = int(self.case_result_id)
            runby = 'ATL'
            if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
                runby = 'CTest'
        else:
            caseresult_id = 0
        mysqlConnect.insertCaseDetail(caseresult_id, scene_name, hotel_id, room_id, order_id, status, remark, runby)

    @classmethod
    def getcase_result_bds(cls):
        return cls.case_result_bds

class LogCategory(Enum):
    epass = 0
    info = 0
    step = 0
    remark = 0
    file = 0
    error = 1
    assert_error = 2
    assert_pass = 3
    capture = 4
    video = 5


class CaseResultType(Enum):
    processing = 0
    epass = 401
    fail = 402
    skip = 403

class CtestCaseResultType(Enum):
    success = 3
    failure = 4

class MpassCaseResultType(Enum):
    success = 0
    failure = 1
    timeout = 2
    cancel = 3
    abort = 4
    unknown = -1

class CaseStepType(Enum):
    assertion = 51
    log = 52
    capture = 53
    exception = 54
    video = 55



