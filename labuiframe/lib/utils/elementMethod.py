import json
from labuiframe.lib.utils.ElementItem import ElementItem

class elementMethod():

    @classmethod
    def load_page_elements(self,testbasecase):
        if any(base.__name__ == 'WebBaseCase' for base in testbasecase.testCase.__class__.__bases__):
            tree = testbasecase.get_element_tree(testbasecase)
            testbasecase.page_elements = json.dumps([tree], default=ElementItem.custom_serializer)
            return
        trees = []

        first_tree = testbasecase.get_element_tree()
        trees.append(first_tree)
        if first_tree is None:
            return
        end = first_tree.is_end()
        down_count = 0
        last_hash = 0

        while not end:
            testbasecase.testCase.scroll_to_down()
            # print("下拉第{}次".format(down_count + 1))
            tree = testbasecase.get_element_tree()
            trees.append(tree)
            end = tree.is_end()
            current_hash = hash(tree)
            if current_hash == last_hash:
                break
            last_hash = current_hash
            down_count += 1
            if down_count > 10:
                end = True

        # print("下拉{}次".format(down_count))

        for i in range(down_count - 1):
            testbasecase.testCase.poco.scroll("vertical", percent=-0.5, duration=0.5)
            # print("上拉第{}次".format(i + 1))
        if down_count > 1:
            for i in range(10):
                testbasecase.testCase.poco.scroll("vertical", percent=-0.05, duration=0.1)

        testbasecase.page_elements = json.dumps(trees, default=ElementItem.custom_serializer)

    @classmethod
    def print_page_elements(self,testbasecase):
        if any(base.__name__ == 'WebBaseCase' for base in testbasecase.testCase.__class__.__bases__):
            tree = testbasecase.get_element_tree()
            testbasecase.page_elements = json.dumps([tree], default=ElementItem.custom_serializer)
            print(testbasecase.page_elements)
            return
        if testbasecase.page_elements is None:
            elementMethod.load_page_elements(testbasecase)
        print(testbasecase.page_elements)
