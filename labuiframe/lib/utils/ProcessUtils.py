from labuiframe import CommonAction


class ProcessUtils:

    # 获取所有的叶子节点
    @classmethod
    def getHierarchy(cls, hierarchy, payloadList):
        # 有children的节点，将payload信息存入payloadList
        if isinstance(hierarchy, dict):
            json = cls.getDictData(cls, hierarchy)

            if hierarchy.get('children') is not None:
                cls.getHierarchy(hierarchy.get('children'), payloadList)
            else:
                if json is not None:
                    payloadList.append(json)

        elif isinstance(hierarchy, list):
            for i in hierarchy:
                json = cls.getDictData(cls, i)

                if i.get('children') is not None:
                    cls.getHierarchy(i.get('children'), payloadList)
                else:
                    if json is not None:
                        payloadList.append(json)

    # 获取字典中指定key的数据
    @classmethod
    def findTargetElement(cls, hierarchy, targetElementName):
        if isinstance(hierarchy, dict):
            payload = hierarchy.get('payload')
            if payload.get('name') == targetElementName:
                return hierarchy
            if hierarchy.get('children') is not None:
                return cls.findTargetElement(hierarchy.get('children'), targetElementName)

        elif isinstance(hierarchy, list):
            if hierarchy is None or len(hierarchy) == 0:
                return None
            for i in hierarchy:
                payload = i.get('payload')
                if payload.get('name') == targetElementName:
                    return i
                if i.get('children') is not None:
                    find = cls.findTargetElement(i.get('children'), targetElementName)
                    if find is not None:
                        return find
            return None

    @classmethod
    def get_tree_desc(cls, tree: dict, index: int) -> str:
        name = tree['payload'].get('name', 'None')
        text = tree['payload'].get('text', 'TextNone')
        touchable = "TouchDisabled"
        editable = "EditableDisabled"
        if tree['payload'].get('touchable', False) is True:
            touchable = "TouchEnabled"
        if tree['payload'].get('editalbe', False) is True:
            editable = "EditableEnabled"
        if 'android.view.ViewGroup' in tree['payload'].get('name', 'None') and text == 'TextNone':
            output = ""
        else:
            output = ("\t" * index) + f"{name}:{text}:{touchable}:{editable}\n"
        if 'children' in tree:
            for child in tree['children']:
                output += cls.get_tree_desc(child, index + 1)
        return output

    # 获取叶子节点中属性值为指定值的父节点
    @classmethod
    def getHierarchyParentByAttr(cls, hierarchy, payloadList, attr, value):
        # 有children的节点，将payload信息存入payloadList

        if isinstance(hierarchy, dict):
            res = cls.getChildByAttr(cls, hierarchy, attr, value)
            if res is None:
                if hierarchy.get('children') is not None:
                    cls.getHierarchyParentByAttr(hierarchy.get('children'), payloadList, attr, value)
            else:

                payloadList.append(hierarchy)
                return hierarchy

        elif isinstance(hierarchy, list):
            for i in hierarchy:
                res = cls.getChildByAttr(cls, i, attr, value)
                if res is None:
                    if i.get('children') is not None:
                        cls.getHierarchyParentByAttr(i.get('children'), payloadList, attr, value)
                else:
                    if i.get('children') is not None:
                        payloadList.append(i.get('children'))
                    else:

                        payloadList.append(i)

    @classmethod
    def getHierarchyParentByAttrNew(cls, hierarchy,payloadList, attr, value):

        if isinstance(hierarchy, dict):
            res = cls.getChildByAttr(cls, hierarchy, attr, value)
            if res is None:
                if hierarchy.get('children') is not None:
                    cls.getHierarchyParentByAttrNew(hierarchy.get('children'), payloadList, attr, value)
            else:
                payloadList.append(cls.simplyHierary(cls, hierarchy))
        elif isinstance(hierarchy, list):
            for i in hierarchy:
                res = cls.getChildByAttr(cls, i, attr, value)
                if res is None:
                    if i.get('children') is not None:
                        cls.getHierarchyParentByAttrNew(i.get('children'), payloadList, attr, value)
                else:
                    payloadList.append(cls.simplyHierary(cls, i))


    def simplyHierary(self, hierarchy):
        if isinstance(hierarchy, dict):
            payload = hierarchy.get('payload')
            # 仅保留payload的name和text信息
            payload = {k: v for k, v in payload.items() if k in ['name', 'text', 'editalbe', 'touchable']}
            hierarchy['payload'] = payload
            if hierarchy.get('children') is not None:
                self.simplyHierary(self, hierarchy.get('children'))
        if isinstance(hierarchy, list):
            for i in hierarchy:
                self.simplyHierary(self,i)

        return hierarchy

    @classmethod
    def getHierarchyAssignKey(cls, hierarchy, elementBody, attr, value):
        # 有children的节点，将payload信息存入payloadList

        if isinstance(hierarchy, dict):
            res = cls.getChildByAttr(cls, hierarchy, attr, value)
            if res is None:
                if hierarchy.get('children') is not None:
                    cls.getHierarchyAssignKey(hierarchy.get('children'), elementBody, attr, value)
            else:
                elementBody.append(hierarchy)
        elif isinstance(hierarchy, list):
            for i in hierarchy:
                res = cls.getChildByAttr(cls, i, attr, value)
                if res is None:
                    if i.get('children') is not None:
                        cls.getHierarchyAssignKey(i.get('children'), elementBody, attr, value)
                else:
                    if i.get('children') is not None:
                        elementBody.append(i.get('children'))
                    else:
                        elementBody.append(i)

    # 找到符合属性值的元素，获取其父节点的父节点
    def getChildByAttr(self, hierarchy, attr, value):
        if hierarchy.get(attr) is None:
            return None
        if hierarchy.get(attr) == value:
            return hierarchy

    # 获取所有的叶子节点，并吧对应的父节点信息带上
    def getHierarchyParent(self, hierarchy, payloadList):
        # 有children的节点，将payload信息存入payloadList
        if isinstance(hierarchy, dict):
            json = self.getDictData(hierarchy)
            children = []

            if hierarchy.get('children') is not None:
                if json is not None:
                    json['children'] = children
                    payloadList.append(json)
                    self.getHierarchyParent(hierarchy.get('children'), children)
                else:
                    self.getHierarchyParent(hierarchy.get('children'), payloadList)
            else:
                if json is not None:
                    payloadList.append(json)
        elif isinstance(hierarchy, list):
            for i in hierarchy:
                json = self.getDictData(i)
                children = []

                if i.get('children') is not None:
                    if json is not None:
                        json['children'] = children
                        payloadList.append(json)
                        self.getHierarchyParent(i.get('children'), children)
                    else:
                        self.getHierarchyParent(i.get('children'), payloadList)
                else:
                    if json is not None:
                        payloadList.append(json)

    def getDictData(self, hierarchy):
        payload = hierarchy.get('payload')
        # 仅保留payload的name和text信息
        payload = {k: v for k, v in payload.items() if k in ['name', 'text', 'editalbe', 'touchable']}

        ignoreList = ['<Root>', 'android.widget.ScrollView', 'android.widget.LinearLayout', 'ctrip.android.view:id/fragment_stub', 'android.view.View', 'android.view.ViewGroup', 'android.widget.ImageView',
                      'android.widget.FrameLayout', 'android:id/content'
            , '软件更新通知：', '蓝牙开启。', '振铃器振动。']
        name = payload.get('name')
        json = None

        # 系统控件，即name包含systemui的控件，不需要存入payloadList
        if name in ignoreList and payload.get('text') is None or name.find('systemui') != -1:
            pass
        elif isinstance(name, dict):
            pass
        elif name.find('userData') != -1:
            pass
        elif name == 'android.widget.TextView':
            json = {'text': payload.get('text')}
        else:
            json = payload
        return json

    # 将字典数据转成对应的UI元素对下
    @classmethod
    def getDictDataToElement(cls, hierarchy: str) -> str:
        replyDict = eval(hierarchy)
        content: str = ""
        for key, value in replyDict.items():
            # 将控件的name和对应功能存入dict
            comment = value.get("desc")
            variable = value.get("component_name")
            operation = value.get("operation")
            if len(operation) == 0:
                continue
            opers = ", ".join(operation)

            content += '# ' + comment + " [" + opers + "]" + '\n' + variable + ' = ' + "ViewId('%s')" % key + '\n\n'

        return content

    # 针对给定的字符串转成对应的字典
    @classmethod
    def getValToDict(cls, val: str) -> dict:
        reList = val.split("\n")
        dic = {}
        for v in range(0, len(reList)):

            if not "#" in reList[v]:
                continue
            comment = CommonAction.getSubString(reList[v], "#", "[")
            list = CommonAction.getSubString(reList[v], "[", "]").split(",")
            opers = [x.strip() for x in list]
            component_val, element_val = cls.getComponentAndElement(cls, reList[v + 1])
            dic[element_val] = {"desc": comment, "component_name": component_val, "operation": opers}

        return dic

    def getComponentAndElement(self, val: str):
        val = val.split("=")
        component_val = val[0].strip()
        element_val = CommonAction.getSubString(val[1].strip(), "(", ")").replace("'", "").replace('"', "")
        return component_val, element_val
