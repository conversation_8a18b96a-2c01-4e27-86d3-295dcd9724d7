import re


def assert_text_type(text: str, text_type: str) -> bool:
    """
    Assert the text type of the input text.
    :param text: The input text.
    :param text_type: The text type to be asserted. Supported text types: "number", "english", "chinese", "special_characters".
    :return: True if the text type of the input text is the same as the text type to be asserted, otherwise False.
    """
    text_type = text_type.lower()
    if text_type == "number":
        return contains_numbers(text)
    if text_type == "english":
        return contains_english(text)
    if text_type == "chinese":
        return contains_chinese(text)
    if text_type == "special_characters":
        return contains_special_characters(text)
    raise ValueError(f"Unsupported text type: {text_type}")


def contains_chinese(text: str) -> bool:
    # 使用正则表达式检查字符串是否包含中文字符
    return bool(re.search(r'[\u4e00-\u9fff]', text))


def contains_english(text: str) -> bool:
    # 使用正则表达式检查字符串是否包含英文字符
    return bool(re.search(r'[a-zA-Z]', text))


def contains_special_characters(text: str) -> bool:
    # 特殊字符列表
    special_char = [" ", "!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "+", "=", "[", "]", "{", "}", ";", ":", "'", "\"", ",", "<", ".", ">", "/", "?", "\\", "|", "`", "~", "，", "。",
                    "、",
                    "；", "：", "‘", "’", "“", "”", "【", "】", "《", "》", "？", "！", "￥", "…", "（", "）"]
    # 检查字符串是否包含列表中的任一特殊字符
    return any(char in text for char in special_char)


def contains_numbers(text: str) -> bool:
    # 使用正则表达式检查字符串是否包含数字
    return bool(re.search(r'\d', text))


if __name__ == "__main__":
    _test_text = "Hello, 世界！"
    print(assert_text_type(_test_text, "english"))
    print(assert_text_type(_test_text, "chinese"))
    print(assert_text_type(_test_text, "special_characters"))
    print(assert_text_type(_test_text, "number"))
