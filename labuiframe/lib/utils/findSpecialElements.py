
def find_special_elements(element: dict, top_element: list, bottom_element: list):
    """查找特殊元素，如顶部浮层元素和底部浮层元素
    # 浮层元素一般宽度为size\[0\] >= 0.98，高度不为0，size\[1\] != 0
    # 顶部元素上边界在0.02内，底部元素下边界在0.02内
    # 顶部元素的中心点在0.3内，底部元素的中心点在0.7外

    top_element和bottom_element结构如下：
        [{
            "type": "android.widget.FrameLayout",
            "resourceId": "",
            "package": "com.miui.home",
            "name": "android.widget.FrameLayout",
            "pos": [0.5, 0.9858333333333333],
            "size": [1, 0.028333333333333332]
        }]

    param element: 元素
    param top_element: 顶部元素列表
    param bottom_element: 底部元素列表
    """
    _find_special_elements_recursive(element, top_element, bottom_element)

def _find_special_elements_recursive(element, top_element: list, bottom_element: list):
    try:
        if element['payload']['type'] != "Root":
            pos = element['payload']['pos']  # 获取元素中心位置
            size = element['payload']['size']  # 获取元素的大小

            if element['payload'].get('resourceId'):
                resource_id = element['payload']['resourceId']
            else:
                resource_id = ""

            temp = {
                "type": element['payload']['type'],  # 获取元素的type
                "resourceId": resource_id,
                "package": element['payload']['package'],  # 获取元素所在的包名
                "name": element['payload']['name'],  # 获取元素的name
                "pos": pos,
                "size": size,
            }

            if pos[1] - size[1] / 2 <= 0.02 and size[0] >= 0.98 and 0.5 - pos[1] >= 0.2 and size[1] <= 0.3:
                top_element.append(temp)

            if pos[1] + size[1] / 2 >= 0.98 and size[0] >= 0.98 and pos[1] - 0.5 >= 0.2 and size[1] <= 0.3:
                bottom_element.append(temp)

        if element.get('children'):
            for child in element['children']:
                _find_special_elements_recursive(child, top_element, bottom_element)
    except KeyError as e:
        print("element: ", element)
        raise e

