import json
import requests
from labuiframe.lib.utils.printUtil import printUtil


class GetProjectConfigInfoUtil:
    # 全局变量，用于执行同一批case使用同一个projectConfigInfo
    projectConfigInfo = {}

    # 获取projectConfigInfo
    @classmethod
    def getProjectConfigInfo(cls, projectName, platform, automationType, bu=1):
        # 默认bu为酒店
        if cls.projectConfigInfo == {}:
            # 调用接口获取projectConfigInfo
            cls.projectConfigInfo = cls.getProjectConfigInfoFromApi(bu, projectName, platform, automationType)
        return cls.projectConfigInfo
    
    # 获取projectConfigInfo
    @classmethod
    def getProjectConfigInfoFromApi(cls, bu, projectName, platform, automationType):
        # post请求http://127.0.0.1:8080/api/generateConfig/getProjectConfigInfo，"bu": 1,"projectName": "aigenerate-test","platform": 6,"automationType": "web"
        url = f"http://htlmysticmare.fws.qa.nt.ctripcorp.com"
        path = f"/api/generateConfig/getProjectConfigInfo"
        body = {
            "bu": bu,
            "projectName": projectName,
            "automationType": automationType,
            "platform": platform
        }
        response = requests.post(url=url + path, json=body)
        if response.status_code == 200 and response.json().get("data", {}):
            printUtil.printCaseDevice(f"获取项目配置信息返回结果：{response.text}")
            return json.loads(response.json().get("data", {}).get("extension", '{}'))
        else:
            printUtil.printCaseDevice(f"获取项目配置信息失败：{response.text}")
            return {}
        