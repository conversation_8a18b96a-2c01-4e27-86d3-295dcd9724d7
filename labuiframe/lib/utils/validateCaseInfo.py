# # -*- coding: utf-8 -*-
# import importlib
# import json
#
#
# import os
# import requests
# import time
#
# import configparser
#
# from labuiframe.lib.config.labconfig import Labconfig
#
#
# class validateCaseInfo():
#
#     def getCaseInfo(self):
#         headers = {'content-type': 'application/json'}
#         baseurl = 'http://10.3.220.233:8080/api/cap/getCaseInfoByDept'
#         for i in range(10):
#             try:
#                 group = Labconfig.getGroup()
#                 if group != "" and group != None:
#                     response = requests.post(baseurl,params={'department':group},headers=headers, timeout=20)
#                     return response.text
#                 break
#             except requests.exceptions.ConnectionError:
#                 print('Connection error -- please wait 3s')
#                 time.sleep(3)
#             except requests.exceptions.ChunkedEncodingError:
#                 print('ChunkedEncodingError -- please wait 3s')
#                 time.sleep(3)
#             except:
#                 print('Unfortunitely -- An unknown error -- please wait 3s')
#                 time.sleep(3)
#
#
#     def disableCase(self,caseId):
#         print("Remove Class")
#         headers = {'content-type': 'application/json'}
#         baseurl = 'http://10.3.220.233:8080/api/cap/disableInvalidCase/'+ str(caseId)
#         list = range(10)
#         for i in list:
#             try:
#                 response = requests.put(baseurl, headers=headers, timeout=20)
#                 del list
#                 return response.text
#                 break
#             except requests.exceptions.ConnectionError:
#                 print('Connection error -- please wait 3s')
#                 time.sleep(3)
#             except requests.exceptions.ChunkedEncodingError:
#                 print('ChunkedEncodingError -- please wait 3s')
#                 time.sleep(3)
#             except:
#                 print('Unfortunitely -- An unknown error -- please wait 3s')
#                 time.sleep(3)
#         del list
#
#     @classmethod
#     def delCaseInfo(cls,projectroot):
#         Labconfig.config(projectroot)
#         case_file = cls.getCaseInfo(cls)
#         cases = json.loads(case_file)
#         modules =[]
#         for case in cases:
#             try:
#                 module = importlib.import_module(case['packagename']+"."+case['classname'])
#             except Exception as e:
#                 print("Load ClassName Error:",e)
#                 print(case['id'])
#                 cls.disableCase(cls,case['id'])
