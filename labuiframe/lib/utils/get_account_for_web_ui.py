from labuiframe.lib.case.suite import *
from labuiframe.lib.result.logger import CmtLogger
from labuiframe.lib.case.web_base_case import WebBaseCase
from labuiframe.lib.utils.MySqlConnect import MySqlConnect

#trip-web获取登录信息脚本
class tripAccount(WebBaseCase):
    @classmethod
    def getMetaInfo(cls):
        return {
            "config": CommonAction.get_config(page_url="https://hk.trip.com/",platform=4),
        }

    def runTest(self):
        self.assertTrue(CommonAction.waitPageLoad(self.page,page_flag="登入"),"页面是否进入")

        self.click("登入", index=0)

        self.inputText("請輸入電郵地址","***********")

        self.click("繼續", index=0)

        self.inputText("請輸入密碼", "wendanuitest")

        self.click("登入", index=1)

        processCookies.process_cookies_and_duid(self,"_TIHK1c05b3kzowzv",2)

#Ctrip-web获取登录信息脚本
class ctripAccount(WebBaseCase):
    @classmethod
    def getMetaInfo(cls):
        return {
            "config": CommonAction.get_config(page_url="https://accounts.ctrip.com/h5Login/login_ctrip?sibling=T",platform=5),
        }

    def runTest(self):
        self.assertTrue(CommonAction.waitPageLoad(self.page,page_flag="携程账号登录"),"页面是否进入")

        self.inputText("ctripAccount","***********")

        self.inputText("ctripPassword", "wendanuitest")

        self.click("//*[contains(@class, 'g_btn_s') and contains(@class, 'nofastclick')]", index=0)

        self.click("同意并登录", index=0)

        processCookies.process_cookies_and_duid(self,"M6405505246",1)

'''
#租车团队获取登录信息脚本
'''
class sBUAccount(WebBaseCase):
    @classmethod
    def getMetaInfo(cls):
        return {
            "config": CommonAction.get_config(page_url="https://zuchebao.ctrip.com/pc/login"),
        }

    def runTest(self):
        self.assertTrue(CommonAction.waitPageLoad(self.page,page_flag="登录"),"页面是否进入")

        self.inputText("用户名/手机","***********")

        self.inputText("登录密码", "qwer1234.")

        self.click("登录", index=0)

        processCookies.process_cookies_and_duid(self,"SBUAccountInfo",18)

class processCookies:
    @classmethod
    #获取web生产登录的duid和cticket并将数据落库
    def process_cookies_and_duid(cls,testcase,uid,platform):
        cookies = testcase.page.context.cookies()
        cticket = None
        duid = None

        # 查找 cticket 和 uid
        for cookie in cookies:
            if (cookie['name'] == 'cticket' or cookie['name'] == 'bticket') and cookie['value']:
                cticket = cookie['value'].strip('"')
            elif cookie['name'] == 'DUID':
                duid = cookie['value'].strip('"')

        print(f"Account info：uid:{uid}, cticket: {cticket}, duid: {duid}")

        login_info_dal = MySqlConnect.get_uid_login_info(uid, platform)

        if login_info_dal and len(login_info_dal) == 2:
            MySqlConnect.save_uid_login_info(uid, platform, cticket, duid, 1)
        else:
            MySqlConnect.save_uid_login_info(uid, platform, cticket, duid, 0)


def main():
    suite = Suite([
        ctripAccount(),
        tripAccount(),
        sBUAccount()
    ])

    suite.platform = 'web'

    import pocounit
    result = pocounit.run(suite)


if __name__ == '__main__':
    main()



