import requests

from labuiframe.lib.utils.printUtil import printUtil


class GetBuListCacheUtil:
    # 全局变量，用于执行同一批case使用同一个buListCache
    buListCache = {}

    # 获取buListCache
    @classmethod
    def getBuListCache(cls):
        if cls.buListCache == {}:
            # 调用接口获取buListCache
            cls.buListCache = cls.getBuListCacheFromApi()
        return cls.buListCache
    
    # 获取buListCache
    @classmethod
    def getBuListCacheFromApi(cls):
        # get请求http://127.0.0.1:8080/api/generateConfig/getBuListCache
        response = requests.get("http://htlmysticmare.fws.qa.nt.ctripcorp.com/api/generateConfig/getBuList")
        if response.status_code == 200 and response.json().get("data", {}):
            return response.json().get("data", {})
        printUtil.printCaseDevice("获取buListCache失败，返回结果为：{}".format(response.text))
        return {}

    @classmethod
    def getPlatformById(cls, platformId, bu=1):
        # 根据platformId获取对应平台名称
        buListCache = cls.getBuListCache()
        # 先找到对应的BU数据
        bu_data = next((item for item in buListCache if item['buId'] == bu), None)
        if not bu_data:
            return {}
        
        # 在web和android类型中查找platformId
        for auto_type in bu_data['automationType'].values():
            platform = next((item for item in auto_type if item['id'] == platformId), None)
            if platform:
                return platform
        return {}
    
    @classmethod
    def getIsH5(cls, platformId, bu=1):
        # 根据platformId获取对应平台是否为h5, 默认bu为酒店
        platform_info = cls.getPlatformById(platformId, bu)
        printUtil.printCaseDevice(f"platform_info: {platform_info}")
        return 'H5' in platform_info.get('platform', '') if platform_info else False
    
# if __name__ == "__main__":
#     print(GetBuListCacheUtil.getIsH5(4))
#     print(GetBuListCacheUtil.getIsH5(6, 1))
    