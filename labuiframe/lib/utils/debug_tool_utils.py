#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import shutil
import subprocess
from pathlib import Path
import re
import requests
import json
import urllib3
import base64
import warnings

# 禁用所有警告
warnings.filterwarnings("ignore", category=Warning)

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class DebugToolUtils:
    def __init__(self):
        self.workspace_dir = Path(__file__).parent.absolute()
        
    def clean_dirs(self):
        """清理构建目录"""
        for dir_name in ['dist', 'build']:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                
    def check_conda(self):
        """检查是否安装了conda"""
        try:
            subprocess.run(['conda', '--version'], check=True, capture_output=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
            
    def check_conda_env(self, env_name):
        """检查指定的conda环境是否存在"""
        try:
            result = subprocess.run(['conda', 'env', 'list'], 
                                 check=True, capture_output=True, text=True)
            return env_name in result.stdout
        except subprocess.CalledProcessError:
            return False
            
    def get_package_version(self):
        """从dist目录获取包版本"""
        try:
            wheel_file = next(Path('dist').glob('*.whl'))
            version_match = re.search(r'labuiframe-(.+?)-', wheel_file.name)
            if version_match:
                return version_match.group(1)
        except (StopIteration, AttributeError):
            pass
        return None
        
    def build_package(self, use_conda=False, conda_env=None):
        """构建包"""
        self.clean_dirs()
        cmd = 'python setup-test.py sdist bdist_wheel'
        
        if use_conda and conda_env:
            cmd = f'conda run -n {conda_env} {cmd}'
            
        result = subprocess.run(cmd.split(), check=True, capture_output=True, text=True)
        return result.stdout
        
    def install_package(self, use_conda=False, conda_env=None):
        """安装包到本地环境"""
        wheel_file = next(Path('dist').glob('*.whl'))
        
        install_cmd = (
            'pip install '
            # '--find-links http://artifactory.release.ctripcorp.com/artifactory/api/pypi/trip-pypi-prod/simple '
            # '--trusted-host artifactory.release.ctripcorp.com '
            '--index-url http://artifactory.release.ctripcorp.com/artifactory/api/pypi/trip-pypi-prod/simple '
            '--trusted-host artifactory.release.ctripcorp.com '
            f'{wheel_file}'
        )
        
        if use_conda and conda_env:
            install_cmd = f'conda run -n {conda_env} {install_cmd}'
            
        result = subprocess.run(install_cmd.split(), check=True, capture_output=True, text=True)
        return result.stdout
        
    def upload_package(self):
        """重新构建并上传包到测试环境"""
        try:
            # 获取当前工作目录的绝对路径
            current_dir = os.getcwd()

            # 先清理构建目录
            print("清理构建目录...")
            self.clean_dirs()

            # 设置环境变量来配置仓库信息
            env = os.environ.copy()
            env.update({
                'DISTUTILS_INDEX_SERVERS': 'local',
                'DISTUTILS_LOCAL_REPOSITORY': 'http://artifactory.release.ctripcorp.com/artifactory/api/pypi/trip-pypi-prod-shanghai',
                'DISTUTILS_LOCAL_USERNAME': '<EMAIL>',
                'DISTUTILS_LOCAL_PASSWORD': 'cmVmdGtuOjAxOjAwMDAwMDAwMDA6a0xBNE45WnNrVTNZWks2aEFUQk1BTzJiWUVB'
            })

            # 重新构建并上传
            print("开始重新构建并上传...")
            cmd = "python setup-test.py bdist_wheel upload -r local"

            # 移除ANSI颜色代码的正则表达式
            ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')

            # 在当前工作目录执行命令
            result = subprocess.run(
                cmd,
                shell=True,
                check=True,
                capture_output=True,
                text=True,
                cwd=current_dir,  # 使用当前工作目录
                env=env  # 传递环境变量
            )

            # 清理输出中的ANSI颜色代码
            stdout = ansi_escape.sub('', result.stdout) if result.stdout else ''
            stderr = ansi_escape.sub('', result.stderr) if result.stderr else ''

            # 检查是否有包被跳过（已存在）
            if 'File already exists' in stderr or 'Skipping' in stderr:
                version = self.get_package_version()
                return f"{stdout}\n包已存在，版本 {version} 已跳过上传"

            if stderr and not stderr.strip().startswith('SKIPPED'):
                print(f"警告信息: {stderr}")

            return f"{stdout}\n重新构建并上传成功！" if stdout or not stderr else stdout
        except subprocess.CalledProcessError as e:
            stdout = ansi_escape.sub('', e.stdout) if e.stdout else ''
            stderr = ansi_escape.sub('', e.stderr) if e.stderr else ''
            error_msg = f"重新构建并上传失败:\nSTDOUT: {stdout}\nSTDERR: {stderr}"
            raise Exception(error_msg)
        except Exception as e:
            raise Exception(f"重新构建并上传失败: {str(e)}")
        
    def update_requirements(self, project_id, branch, token, version):
        """更新requirements.txt文件"""
        try:
            headers = {'PRIVATE-TOKEN': token}
            
            base_url = "https://git.dev.sh.ctripcorp.com"
            
            # 获取文件内容
            encoded_path = requests.utils.quote('requirements.txt', safe='')
            file_url = f"{base_url}/api/v4/projects/{project_id}/repository/files/{encoded_path}?ref={branch}"
            response = requests.get(file_url, headers=headers, verify=False)
            response.raise_for_status()
            
            # GitLab API返回的文件内容是Base64编码的
            content = base64.b64decode(response.json()['content']).decode('utf-8')
            
            # 检查是否需要更新
            current_version = re.search(r'labuiframe==([\d\.]+)', content)
            if current_version and current_version.group(1) == version:
                return {"message": f"版本号已经是 {version}，无需更新"}
            
            # 更新版本号
            new_content = re.sub(
                r'labuiframe==[\d\.]+',
                f'labuiframe=={version}',
                content
            )
            
            if new_content == content:
                return {"message": "未找到需要更新的版本号"}
            
            # 提交更新
            commit_url = f"{base_url}/api/v4/projects/{project_id}/repository/commits"
            data = {
                'branch': branch,
                'commit_message': f'Update labuiframe version to {version}',
                'actions': [{
                    'action': 'update',
                    'file_path': 'requirements.txt',
                    'content': new_content
                }]
            }
            
            response = requests.post(commit_url, headers=headers, json=data, verify=False)
            response.raise_for_status()
            result = response.json()
            
            return {
                "message": f"成功更新版本号到 {version}",
                "details": result
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {str(e)}"
            if hasattr(e.response, 'text'):
                error_msg += f"\n响应内容: {e.response.text}"
            raise Exception(error_msg)
        except Exception as e:
            raise Exception(f"更新失败: {str(e)}")
        
    def trigger_mpaas_job(self, job_id):
        """触发MPAAS任务"""
        url = "http://mpaas.cloud.phone.ctripcorp.com/mpaas/platform/openapi/job/run"
        # 根据jobId确定projectId
        project_id = "1521" if job_id in ["464", "463"] else "1517"
        
        data = {
            "jobId": int(job_id),
            "projectId": int(project_id)
        }
        
        response = requests.post(url, json=data, verify=False)
        return response.json()
        
    def get_mpaas_tasks(self, job_id):
        """获取MPAAS任务列表"""
        url = "http://mpaas.cloud.phone.ctripcorp.com/mpaas/platform/openapi/task/query/search"
        data = {
            "jobId": int(job_id),
            "pageNo": 0,
            "pageSize": 100
        }
        
        response = requests.post(url, json=data, verify=False)
        return response.json()
        
    def trigger_ai_generate(self, case_id):
        """触发AI生成接口"""
        try:
            # TODO: 实现AI生成接口调用逻辑
            # 这里需要实现实际的API调用
            return {
                "status": "success",
                "case_id": case_id,
                "message": "AI生成任务已触发"
            }
        except Exception as e:
            raise Exception(f"触发AI生成失败: {str(e)}")
        
    def get_repo_version(self, project_id, branch, token):
        """获取仓库中的依赖版本"""
        try:
            headers = {'PRIVATE-TOKEN': token}
            base_url = "https://git.dev.sh.ctripcorp.com"
            
            # 获取文件内容
            encoded_path = requests.utils.quote('requirements.txt', safe='')
            file_url = f"{base_url}/api/v4/projects/{project_id}/repository/files/{encoded_path}?ref={branch}"
            response = requests.get(file_url, headers=headers, verify=False)
            response.raise_for_status()
            
            # GitLab API返回的文件内容是Base64编码的
            content = base64.b64decode(response.json()['content']).decode('utf-8')
            return {"content": content}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {str(e)}"
            if hasattr(e.response, 'text'):
                error_msg += f"\n响应内容: {e.response.text}"
            raise Exception(error_msg)
        except Exception as e:
            raise Exception(f"获取文件失败: {str(e)}") 
        
    def get_python_info(self):
        """获取Python环境信息"""
        try:
            # 获取Python版本
            python_version = sys.version.split()[0]
            
            # 获取Python路径
            python_path = sys.executable
            
            # 获取pip版本
            pip_version = subprocess.run(
                ['pip', '--version'], 
                capture_output=True, 
                text=True, 
                check=True
            ).stdout.split()[1]
            
            # 获取setuptools版本
            setuptools_version = subprocess.run(
                [sys.executable, '-c', 'import setuptools; print(setuptools.__version__)'],
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
            
            # 获取wheel版本
            wheel_version = subprocess.run(
                [sys.executable, '-c', 'import wheel; print(wheel.__version__)'],
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
            
            return {
                "version": python_version,
                "path": python_path,
                "pip": pip_version,
                "setuptools": setuptools_version,
                "wheel": wheel_version
            }
        except Exception as e:
            return {
                "version": "未知",
                "path": "未知",
                "pip": "未知",
                "setuptools": "未知",
                "wheel": "未知"
            } 