from __future__ import absolute_import

from asyncio.coroutines import iscoroutinefunction
import time

from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.utils.capture import Labconfig


def fun_run_log_text(text):
    def fun_run(func):
        def fun(self, *args, **kwargs):
            printUtil.printCaseDevice(f'{func.__qualname__},{text} 开始进入')
            result = func(self, *args, **kwargs)
            printUtil.printCaseDevice(f'{func.__qualname__},{text} 执行完毕')
            return result

        async def func_async(self, *args, **kwargs):
            printUtil.printCaseDevice(f'{func.__qualname__},{text} 开始进入')
            result = await func(self, *args, **kwargs)
            printUtil.printCaseDevice(f'{func.__qualname__},{text} 执行完毕')
            return result

        if iscoroutinefunction(func):
            return func_async
        else:
            return fun
    return fun_run

def fun_run_log(func):
    def fun(self, *args, **kwargs):
        printUtil.printCaseDevice(f'{func.__qualname__} 开始进入')
        result = func(self, *args, **kwargs)
        printUtil.printCaseDevice(f'{func.__qualname__} 执行完毕')
        return result

    async def func_async(self, *args, **kwargs):
        printUtil.printCaseDevice(f'{func.__qualname__} 开始进入')
        result = await func(self, *args, **kwargs)
        printUtil.printCaseDevice(f'{func.__qualname__} 执行完毕')
        return result

    if iscoroutinefunction(func):
        return func_async
    else:
        return fun

def coast_time(func):
    def fun(self ,*args, **kwargs):
        t = time.perf_counter()
        result = func(self ,*args, **kwargs)
        printUtil.printCaseDevice(f'func {func.__name__} 执行结束，耗时 time:{time.perf_counter() - t:.8f} s')
        return result

    async def func_async(self ,*args, **kwargs):
        t = time.perf_counter()
        result = await func(self ,*args, **kwargs)
        printUtil.printCaseDevice(f'func {func.__name__} 执行结束，耗时 time:{time.perf_counter() - t:.8f} s')
        return result

    if iscoroutinefunction(func):
        return func_async
    else:
        return fun

def outer_time(text):
    def coast_time(func):
        def fun(self, *args, **kwargs):
            timeSplitToLoadPageStart = int(round(time.time() * 1000))
            t = time.perf_counter()
            printUtil.printCaseDevice(text + '开始执行')
            result = func(self, *args, **kwargs)
            printUtil.printCaseDevice(f'func {func.__name__} 执行结束，耗时 time:{time.perf_counter() - t:.8f} s')
            timeSplitToLoadPageEnd = int(round(time.time() * 1000))
            Labconfig.setTimeSplitToLoadPage(int(round((timeSplitToLoadPageEnd - timeSplitToLoadPageStart) / 1000, 0)))
            return result

        async def func_async(self, *args, **kwargs):
            timeSplitToLoadPageStart = int(round(time.time() * 1000))
            t = time.perf_counter()
            printUtil.printCaseDevice(text + '开始执行')
            result = await func(self, *args, **kwargs)
            printUtil.printCaseDevice(f'func {func.__name__} 执行结束，耗时 time:{time.perf_counter() - t:.8f} s')
            timeSplitToLoadPageEnd = int(round(time.time() * 1000))
            Labconfig.setTimeSplitToLoadPage(int(round((timeSplitToLoadPageEnd - timeSplitToLoadPageStart) / 1000, 0)))
            return result

        if iscoroutinefunction(func):
            return func_async
        else:
            return fun
    return  coast_time

