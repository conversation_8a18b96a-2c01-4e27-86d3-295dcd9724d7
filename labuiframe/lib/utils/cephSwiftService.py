# coding=utf-8
import requests
__author__ = "tingxiao"

import swiftclient

class CephSwiftService:
    user = 'Hotel_APP_APL:APL_ceph01'
    key = '8nrwzZu4HC0oXM64H5odlucTLTdWJt7vefDPdXof'
    SDK_URL = 'http://hotelsdk.hotel.ctripcorp.com/api/ceph'

    def __init__(self):
        self.conn = swiftclient.Connection(
        user=self.user,
        key=self.key,
        authurl='http://objstore2.qa.nt.ctripcorp.com/auth/1.0',
    )

    def uploadFile(self,file_path,file_name):
        container_name = 'APL'
        try:
            with open(file_path,'rb') as f:
                self.conn.put_object(container_name,file_name,contents=f.read())
        except Exception as e:
            print("CephSwift uploadFile fail:" + str(e))

    def listBuckets(self):
        for container in self.conn.get_account()[1]:
            print(container['name'])

    def getTemplateUrl(self, file_name, duration = 1000 * 60 * 60 * 24 * 15):
        try:
            rq = requests.get(self.SDK_URL + "/getTempUrl?fileName={}&durationTime={}".format(file_name, duration))
            if rq.status_code == 200:
                print(rq.text)
                return rq.text
            else:
                return None
        except Exception as e:
            print(e)
            return None


if __name__ == '__main__':
#     import boto3
#     boto3.setup_default_session(
#         aws_access_key_id=  '********************',
#         aws_secret_access_key='hMUROT774Ce3uKQGxRUXgrZwxVxvEQ1klPA7BsTU')
#     s3 = boto3.resource('s3')
#     for bucket in s3.buckets.all():
#         print(bucket.name)
    s3 = CephSwiftService()
    s3.listBuckets()