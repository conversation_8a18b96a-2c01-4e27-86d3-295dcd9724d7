# -*- coding: utf-8 -*-
import json
import requests
from labuiframe.lib.utils import general_utils
from labuiframe.lib.utils.timeUtil import timeUtil


class FlightMockUtil():
    GET_MOCK_URL = "http://mock.fws.qa.nt.ctripcorp.com/SuiteService/api/{}"

    @classmethod
    def getFlightMockCaseInfoList(cls, mockCaseId):
        data = {
            "mockCaseId": mockCaseId,
            "userDomain": "cn1",
            "userName": "yangyangpeng"
        }
        url = cls.GET_MOCK_URL.format("getMockCaseApiList")
        try:
            print("getMockCaseApiList request:{}".format(str(data)))
            response = requests.post(url, data=general_utils.to_json(data), verify=False)
            if response.content is not None:
                content = json.loads(response.content)
                print("getMockCaseApiList response:{}".format(content))
            else:
                print('***getMockCaseApiList fail!!***')
                return []
            mockDetailInfo = []
            if len(content['MockApiList']) > 0:
                for item in content["MockApiList"]:
                    dict = {"serviceName": "", "apiName": ""}
                    dict["serviceName"] = item["ServiceName"]
                    dict["apiName"] = item["ApiName"]
                    mockDetailInfo.append(dict)
                return mockDetailInfo
            else:
                print('***getFlightMockResult error!!')
                return mockDetailInfo
        except:
                print('***getMockCaseApiList exception!!')
                return mockDetailInfo

    @classmethod
    def getMockSuiteLog(cls, startTime, endTime, clientId, mockCaseId):
        data = {
            "mockCaseId":mockCaseId,
            "clientType" : 0,
            "clientInfo" : clientId,
            "startTime": timeUtil.getStrTime(startTime),
            "endTime": timeUtil.getStrTime(endTime),
            "userDomain": "cn1",
            "userName": "yangyangpeng",
        }
        url = cls.GET_MOCK_URL.format("getMockSuiteLog")
        try:
            print("getMockSuiteLog request:{}".format(str(data)))
            response = requests.post(url, data=general_utils.to_json(data), verify=False)
            if response.content is not None:
                content = json.loads(response.content)
                print("getMockSuiteLog response:{}".format(content))
            else:
                print('***getMockSuiteLog fail!!***')
                return []
            sucMockServiceInfo = []
            if len(content['MockLogList']) > 0:
                for item in content["MockLogList"]:
                    dict = {"serviceName": "", "apiName": ""}
                    dict["serviceName"] = item["ServiceName"]
                    dict["apiName"] = item["ApiName"]
                    if dict not in sucMockServiceInfo:
                        sucMockServiceInfo.append(dict)
                return sucMockServiceInfo
            else:
                print('***getMockSuiteLog error!!')
                return sucMockServiceInfo
        except:
            print('***getMockCaseApiList exception!!')
            return sucMockServiceInfo


    @classmethod
    def getFlightMockInfo(cls,startTime,endTime,clientId,mockCaseId):
        # url = "http://************:8080/api/getFlightMockResult"
        #机票mock无生产环境，因此使用测试环境
        url = "http://offline.fx.fws.qa.nt.ctripcorp.com/soa2/22336/getFlightMockResult"
        data = {
            "startTime": startTime,
            "endTime": endTime,
            "clientId": clientId,
            "mockCaseId": mockCaseId
        }
        try:
            print("getFlightMockResult request:{}".format(str(data)))
            response = requests.post(url, data=general_utils.to_json(data), verify=False)
            if response.content is not None:
                content = json.loads(response.content)
                print("getFlightMockResult response:{}".format(content))
            else:
                print('***getFlightMockResult fail!!***')
                return [], []
            mockDetailInfo = []
            sucMockServiceInfo = []
            if len(content['mockDetailInfo']) != 0:
                mockDetailInfo = content['mockDetailInfo']
                if len(content['sucMockServiceInfo']) != 0:
                    try :
                        sucMockServiceInfo = content['sucMockServiceInfo']
                    except:
                        print('***getFlightMockResult sucMockServiceInfo fail!!***')
                return mockDetailInfo, sucMockServiceInfo
            else:
                print('***getFlightMockResult error!!')
                return mockDetailInfo,sucMockServiceInfo
        except:
                print('***getFlightMockResult exception!!')
                return mockDetailInfo,sucMockServiceInfo
