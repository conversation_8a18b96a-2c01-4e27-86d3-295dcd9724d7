# -*- coding: utf-8 -*-
from labuiframe.lib.utils.capture import Capture

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.image.watcher import *
from labuiframe.lib.utils.device import DeviceInfo
from airtest.core.helper import log
import time

class Watcher_util:
    metaInfo = None

    def __init__(self,metaInfo=None):
        self.metaInfo = metaInfo
        self.watcher = Watcher()  # 图片比对平台
        self.cmt_capture = Capture()
        if metaInfo != None:
            self.watcher.createBy = metaInfo["author"]
            try:
                self.watcher.env = metaInfo['evn']
            except:
                print("--")


    def watcher_open(self,poco,test_name, app_name='ctrip', viewport_size=None, author = None, env=""):
        if author != None:
            self.watcher.createBy = author
        if env != "":
            self.watcher.env = env
        if Labconfig.getPlatformEnv() != "":
            self.watcher.env = Labconfig.getPlatformEnv()
        if Labconfig.getIsLogLab() == "True" and Labconfig.is_watcher_enabled() == "True":
            print("[Lab] Watcher open")
            if Labconfig.getAppid() is not None:
                if Labconfig.getAppid() == "99999999":
                    app_name = "ctrip"
                elif Labconfig.getAppid() == "37":
                    app_name = "Trip"
            return self.watcher.open(poco ,DeviceInfo.getDevice() ,app_name ,test_name ,viewport_size)


    def watcher_check(self, tag, match_timeout=-1, target=None):
        self.watcher.uiCase = ""
        if self.metaInfo != None:
            try:
                self.watcher.uiCase = self.metaInfo["casename"]
            except:
                print("get caseName fail")
        if Labconfig.getIsLogLab() == "True" and Labconfig.is_watcher_enabled() == "True":
            print("[Lab] Watcher Check")
            if Labconfig.get_sync_watcher_result().lower() == 'true':
                response = self.watcher.check_window_sync(tag, match_timeout, target)
                if response.status_code == 200:
                    watcher_result = response.json()
                    if watcher_result['newBaseline']:
                        message = "自动设置为基准图，图片链接：" + watcher_result['imageMatchResult']['baseImagePath']
                        log(message, timestamp=time.time(), desc="watcher比对")
                    else:
                        match_result = "Watcher比对结果：" + ("成功" if watcher_result['imageMatchResult']['match'] else "失败")
                        diff_value = "相似度：" + str(watcher_result['imageMatchResult']['diffResult'])
                        baseimage_message = "基准图片：\n" + watcher_result['imageMatchResult']['baseImagePath']
                        diffimage_message = "比对图片：\n" + watcher_result['imageMatchResult']['diffImagePath']
                        detail_message = match_result + ", " + diff_value + "\n\n" + baseimage_message + "\n\n" + diffimage_message
                        if watcher_result['imageMatchResult']['match']:
                            log(detail_message, timestamp=time.time(), desc="Watcher比对")
                        else:
                            if Labconfig.get_ignore_watcher_result().lower() == 'true':
                                log(detail_message, timestamp=time.time(), desc="Watcher比对")
                            else:
                                raise Exception(detail_message)
                else:
                    if Labconfig.get_ignore_watcher_result().lower() == 'true':
                        log("Watcher返回"+ str(response.status_code), timestamp=time.time(), desc="watcher比对")
                    else:
                        raise Exception("Watcher返回"+ str(response.status_code))
            else:
                self.watcher.check_window(tag, match_timeout, target)
        else:
            self.cmt_capture.getScreenShot()


    def watcher_testsuitelog(self, run_id, suite_name, appIDOrName):
        if Labconfig.getIsLogLab() == "True" and Labconfig.is_watcher_enabled() == "True":
            print("[Lab] Watcher log suite name")
            if int(Labconfig.get_ctaskid()) > 0:
                run_from = "ctest"
            else:
                run_from = "ATL"
            department = Labconfig.getGroup()
            return self.watcher.log_test_suite(run_id, suite_name, appIDOrName, department, run_from)

    def watcher_stopSuite(self, run_id, suite_name, appIDOrName):
        if Labconfig.getIsLogLab() == "True" and Labconfig.is_watcher_enabled() == "True":
            print("[Lab] Watcher stop suite")
            return self.watcher.stopSuite(run_id, suite_name, appIDOrName)