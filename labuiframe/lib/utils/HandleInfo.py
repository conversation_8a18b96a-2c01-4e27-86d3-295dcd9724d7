# coding=utf-8
import os
import time

import jsonpath

from labuiframe.lib.result.logger import CmtLogger
from labuiframe.lib.utils.device import DeviceInfo
import json
import string
import random
import requests

from labuiframe.lib.utils.logsDecorator import coast_time
from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.socketUtil import SocketUtil

__author__ = "ss_gao"

import socket
from airtest.core.android.adb import ADB


class HandleInfo:
    currentClientid = ""
    currentMockid = ""
    localMockNameList = []
    request = []
    response = []
    socketMessageDict = dict()
    bdsData = ""
    logTraceArray = []
    logTraceList = ""
    host = '127.0.0.1'
    circulateTimes = 1
    currentCirculate = 1
    TagLists = []
    currentTag = ''
    extensionForCase = None
    logoutSelfUid = 0
    caseInfoList = None
    primaryCaseId = "0"
    currentLabelTag = ''
    currentSim = ''
    currentIp_port = ''
    IOSSession = None
    metaInfo = None
    mockKey =''

    @classmethod
    def connectSocket(cls, case_config_json=""):
        try:
            print("开始进行socket连接")
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                return False
            cls.currentClientid = cls.getSocketMessage(cls, sock, "info", 'cid')
            sock.close()
            print("已关闭socket连接")
        except Exception as e:
            sock.close()
            print("异常，已关闭socket连接")
            print('str(e):\t\t', str(e))
            print('########################################################')

    def getSocketMessage(self, sock, info, key=''):
        try:
            with sock:
                for _ in range(10):
                    msg = sock.recv(4)
                    leng = int.from_bytes(msg, byteorder='big', signed=False)
                    if leng and leng > 0:
                        msg = sock.recv(leng)
                        msg = self.getTruncationMsg(msg, leng, sock)
                        if msg:
                            msg = json.loads(msg.decode('utf-8'))
                            if msg['msgType'] == info:
                                if key == '':
                                    return msg['msgContent']
                                return msg['msgContent'][key]
        except Exception as e:
            print('getSocketMessage get socket msg:\t\t', str(e))
            print('########################################################')
            return ''

    @classmethod
    def sendSocketMessage(cls, key, message):
        try:
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                printUtil.printCaseDevice("Socket connection failed.")
                return False

            with sock:
                info = f'"key":"{key}","message":"{message}"'
                sock.send(info.encode(encoding='utf_8', errors='strict'))
                time.sleep(4)

            return True
        except Exception as e:
            printUtil.printCaseDevice('send socket msg:\t', repr(e))
            printUtil.printCaseDevice('Socket message sending failed.')
            return False

    # 获取请求的request
    @classmethod
    def getServiceRequest(cls, type='list'):
        res, sock, port = SocketUtil.conDeviceForward()
        if not res:
            return ""
        instance = cls()  # 创建一个类的实例
        instance.setRequest("")
        try:
            with sock:
                for _ in range(Labconfig.getRequestCount()):
                    msg = sock.recv(4)
                    leng = int.from_bytes(msg, byteorder='big', signed=False)
                    if not leng or leng <= 0:
                        continue
                    msg = sock.recv(leng)
                    msg = instance.getTruncationMsg(msg, leng, sock)
                    if not msg:
                        continue
                    msg = json.loads(msg.decode('utf-8'), strict=False)
                    if msg.get('msgType','') != type:
                        continue
                    print('getServiceRequest -> msgContent:', msg['msgContent'])
                    instance.setRequest(msg['msgContent'])
        except Exception as e:
            printUtil.printCaseDevice('###########################消息全部接受完毕#############################')
        return ''

    @classmethod
    def getAllSocketMessage(cls):
        keys = []
        values = []

        try:
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                printUtil.printCaseDevice("Socket connection failed.")
                return False
            instance = cls()  # 创建一个类的实例
            try:
                with sock:
                    for _ in range(Labconfig.getRequestCount()):
                        msg_len = int.from_bytes(sock.recv(4), byteorder='big', signed=False)
                        if msg_len > 0:
                            msg = sock.recv(msg_len)
                            msg = instance.getTruncationMsg(msg,msg_len, sock)
                            if msg:
                                msg_content = json.loads(msg.decode('utf-8'), strict=False)
                                printUtil.printCaseDevice("Message: ", msg_content)
                                if 'msgType' in msg_content:
                                    keys.append(msg_content['msgType'])
                                    values.append(msg_content['msgContent'])
                                    printUtil.printCaseDevice("Message Type: ", msg_content['msgType'])
                                else:
                                    break
            except:
                printUtil.printCaseDevice('###########################消息全部接受完毕#############################')
        except Exception as e:
            printUtil.printCaseDevice('getAllSocketMessage - repr(e):\t', repr(e))
            printUtil.printCaseDevice('########################################################')
        if keys:
            printUtil.printCaseDevice('消息接收成功')
            cls.socketMessageDict = {k: v for k, v in zip(keys, values)}
            # print(cls.socketMessageDict)


    # 获取请求的request
    @classmethod
    def getServiceResponse(cls, type='bookResponse'):
        try:
            instance = cls()  # 创建一个类的实例
            instance.setResponse("")
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                return False
            try:
                while True:
                    msg = sock.recv(4)
                    leng = int.from_bytes(msg, byteorder='big', signed=False)
                    if leng and leng > 0:
                        msg = sock.recv(leng)
                        msg = instance.getTruncationMsg( msg, leng, sock)
                        if msg:
                            msg = json.loads(msg.decode('utf-8'), strict=False)
                            if msg['msgType'] and msg['msgType'] == type:
                                instance.setResponse( msg['msgContent'])
                                break
            except:
                printUtil.printCaseDevice('###########################消息全部接受完毕#############################')
        except Exception as e:
            printUtil.printCaseDevice('getServiceResponse - str(e):\t\t', str(e))
            printUtil.printCaseDevice('########################################################')
        finally:
            sock.close()

    @classmethod
    def getAutoOrderInfo(cls):
        result = {
            "orderId": '',
            "traceId": '',
            "amount": '',
            "currecyCode": '',
            "cnyAmount": '',
            "requestId": ''
        }
        instance = cls()  # 创建一个类的实例
        try:
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                return result
            try:
                with sock:
                    while True:
                        msg = sock.recv(4)
                        leng = int.from_bytes(msg, byteorder='big', signed=False)
                        if leng and leng > 0:
                            msg = sock.recv(leng)
                            msg = instance.getTruncationMsg(msg, leng, sock)
                            if msg:
                                msg = json.loads(msg.decode('utf-8'), strict=False)
                                if msg['msgType'] and msg['msgType'] == 'bookResponse':
                                    result["orderId"] = instance.getJsPath(msg['msgContent'], "$.OrderId")
                                    result["traceId"] = instance.getJsPath(msg['msgContent'],
                                                                           "$.head.extension[?(@.name==\"htl-tracelogid\")].value")
                                    result["amount"] = instance.getJsPath(msg['msgContent'], "$.PriceForPay.Amount")
                                    result["currecyCode"] = instance.getJsPath(msg['msgContent'],
                                                                               "$.PriceForPay.CurrecyCode")
                                    result["cnyAmount"] = instance.getJsPath(msg['msgContent'], "$.PriceForPay.CNYAmount")
                                    result["requestId"] = instance.getJsPath(msg['msgContent'],
                                                                             "$.PayEntity.payinfo.requestId")
                                    break
            except:
                printUtil.printCaseDevice('###########################消息全部接受完毕#############################')
        except Exception as e:
            printUtil.printCaseDevice('reqget - str(e):\t\t', str(e))
            printUtil.printCaseDevice('########################################################')
        return result

    @classmethod
    def getAutoOrderInfoNewCombine(cls):
        result = {}
        result["orderId"] = ''
        result["traceId"] = ''
        result["needPay"] = True
        try:
            res, sock, port = SocketUtil.conDeviceForward()
            if not res:
                return False
            times = 0
            try:
                while True:
                    # print(time.strftime("%H:%M:%S:%U", time.localtime(time.time())), '...开始收消息，消息次数：',times)
                    start = time.time()
                    msg = sock.recv(4)
                    leng = int.from_bytes(msg, byteorder='big', signed=False)
                    if leng and leng > 0:
                        msg = sock.recv(leng)
                        msg = cls.getTruncationMsg(cls, msg, leng, sock)
                        if msg:
                            end = time.time()
                            print('消息耗时{}秒'.format((end - start)))
                            strMsg = msg.decode('utf-8')
                            print(time.strftime("%H:%M:%S", time.localtime(time.time())), "。。。。。message ", strMsg)
                            msg = json.loads(strMsg, strict=False)
                            if msg['msgType'] and msg['msgType'] == 'bookResponse':
                                # 暂
                                result["orderId"] = cls.getJsPath(cls, msg['msgContent'], "$.orderList[0]")
                                result["traceId"] = cls.getJsPath(cls, msg['msgContent'],
                                                                  "$.ReservationClientTraceInfo.ClientRoomTraceInfos[0].OrderSubmitTraceId")
                                nextOperationType = cls.getJsPath(cls, msg['msgContent'], "$.NextOperationType")
                                sock.close()
                                return result
                    times = times + 1
            except:
                printUtil.printCaseDevice('###########################消息全部接受完毕#############################')
            sock.close()
            if nextOperationType == "OrderDetail" or nextOperationType == "OrderList":
                result["needPay"] = True
            return result
        except Exception as e:
            try:
                sock.close()
            except Exception as se:
                printUtil.printCaseDevice(str(se))
            printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), 'reqget - str(e):\t\t', str(e))
            printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), 'reqget - repr(e):\t', repr(e))
            printUtil.printCaseDevice('########################################################')
            return result

    # 这里处理链接通道，建立socket 链接
    # def conDeviceForward(self, timeout=20):
    #     printUtil.printCaseDevice(time.strftime("%H:%M:%S:%U", time.localtime(time.time())), "....开始链接通道，socket链接超时时间:{}".format(timeout))
    #     res, port = self.connChannel(self)
    #     if not res:
    #         return (False, None, None)
    #     try:
    #         socket.setdefaulttimeout(timeout)
    #         sock = socket.socket()
    #         res = sock.connect((self.host, port))
    #         return (True, sock, port)
    #     except Exception as e:
    #         print(time.strftime("%H:%M:%S", time.localtime(time.time())), 'conDeviceForward - repr(e):\t', repr(e))
    #         return (False, None, None)

    def getJsPath(self, req, jPath):
        try:
            req = json.loads(req)
            printUtil.printCaseDevice("jsonpath", str(jsonpath.jsonpath(req, jPath)))
            res = jsonpath.jsonpath(req, jPath)
            if not res:
                return ""
            if len(res):
                return res[0]
            else:
                return ""
        except Exception as e:
            printUtil.printCaseDevice('jsonpath-str(e):\t\t', str(e))
            printUtil.printCaseDevice('jsonpath-repr(e):\t', repr(e))
            return ""

    # 获取埋点数据
    @classmethod
    def getClientTrace(cls, traceKey='TTI', value=""):
        try:
            res, sock, port = SocketUtil.conDeviceForward( timeout=5)
            if not res:
                return False
            times = 0
            while True:
                printUtil.printCaseDevice( '...开始收消息，消息次数：', times)

                msg = sock.recv(4)
                leng = int.from_bytes(msg, byteorder='big', signed=False)
                if leng and leng > 0:
                    msg = sock.recv(leng)
                    msg = cls.getTruncationMsg(cls, msg, leng, sock)

                    if msg:
                        strMsg = msg.decode('utf-8')
                        print(time.strftime("%H:%M:%S", time.localtime(time.time())), "。。。。。rquest message ", strMsg)
                        msg = json.loads(strMsg, strict=False)
                        if msg['msgType'] and msg['msgType'] == traceKey:
                            try:
                                # msg['msgContent']为json场景
                                trace = json.loads(msg['msgContent'], strict=False)
                            except Exception as e:
                                # msg['msgContent']为str场景
                                trace = msg['msgContent']
                                #若trace值为o-app-toast，则单独处理，因为公共会发多条名为o-app-toast的toast
                                if traceKey=="o_app_show_toast":
                                    printUtil.printCaseDevice(
                                        "traceKey为o_app_show_toast的多toast场景")
                                    if len(value) > 0 and value in trace:
                                        printUtil.printCaseDevice("已找到需要的o_app_show_toast")
                                        cls.setLogTrace(cls, trace)
                                        sock.close()
                                        return
                                    printUtil.printCaseDevice("本次不是需要的o_app_show_toast")
                                    continue
                            cls.setLogTrace(cls, trace)
                            sock.close()
                            return
                    else:
                        cls.setRequest(cls, "")
                else:
                    cls.setRequest(cls, "")
                times = times + 1
            sock.close()
        except Exception as e:
            try:
                sock.close()
            except Exception as se:
                print(time.strftime("%H:%M:%S", time.localtime(time.time())), 'reqget - str(e):\t\t', repr(se))
            print(time.strftime("%H:%M:%S", time.localtime(time.time())), 'reqget - repr(e):\t', repr(e))
            print('########################################################')

    # 如果有消息没有接收全，之类需要调用下重复再次接收
    def getTruncationMsg(self, msg, leng, sock):
        pack_length = len(msg)
        # print(time.strftime("%H:%M:%S", time.localtime(time.time())), "消息长度：" + str(pack_length), "需要接收消息的长度", leng,
        #       "。。。。。socket message value ", str(msg))
        while pack_length < leng:
            try:
                part_body = sock.recv((leng - pack_length))
                # print("------- 再次接收消息长度：" + str(len(part_body)))
                msg += part_body
                pack_length = len(msg)
            except:
                print("消息接收完成")
                break
        return msg

    # # 链接通道方法
    # def connChannel(self):
    #     times = 0
    #     while times < 5:
    #         try:
    #             port = self.check_port(self)
    #             if port == '':
    #                 print(time.strftime("%H:%M:%S:%U", time.localtime(time.time())), '...没有可用的端口号了,返回空')
    #                 times = 10
    #                 return (False, port)
    #             printUtil.printCaseDevice('拿到端口号了执行adb forward')
    #             deviceName = DeviceInfo.getDevice().serialno
    #             adbres = ADB(serialno=deviceName).cmd("forward tcp:" + str(port) + " tcp:8800")
    #             printUtil.printCaseDevice(adbres)
    #             res = ADB(serialno=deviceName).cmd("forward --list")
    #             printUtil.printCaseDevice(res)
    #             times = 10
    #             return (True, port)
    #         except Exception as e:
    #             printUtil.printCaseDevice('channel(e):\t\t', str(e))
    #             print('########################################################')
    #             times = times + 1
    #     return (False, port)

    # # 检查端口号是否占用，check5次，不占用则返回有用的端口号
    # def check_port(self):
    #     seeds = string.digits
    #     i = 0
    #     while i < 5:
    #         port = random.choices(seeds, k=4)
    #         port = int(''.join(str(item) for item in port))
    #         s = socket.socket()
    #         try:
    #             s.connect((self.host, port))
    #             s.shutdown(2)
    #             print('%s:%d is used' % (self.host, port))
    #             port = ''
    #             continue
    #         except socket.error as e:
    #             print('%s:%d is unused' % (self.host, port))
    #             break
    #         printUtil.printCaseDevice("-------端口号占用，重新尝试。")
    #         i = i + 1
    #     return port

    @classmethod
    def getCurrentTraceKey(cls, traceKey, valuePath, value):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey, value)
            return cls.getTraceKeyBody(cls, traceKey, valuePath)
        except Exception as e:
            printUtil.printCaseDevice('getCurrentTraceKey(e):\t\t', str(e))
            return ''

    # 返回通过jsonpath获取的value值以及整个埋点的json
    @classmethod
    def getCurrentTraceKeyNew(cls, traceKey, valuePath):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
            return cls.getTraceKeyBodyNew(cls, traceKey, valuePath)
        except Exception as e:
            print('getCurrentTraceKey(e):\t\t', str(e))
            print('getCurrentTraceKey(e):\t', repr(e))
            return ''

    @classmethod
    def getCurrentTraceKeyInfo(cls, traceKey, valuePath):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
            return cls.getTraceKeyBodyNew(cls, traceKey, valuePath)
        except Exception as e:
            print('getCurrentTraceKey(e):\t\t', str(e))
            return None, None

    @classmethod
    def getTraceValue(cls, traceKey):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
            if cls.logTraceList != '':
                path = "$.trace"
                res = jsonpath.jsonpath(cls.logTraceList, path)
                if not res:
                    return None, None
                if len(res):
                    return res[0]
        except Exception as e:
            print('getTraceValue(e):\t\t', str(e))
            return ''

    # 返回通过jsonpath获取的value值以及整个埋点的json
    @classmethod
    def getTraceJsonPathValue(cls, traceKey, valuePath):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
                # 这里重试
                if not cls.getTraceKey(cls, traceKey):
                    cls.getClientTrace(traceKey)
            if cls.logTraceList != '':
                # print("trace:",cls.logTraceList)
                path = "$.trace"
                res = jsonpath.jsonpath(cls.logTraceList, path)
                if not res:
                    return None, None
                if len(res):
                    val = json.loads(res[0], strict=False)
                    print(time.strftime("%H:%M:%S", time.localtime(time.time())), "最终检查的path", valuePath, "\n", val,
                          "\n", "pathvalue - jsonpath", str(jsonpath.jsonpath(val, valuePath)))
                    value = jsonpath.jsonpath(val, valuePath)
                    if value:
                        return value[0], val
                    else:
                        return 'uiTestjsonPathNullMatch', val
                else:
                    return None, None
            else:
                return None, None
        except Exception as e:
            print('getTraceKeyBody(e):\t\t', str(e))
            return None, None

    @classmethod
    def getCurrentTraceKey1(cls, traceKey, valuePath):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
            return cls.getTraceKeyBody1(cls, traceKey, valuePath)
        except Exception as e:
            print('getCurrentTraceKey(e):\t\t', str(e))
            print('getCurrentTraceKey(e):\t', repr(e))
            return ''

    @classmethod
    def getCurrentTraceValue(cls, traceKey):
        try:
            if not cls.getTraceKey(cls, traceKey):
                cls.getClientTrace(traceKey)
            return cls.logTraceList
        except Exception as e:
            print('getCurrentTraceKey(e):\t\t', str(e))
            print('getCurrentTraceKey(e):\t', repr(e))
            return ''

    def getTraceKey(cls, traceKey):
        try:
            if cls.logTraceList != '':
                path = "$.traceKey"
                res = jsonpath.jsonpath(cls.logTraceList, path)
                if not res:
                    return False
                else:
                    if len(res):
                        if res[0] == traceKey:
                            return True

                    return False
            return False
        except Exception as e:
            print('getTraceKey(e):\t\t', str(e))
            return False

    def getTraceKeyBody(self, traceKey, valuePath):
        try:
            if self.logTraceList != '':
                # self.logTraceList为str场景
                if isinstance(self.logTraceList, str):
                    return self.logTraceList
                # self.logTraceList为json场景
                path = "$.trace"
                res = jsonpath.jsonpath(self.logTraceList, path)
                res2 = jsonpath.jsonpath(self.logTraceList, valuePath);
                if not res:
                    if res2:
                        return res2[0]
                    else:
                        return ""
                if len(res):
                    # print("pathvalue", path, "|", "pathvalue - jsonpath", str(jsonpath.jsonpath(self.logTraceList, path)))
                    val = json.loads(res[0], strict=False)
                    print(time.strftime("%H:%M:%S", time.localtime(time.time())), "最终检查的path", valuePath, "\n", val,
                          "\n", "pathvalue - jsonpath", str(jsonpath.jsonpath(val, valuePath)))
                    value = jsonpath.jsonpath(val, valuePath)
                    return value[0]
                else:
                    return ""
            else:
                return ""
        except Exception as e:
            print('getTraceKeyBody(e):\t\t', str(e))
            print('getTraceKeyBody(e):\t', repr(e))
            return ""

    # 返回通过jsonpath获取的value值以及整个埋点的json
    def getTraceKeyBodyNew(self, traceKey, valuePath):
        try:
            list = []
            if self.logTraceList != '':
                path = "$.trace"
                res = jsonpath.jsonpath(self.logTraceList, path)
                res2 = jsonpath.jsonpath(self.logTraceList, valuePath);
                if not res:
                    if res2:
                        list.append(res2[0])
                        list.append(res2[0])
                        return list
                    else:
                        return list
                if len(res):
                    # print("pathvalue", path, "|", "pathvalue - jsonpath", str(jsonpath.jsonpath(self.logTraceList, path)))
                    val = json.loads(res[0], strict=False)
                    print(time.strftime("%H:%M:%S", time.localtime(time.time())), "最终检查的path", valuePath, "\n", val,
                          "\n", "pathvalue - jsonpath", str(jsonpath.jsonpath(val, valuePath)))
                    value = jsonpath.jsonpath(val, valuePath)
                    list.append(value[0])
                    list.append(val)
                    return list
                else:
                    return list
            else:
                return list
        except Exception as e:
            print('getTraceKeyBody(e):\t\t', str(e))
            print('getTraceKeyBody(e):\t', repr(e))
            return list

    def getTraceKeyBody1(self, traceKey, valuePath):
        try:
            list = []
            if self.logTraceList != '':
                path = "$.trace"
                res = jsonpath.jsonpath(self.logTraceList, path)
                res2 = jsonpath.jsonpath(self.logTraceList, valuePath);
                if not res:
                    if res2:
                        list.append(res2)
                        list.append(res2)
                        return list
                    else:
                        return list
                if len(res):
                    # print("pathvalue", path, "|", "pathvalue - jsonpath", str(jsonpath.jsonpath(self.logTraceList, path)))
                    val = json.loads(res[0], strict=False)
                    print(time.strftime("%H:%M:%S", time.localtime(time.time())), "最终检查的path", valuePath, "\n", val,
                          "\n", "pathvalue - jsonpath", str(jsonpath.jsonpath(val, valuePath)))
                    value = jsonpath.jsonpath(val, valuePath)
                    list.append(value)
                    list.append(val)
                    return list
                else:
                    return list
            else:
                return list
        except Exception as e:
            print('getTraceKeyBody(e):\t\t', str(e))
            print('getTraceKeyBody(e):\t', repr(e))
            return list

    # 设置logtrace值
    def setLogTrace(self, value=''):
        try:
            self.logTraceList = value;
            # self.logTraceList.append(value)
            print("traceInfo:\t", self.logTraceList)
        except  Exception as e:
            print('setLogTrace(e):\t\t', str(e))
            print('setLogTrace(e):\t', repr(e))

    def setRequest(self, value=''):
        if value == '':
            self.request = []
        else:
            self.request.append(value)
            print(CmtLogger.case_result_id, "请求list大小：", len(self.request))

    def setLogTracList(self, value=''):
        if value == '':
            self.logTraceArray = []
        else:
            self.logTraceArray.append(value)
            print(CmtLogger.case_result_id, "log trace list size：", len(self.request))

    def setResponse(self, value=''):
        if value == '':
            self.response = []
        else:
            self.response.append(value)
            print(CmtLogger.case_result_id, "返回list大小：", len(self.response))

    @classmethod
    def clearData(cls):
        cls.request = []
        cls.response = []
        cls.logTraceArray = []
        cls.logTraceList = ''

    @classmethod
    def clearRequest(cls):
        cls.request = []

    @classmethod
    def getLogTrace(cls):
        return cls.logTraceList

    @classmethod
    def getRequest(cls):
        if len(cls.request) == 0:
            return ''
        return cls.request[len(cls.request) - 1]

    @classmethod
    def getFirstRequest(cls):
        if len(cls.request) == 0:
            return ''
        return cls.request[0]

    @classmethod
    def getRequestLenth(cls):
        return len(cls.request)

    @classmethod
    def getResponse(cls):
        if len(cls.response) == 0:
            return ''
        return cls.response[len(cls.response) - 1]

    @classmethod
    def getClientid(cls):
        if (DeviceInfo.getPlatform().lower() == "ios"):
            return ""
        # if cls.currentClientid is None or cls.currentClientid == "":
        #     cls.currentClientid = cls.getClientSocket(cls)
        if cls.currentClientid is None or cls.currentClientid == "":
            logcat_info = ADB(serialno=DeviceInfo.deviceName).logcat(read_timeout=1)
            start = time.time()
            for line in logcat_info:
                if time.time() - start > 5:
                    printUtil.printCaseDevice("logcat未成功获取clientid")
                    break
                line = line.decode('utf-8', 'ignore')
                if "current clientId" in line:
                    cid_line = line.split(":")
                    cid = cid_line[len(cid_line) - 1].replace("\r", "").replace("\n", "").strip()
                    # 日志用于自动分析失败case，请不要变更！！
                    printUtil.printCaseDevice("logcat 获取到的 current clientId为:" + cid)
                    printUtil.printCaseDevice("如果case中已经单独设置cid，请忽略该信息")
                    cls.currentClientid = cid
                    break
                if " Ctrip   : clientID" in line:
                    cid = line.split("=")[1].replace("\r", "").replace("\n", "").strip()
                    # cid = eval(line.split("jsonStr = ")[1])["acid"]
                    # cid = cid_line[len(cid_line)]
                    printUtil.printCaseDevice("logcat 获取到的 cid " + cid)
                    printUtil.printCaseDevice("如果case中已经单独设置cid，请忽略该信息")
                    cls.currentClientid = cid
                    break
            adb = ADB(serialno=DeviceInfo.device.serialno)
            adb.cmd("logcat -c")

        return cls.currentClientid


    def getClientSocket(self):
        cid=""
        try:
            res, sock, port = self.conDeviceForward(self)
            if not res:
                printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "。。。socket链接失败，")
                return False
            start_time = time.time()
            while True:
                msg = sock.recv(4)
                leng = int.from_bytes(msg, byteorder='big', signed=False)
                if leng and leng > 0:
                    msg = sock.recv(leng)
                    printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "。。。。。message11 ", msg)
                    msg = self.getTruncationMsg(self, msg, leng, sock)
                    if msg:
                        strMsg = msg.decode('utf-8')
                        printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "。。。。。message ", strMsg)
                        msg = json.loads(strMsg, strict=False)
                        if msg['msgType'] and msg['msgType'] == "info":
                            json = msg['msgContent']
                            strs = json.loads(json)
                            cid = strs["cid"]
                            break


                end_time = time.time()
                # 超时
                # if end_time - start_time > 5:
                #     break

        except Exception as e:

            printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), 'getServiceRequest - str(e):\t\t', str(e))
            printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), 'getServiceRequest - repr(e):\t', repr(e))
            printUtil.printCaseDevice('########################################################')
        finally:
            try:
                sock.close()
            except Exception as se:
                printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), 'reqget - str(e):\t\t', str(se))
        return cid

    @classmethod
    def getCurrentMockKey(cls):
        return cls.currentMockid

    @classmethod
    def setCurrentMockKey(cls, mockKey=""):
        cls.currentMockid = mockKey;

    @classmethod
    def getCurrentClientid(cls):
        return cls.currentClientid

    @classmethod
    def setCurrentClientid(cls, clientId):
        cls.currentClientid = clientId

    @classmethod
    def getSocketMessageDict(cls, type):
        if type in cls.socketMessageDict:
            res = cls.socketMessageDict[type]
            return res
        return ''

    @classmethod
    def clearSocketMessageDict(cls):
        cls.socketMessageDict.clear()
        cls.localMockNameList = []

    @classmethod
    def getBdsData(cls):
        return cls.bdsData

    @classmethod
    def setBdsData(cls, bdsData):
        cls.bdsData = bdsData

    @classmethod
    def clearBdsData(cls):
        cls.bdsData = ""
        cls.extensionForCase = None
        cls.primaryCaseId = "0"
        cls.currentTag = ""
        cls.currentLabelTag = ""

    @classmethod
    def setCirculateInfos(cls, currentCirculate, circulateTimes, TagLists, caseName=None):
        extention = Labconfig.extention
        cls.extensionForCase = extention
        print("CmtLogger.case_result_id" + str(CmtLogger.case_result_id))
        print("extention" + str(extention))
        if extention is not None and len(extention) > 0:
            # 有labelId的话只跑一个tag
            if "labelID" in extention and extention["labelID"] != 0:
                for i in TagLists:
                    if (str(extention["labelID"]) in i.split(":")[0].split(",")):
                        cls.currentCirculate = 1
                        cls.circulateTimes = 1
                        cls.TagLists = TagLists
                        cls.currentTag = i.split(":")[1]
                        cls.currentLabelTag = i
                        return
                cls.initDataWithoutExtention(currentCirculate, circulateTimes, TagLists)
            elif "uiCaseInfoList" in extention and extention["uiCaseInfoList"] != "":
                uiCaseInfoList = extention["uiCaseInfoList"]
                tagSelectLists = []
                for i in uiCaseInfoList:
                    if cls.primaryCaseId != "0" and i["id"] == cls.primaryCaseId:
                        tagSelectLists.extend(i["tags"])
                print(tagSelectLists)
                if len(tagSelectLists) > 0:
                    if len(tagSelectLists) == 1:
                        if tagSelectLists[0] == caseName:
                            cls.currentCirculate = currentCirculate
                            cls.circulateTimes = len(tagSelectLists)
                            cls.TagLists = tagSelectLists
                            cls.currentTag = cls.TagLists[0]
                            cls.currentLabelTag = cls.TagLists[0]
                            return
                    cls.currentCirculate = currentCirculate
                    cls.circulateTimes = len(tagSelectLists)
                    cls.TagLists = tagSelectLists
                    cls.currentTag = cls.TagLists[currentCirculate - 1].split(":")[1]
                    cls.currentLabelTag = cls.TagLists[currentCirculate - 1]
                    return
                else:
                    cls.initDataWithoutExtention(currentCirculate, circulateTimes, TagLists)

        else:
            cls.initDataWithoutExtention(currentCirculate, circulateTimes, TagLists)
            return

    @classmethod
    def initDataWithoutExtention(cls, currentCirculate, circulateTimes, TagLists):
        cls.currentCirculate = currentCirculate
        cls.circulateTimes = circulateTimes
        cls.TagLists = TagLists
        if cls.TagLists != []:
            cls.currentTag = TagLists[currentCirculate - 1].split(":")[1]
            cls.currentLabelTag = TagLists[currentCirculate - 1]
        else:
            cls.currentTag = ''
            cls.currentLabelTag = ''

    @classmethod
    def deleteLocalMockFile(cls):
        for item in cls.localMockNameList:
            os.remove(item)
        HandleInfo.localMockNameList = []

    @classmethod
    def getMetaInfo(cls):
        return cls.metaInfo

    @classmethod
    def setMetaInfo(cls, metaInfo):
        cls.metaInfo = metaInfo
        try:
            if metaInfo != None:
                config = cls.getMetaConfiginfo(cls)
                cls.mockKey = config.get('mockKey', '') if config != None else ''
        except Exception as e:
            print(e)

    @classmethod
    def getMockKey(cls):
        return cls.mockKey


    def getMetaConfiginfo(self):
        if self.metaInfo == None:
            return None
        config = self.metaInfo.get('config', None)
        return eval(config)
