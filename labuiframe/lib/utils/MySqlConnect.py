import re

import pymysql
import mysql.connector
from datetime import date, datetime, timedelta
import requests


class MySqlConnect:

    # 链接数据库
    def connentMysql(self, dbName):
        connect = pymysql.Connect(host='**************', port=3306, user='root', passwd='aA1^Sq70PcOY22gTouJd', db=dbName, charset='utf8mb4')
        return connect

    def connectATLSql(self, dbName):
        connect = pymysql.Connect(host='************', port=3306, user='root', passwd='ca!!Duty', db=dbName, charset='utf8mb4')
        return connect

    def connentMysqlTor(self, dbName):
        connect = mysql.connector.Connect(host='**************', port=3306, user='root', passwd='aA1^Sq70PcOY22gTouJd', db=dbName)
        return connect

    def connentDataBase(self, dbName):
        api = "http://htlmysticmare.fws.qa.nt.ctripcorp.com/api/chat/getDataBaseInfo"
        res = requests.get(api, timeout=60)
        if res.status_code != 200:
            raise Exception("Java API getDataBaseInfo Error, status code 错误, status code: {}, body: {}".format(
                res.status_code, res.content))
        if res.json().get("code") < 0:
            raise Exception("Java API getDataBaseInfo Error, body code 错误, status code: {}, body: {}".format(
                res.status_code, res.json().get("msg")))
        connect = mysql.connector.Connect(host='htltesttool.mysql.db.fat.qa.nt.ctripcorp.com', port=55111, user=res.json().get("data").get("pwdAccount"), passwd=res.json().get("data").get("pwdValue"), db=dbName, charset='utf8mb4')
        return connect

    # 修改case执行和实现状态
    def setCoreCase(self, result, caseName):

        try:
            connect = self.connentMysql('report')
            status = 0
            if result:
                status = 1
            cur = connect.cursor()
            sql = "UPDATE  report.core_case SET android_result=" + str(result) + " , android_status=" + str(
                status) + " WHERE case_name='" + caseName + "'"
            cur.execute(sql)
        except:
            print("Error: unable to fetch data")
        connect.commit()
        cur.close()
        connect.close()

    @classmethod
    def insertEarlyMorningUiScene(cls, sceneName="", className="", caseName="", stepIndex=1, stepName="", stepResult=False, imageAddress="", captureUrl=''):
        cal = 0
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            updatetime = datetime.now()

            if stepResult:
                cal = 1
            # sql = "update early_morning set scene_name='%s', classname='%s',step_name='%s', capture_url='%s', updatetime='%s', step_result=%s where casename='%s' and step_index=%s" % (sceneName, className, stepName, captureUrl, updatetime, stepResult, caseName, stepIndex)
            insert_column = 'insert into early_morning(scene_name, classname, casename, step_index, step_name, capture_url, updatetime, step_result) values (%s, %s, %s, %s, %s, %s, %s, %s)'
            data = ('%s' % sceneName, '%s' % className, '%s' % caseName, '%s' % stepIndex, '%s' % stepName, '%s' % captureUrl, '%s' % updatetime, '%s' % cal)
            cur.execute(insert_column, data)
        except  Exception as e:
            print('str(e):\t\t', str(e))
            print('repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()

    @classmethod
    def insertDataToTestToolTable(cls, sql, args=None):

        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            cur.execute(sql, args)
        except  Exception as e:
            print('str(e):\t\t', str(e))
            print('repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()

    @classmethod
    def insertDataToTestToolTableAndReturnId(cls, sql, args=None):
        last_row_id = 0
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            cur.execute(sql, args)
            last_row_id = cur.lastrowid
            print(last_row_id)
        except  Exception as e:
            print('str(e):\t\t', str(e))
            print('repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return last_row_id

    def insertCaseDetail(self, caseresult_id, scene_name, hotel_id, room_id, order_id, status, remark, runby):
        connect = self.connectATLSql('cmt')
        cur = connect.cursor()
        insert_column = 'INSERT INTO m_caseresult_detail(caseresult_id,scene_name,hotel_id,room_id,order_id, status, remark, runby) VALUES(%s,%s,%s,%s,%s, %s, %s, %s)'
        insert_data = ('%s' % caseresult_id, '%s' % scene_name, '%s' % hotel_id, '%s' % room_id, '%s' % order_id, '%s' % status, '%s' % remark, '%s' % runby)
        try:
            cur.execute(insert_column, insert_data)
        except:
            print("Error: unable to fetch data")
        connect.commit()
        cur.close()
        connect.close()

    ##configType =1 实验 =2是增量开关
    @classmethod
    def queryAbConfig(cls, configType=1, page=''):
        values = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            type = configType
            isCtrip = 'c-all' if 'c-' in page else 't-all'

            sql = "SELECT configname,configvalue FROM testtool.ui_ab_config where valid = 1 and configtype = " + str(
                type) + " and effetpage in ('" + page + "','" + isCtrip + "')"
            cur.execute(sql)
            for row in cur.fetchall():
                values.append(list(row))
        except Exception as e:
            print('queryAbConfig，str(e):\t\t', repr(e))

        try:
            connect.commit()
            cur.close()
            connect.close()
        except Exception as es:
            print('queryAbConfig，repr(e):\t', repr(es))

        return values

    # 根据设备号获取c和t分别的cid
    @classmethod
    def queryClientId(cls, appid, deviceNo=""):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            selectName = 'c_cid' if appid == "99999999" else 't_cid'
            sql = "SELECT " + selectName + " FROM testtool.auto_test_ui_config_cid where device_serial='" + str(
                deviceNo) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(row[0])
        except Exception as e:
            print('queryClientId repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

        # 查询表event_tracking

    @classmethod
    def queryEventTrackingTable(cls, ruleId):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT tracekey, servicename, jsonpath, ruletype, rulevalue FROM testtool.event_tracking where id='" + str(
                ruleId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def queryEventTraceRuleCheck(cls, ruleId):
        result = ''
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT traceKey FROM testtool.event_tracking_check_rule where ruleId='" + str(ruleId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                return row[0]
        except Exception as e:
            print('queryEventTraceRuleCheck(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def queryAllValidEventTraceRule(cls, caseName):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            sql = "SELECT traceKey FROM testtool.event_tracking_check_rule where valid = 1 and caseName = '" + str(caseName) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('queryAllValidEventTraceRule(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def queryAllControlTreeByModuleId(cls, moduleId):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            sql = "select id,originalControlTree from testtool.ui_create_control_tree where moduleId = " + str(moduleId)
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('queryAllValidEventTraceRule(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def queryEventTraceByRuleId(cls, ruleId):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT id, tracekey, servicename, jsonpath, ruletype, rulevalue ,checkRuleType FROM testtool.event_tracking where ruleId='" + str(
                ruleId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def checkTraceKeyValid(cls, ruleId):
        result = 0
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT valid FROM testtool.event_tracking_check_rule where ruleId='" + str(
                ruleId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result = row[0]
                break
        except Exception as e:
            print('str(e):\t\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    # 写入结果到event_tracking_mock表
    @classmethod
    def writeEventTrackingMockTable(cls, mockKey, jsonpathRealValue, ruleId, jsonPath, serviceName):

        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            insert_column = 'INSERT INTO testtool.event_tracking_mock(mockkey, jsonpathValue, ruleId, jsonpath, servicename) VALUES(%s, %s, %s, %s, %s)'
            insert_data = ('%s' % mockKey, '%s' % jsonpathRealValue, '%s' % ruleId, '%s' % jsonPath, '%s' % serviceName)
            cur.execute(insert_column, insert_data)
        except Exception as e:
            print('str(e):\t\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()

    # 查询表event_tracking_mock
    @classmethod
    def queryEventTrackingMockTable(cls, ruleId, jsonPath, serviceName):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT count(*), jsonpathValue, updatetime FROM testtool.event_tracking_mock where ruleId='" + str(
                ruleId) + "' and jsonpath='" + str(jsonPath) + "' and servicename='" + str(serviceName) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
            if result[0][2] != None:
                updatetime = result[0][2]
                # 表中字段updatetime与系统时间比，大于24个小时，删除该ruleId的数据
                if (datetime.now() - updatetime).days >= 1:
                    try:
                        sql = "delete from testtool.event_tracking_mock where ruleId='" + str(ruleId) + "' and jsonpath='" + str(jsonPath) + "' and servicename='" + str(serviceName) + "'"
                        cur.execute(sql)
                        # 提交到数据库执行
                        connect.commit()
                        result = []
                    except:
                        # 发生错误时回滚
                        connect.rollback()
        except Exception as e:
            print('str(e):\t\t', str(e))
            print('repr(e):\t', repr(e))
        cur.close()
        connect.close()
        return result

    # 查询表event_tracking_mock
    @classmethod
    def queryTraceResponseValue(cls, rulePrimayId):

        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            cur.execute("SELECT  jsonpathValue, updatetime FROM testtool.event_tracking_mock where ruleId='" + str(rulePrimayId) + "'")
            result = cur.fetchall()
            table = list(result)
            if (len(table)):
                return table["jsonpathValue"][0]
        except Exception as e:
            print('str(e):\t\t', repr(e))
        cur.close()
        connect.close()
        return ""

    # 查询表event_tracking_result
    @classmethod
    def queryEventTrackingResultTable(cls, ruleId, taskId, caseId):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()

            sql = "SELECT count(*) FROM testtool.event_tracking_result where ruleid='" + str(ruleId) + "' and taskid='" + str(taskId) + "' and caseid='" + str(caseId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(row[0])
        except Exception as e:
            print('str(e):\t\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    # 更新主表落地结果
    @classmethod
    def updateEventTacePrimryResult(cls, ruleId, taskId, caseId, result, traceKey, traceValue=''):
        traceResult = cls.queryTraceResultPrimayTable(cls, ruleId, taskId, caseId)
        if len(traceResult) > 0:
            cls.updateTraceResultPrimayTable(cls, traceResult[0][0], result, traceKey, traceValue)
        else:
            cls.insertTraceResultPrimayTable(cls, ruleId, taskId, caseId, result, traceKey)
        return cls.getTraceResultPrimayTableId(cls, ruleId, taskId, caseId)

    # 查询结果主表的主键

    def getTraceResultPrimayTableId(self, ruleId, taskId, caseId):

        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            sql = "SELECT id FROM testtool.event_trace_primay_result where ruleId = '" + str(ruleId) + "' and taskId = '" + str(taskId) + "' and caseId = '" + str(caseId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                return row[0]
        except Exception as e:
            print('str(e):\t\t', repr(e))

        connect.commit()
        cur.close()
        connect.close()
        return 0

    # 更新字段结果表
    @classmethod
    def updateEventTaceDetailResult(cls, primaryId, jsonPath, result, realVaLue, exceptVale, message, mockKey, checkType):
        queryResult = cls.queryTraceResultDetailTable(cls, primaryId, jsonPath);
        if len(queryResult) > 0:
            cls.updateTraceResultDetailTable(cls, queryResult[0][0], result)
        else:
            cls.insertTraceResultDetailTable(cls, primaryId, jsonPath, result, realVaLue, exceptVale, message, mockKey, checkType)

    # 这里获取结果主表的表数据
    def queryTraceResultPrimayTable(self, ruleId, taskId, caseId):
        result = []
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()

            sql = "SELECT id,ruleId FROM testtool.event_trace_primay_result where ruleId = '" + str(ruleId) + "' and taskId = '" + str(taskId) + "' and caseId = '" + str(caseId) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('str(e):\t\t', repr(e))

        connect.commit()
        cur.close()
        connect.close()
        return result

    # 结果主表  插入数据
    def insertTraceResultPrimayTable(self, ruleId, taskId, caseId, result, traceKey):

        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            insert_column = 'INSERT INTO testtool.event_trace_primay_result(ruleId, taskId, caseId, result,traceKey) VALUES(%s, %s, %s, %s, %s)'
            insert_data = ('%s' % ruleId, '%s' % taskId, '%s' % caseId, '%s' % result, '%s' % traceKey)

            cur.execute(insert_column, insert_data)
        except  Exception as e:
            print('insertTraceResultPrimayTable,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    def updateTraceResultPrimayTable(self, id, result, traceKey, traceValue):
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            insert_column = "update testtool.event_trace_primay_result set result = %s,traceKey = %s,traceValue=%s where id = %s"
            insert_data = ('%s' % result, '%s' % traceKey, '%s' % traceValue, '%s' % id)
            cur.execute(insert_column, insert_data)
        except  Exception as e:
            print('updateTraceResultPrimayTable,(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    # 这里获取 结果明细表数据
    def queryTraceResultDetailTable(self, primaryId, jsonPath):
        result = []
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()

            sql = "SELECT * FROM testtool.event_trace_detail_result where primaryId = %s and jsonPath= %s"
            insert_data = ('%s' % primaryId, '%s' % jsonPath)
            cur.execute(sql, insert_data)
            for row in cur.fetchall():
                result.append(list(row))
        except Exception as e:
            print('queryTraceResultDetailTable,str(e):\t\t', str(e))

        connect.commit()
        cur.close()
        connect.close()
        return result

    # 结果主表  插入数据
    def insertTraceResultDetailTable(self, primaryId, jsonPath, result, realVaLue, exceptValue, message, mockKey, checkType):

        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            insert_column = 'INSERT INTO testtool.event_trace_detail_result(primaryId,  jsonPath, result,realVaLue, exceptValue, message, mockKey, ruleCheckType) VALUES(%s, %s, %s, %s, %s, %s, %s, %s)'
            insert_data = ('%s' % primaryId, '%s' % jsonPath, '%s' % result, '%s' % realVaLue, '%s' % exceptValue, '%s' % message, '%s' % mockKey, '%s' % checkType)

            cur.execute(insert_column, insert_data)
        except  Exception as e:
            print('insertTraceResultDetailTable,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    def updateTraceResultDetailTable(self, id, result):

        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            insert_column = "update testtool.event_trace_detail_result set result = " + str(result) + " where id = " + str(id) + ""
            cur.execute(insert_column)
        except  Exception as e:
            print('updateTraceResultDetailTable,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    @classmethod
    def queryYunAirTestById(cls, id):
        result = []
        try:
            connect = cls.connentMysql(cls, 'testtool')
            cur = connect.cursor()
            sql = "SELECT * FROM testtool.ui_create_case_commit where id ='" + str(id) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                # case = " | ".join(row)
                # print('==>'+case)
                result.append(row)
        except Exception as e:
            print('queryClientId repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    @classmethod
    def queryYunAirTestByIdNew(cls, id):
        mydb = cls.connentDataBase(cls, 'htltesttooldb')
        mycursor = mydb.cursor(dictionary=True)
        data = None
        try:

            sql = "SELECT * FROM ui_create_case_commit where id ='" + str(id) + "'"
            mycursor.execute(sql)
            data = mycursor.fetchone()
            for item in data:
                if isinstance(data[item], bytes):
                    data[item] = data[item].decode('utf-8')
            # print(data)
            # id = data.get('id', '')
            # if isinstance(data.get('codeDetail', ''), bytes):
            #     val = data.get('codeDetail', '').decode('utf-8')
            #     print(val)
            # print(id, '\n', data.get('codeDetail', ''))

        except Exception as e:
            print('queryClientId repr(e):\t', repr(e))

        mycursor.close()
        mydb.close()
        return data

    # 更新已有的元素图片信息到数据集
    # elementPocoImgURL 控件本身截图 elementPositionImgURL 控件位置所在标记截图  mockkey  appVersion 版本号
    @classmethod
    def updateElementImage(cls, dbElement, elementPocoImgURL, elementPositionImgURL):
        connect = cls.connentMysql(cls, 'testtool')
        cur = connect.cursor()
        try:

            id = dbElement.get("id", 0)
            if id == 0:
                return

            update_column = "update testtool.ui_create_module_element set elementPocoImgURL = %s, elementPositionImgURL= %s  where id = %s"
            update_data = ('%s' % elementPocoImgURL, '%s' % elementPositionImgURL, '%s' % id)
            cur.execute(update_column, update_data)
        except  Exception as e:
            print('updateElementImage,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    @classmethod
    def insertNewElementInfo(cls, click_poco_name, poco_text, editable, is_union, click_poco_img, click_positon_img, resolution, click_poco_position, appversion, mockkey):
        datebase = cls.connentMysql(cls, 'testtool')
        cur = datebase.cursor()
        primaryId = 0
        try:
            insertColumn = 'insert into ui_create_click_img(click_poco_name,poco_text,editable,is_union,click_poco_img,click_positon_img,resolution,click_poco_position,appversion,mockkey) values(%s,%s,'+str(editable)+','+str(is_union)+',%s,%s,%s,%s,%s,%s)'
            insertData = ('%s' % click_poco_name, '%s' % poco_text, '%s' % click_poco_img,  '%s' % click_positon_img, '%s' % resolution, '%s' % click_poco_position, '%s' % appversion, '%s' % mockkey)
            cur.execute(insertColumn, insertData)
            primaryId = datebase.insert_id()
        except  Exception as e:
            print('insertNewElementInfo,str(e):\t\t', str(e))
        datebase.commit()
        cur.close()
        datebase.close()
        return primaryId

    @classmethod
    def updateElementClickImg(cls,imgPrimaryId,id):
        datebase = cls.connentMysql(cls, 'testtool')
        cur = datebase.cursor()
        try:
            updateColumn = 'update ui_create_module_element set clickImgID = %s where id = %s'
            updateData = ('%s' % imgPrimaryId, '%s' % id)
            cur.execute(updateColumn, updateData)
        except  Exception as e:
            print('updateElementClickImg,str(e):\t\t', str(e))
        datebase.commit()
        cur.close()
        datebase.close()

    # 根据元素ID查询元素信息
    @classmethod
    def queryElementById(cls, elementId):
        data = None
        mydb = cls.connentMysqlTor(cls, 'testtool')
        cur = mydb.cursor(dictionary=True)
        try:

            sql = "SELECT * FROM testtool.ui_create_module_element where elementID ='" + str(elementId) + "' and status = 1 limit 1"
            cur.execute(sql)
            data = cur.fetchone()

        except Exception as e:
            print('queryElementById repr(e):\t', repr(e))
        cur.close()
        mydb.close()
        return data

    # event_tracking_result结果表
    @classmethod
    def writeEventTrackingResultTable(cls, mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                      jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue):
        connect = cls.connentMysql(cls, 'testtool')
        cur = connect.cursor()
        updatetime = datetime.now()
        insert_column = 'INSERT INTO testtool.event_tracking_result(mockkey, taskid, caseid, casename, tracekey, result, message, ruleid, jsonpathrealvalue, jsonpathexpectvalue, updatetime, tracekeyjsonpathvalue) VALUES(%s,%s,%s,%s,%s, %s, %s, %s, %s, %s, %s, %s)'
        insert_data = (
            '%s' % mockKey, '%s' % taskId, '%s' % caseId, '%s' % caseName, '%s' % traceKey, '%s' % result,
            '%s' % message, '%s' % ruleId, '%s' % jsonpathRealValue, '%s' % jsonpathExpectValue, '%s' % updatetime, '%s' % tracekeyjsonpathvalue)
        try:
            cur.execute(insert_column, insert_data)
        except  Exception as e:
            print('str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    # 更新表表event_tracking_result
    @classmethod
    def updateEventTrackingResultTable(cls, mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                       jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue):
        connect = cls.connentMysql(cls, 'testtool')
        cur = connect.cursor()
        updatetime = datetime.now()
        sql = "DELETE FROM testtool.event_tracking_result where ruleid='" + str(ruleId) + "' and taskid='" + str(taskId) + "' and caseid='" + str(caseId) + "'"
        try:
            cur.execute(sql)
            try:
                insert_column = 'INSERT INTO testtool.event_tracking_result(mockkey, taskid, caseid, casename, tracekey, result, message, ruleid, jsonpathrealvalue, jsonpathexpectvalue, updatetime, tracekeyjsonpathvalue) VALUES(%s,%s,%s,%s,%s, %s, %s, %s, %s, %s, %s, %s)'
                insert_data = (
                    '%s' % mockKey, '%s' % taskId, '%s' % caseId, '%s' % caseName, '%s' % traceKey, '%s' % result,
                    '%s' % message, '%s' % ruleId, '%s' % jsonpathRealValue, '%s' % jsonpathExpectValue, '%s' % updatetime, '%s' % tracekeyjsonpathvalue)
                cur.execute(insert_column, insert_data)
                connect.commit()
            except:
                connect.rollback()
        except Exception as e:
            print('str(e):\t\t', str(e))
            print('repr(e):\t', repr(e))
        cur.close()
        connect.close()

    # appperf_aws实例表 插入数据
    def insertAppperfOnAwsExampleTable(self, instanceIds, instanceIp, status, updateTime, createTime, regionName):
        try:
            connect = self.connentMysql('testtool')
            cur = connect.cursor()
            insert_column = 'INSERT INTO testtool.appperf_on_aws_example(instanceIds,instanceIp,status,updateTime,createTime,region_name) VALUES(%s, %s, %s, %s, %s, %s)'
            insert_data = (
                '%s' % instanceIds, '%s' % instanceIp, '%s' % status, '%s' % updateTime, '%s' % createTime, '%s' % regionName)
            cur.execute(insert_column, insert_data)
        except Exception as e:
            print('insertAppperfOnAwsExampleTable,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    # appperf_aws实例表 更新数据
    def updateAppperfOnAwsExampleTable(self, instanceIds, status, updateTime):
        try:
            connect = self.connentMysql('testtool')
            cur = connect.cursor()
            insert_column = "update testtool.appperf_on_aws_example set status = " + str(
                status) + ",updateTime='" + updateTime + "' where instanceIds = '" + instanceIds + "'"
            cur.execute(insert_column)
        except  Exception as e:
            print('updateTraceResultDetailTable,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()

    # appperf_aws实例表 获取status=1的数据
    @classmethod
    def queryAppperfOnAwsExampleTable(self, status):
        result = []
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            sql = "SELECT instanceIds,region_name FROM testtool.appperf_on_aws_example where status='" + str(
                status) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(row)
        except Exception as e:
            print('queryClientId repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    # appperf_aws实例表 获取 当前使用的区域数量
    @classmethod
    def queryAppperfOnAwsReginNameTable(self, status):
        result = []
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            sql = "SELECT distinct(region_name) FROM testtool.appperf_on_aws_example where status='" + str(
                status) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(row[0])
        except Exception as e:
            print('queryClientId repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    # appperf_aws实例表 获取不同region_name对应的instances，字典返回
    def queryAppperfOnAwsReginNameExampleTable(self, status):
        result = self.queryAppperfOnAwsExampleTable(status)
        regionList = self.queryAppperfOnAwsReginNameTable(status)
        dictInstance = {}
        for region in regionList:
            instances = []
            for re in result:
                if (region == re[1]):
                    instances.append(re[0])
            dictInstance[region] = instances
        return dictInstance

    @classmethod
    def queryBDDScriptData(self, labelId, mockKey):
        result = []
        try:
            connect = self.connentMysql(self, 'testtool')
            cur = connect.cursor()
            sql = "SELECT data FROM testtool.ui_create_bdd_script where caseLableID='" + str(
                labelId) + "' and mockKey='" + str(mockKey) + "'"
            cur.execute(sql)
            for row in cur.fetchall():
                result.append(row)
        except Exception as e:
            print('queryBDDScriptData repr(e):\t', repr(e))
        connect.commit()
        cur.close()
        connect.close()
        return result

    # 根据uid以及platform获取登录信息
    @classmethod
    def get_uid_login_info(cls,uid:str,platform:int):
        login_info_list=[]
        connect = cls.connentMysql(cls,'testtool')
        cur = connect.cursor()
        sql = "SELECT duid,ticket FROM testtool.uid_log_in_info where uid ='" + str(uid) + "' and platform = " + str(platform)
        result=()
        try:
            cur.execute(sql)
            result = cur.fetchone()
            connect.commit()
        except :
            print("Error: unable to fetch data")
        finally:
            cur.close()
            connect.close()
            return result

    @classmethod
    #存储获取到的web登录信息
    def save_uid_login_info(cls, uid: str, platform: int,ticket: str,duid: str,status: int):
        connect = cls.connentMysql(cls, 'testtool')
        cur = connect.cursor()
        if status:
            sql = "UPDATE testtool.uid_log_in_info SET duid = %s, ticket = %s WHERE uid = %s AND platform = %s"
            try:
                cur.execute(sql,(duid, ticket, uid, platform))
            except Exception as e:
                print('save_uid_login_info error,str(e):\t\t', str(e))
        else:
            sql = "INSERT INTO testtool.uid_log_in_info (uid, platform, duid,ticket) VALUES (%s, %s, %s, %s)"
            insert_data = ('%s' % uid, '%s' % platform, '%s' % duid, '%s' % ticket)
            try:
                cur.execute(sql, insert_data)
            except Exception as e:
                print('save_uid_login_info error,str(e):\t\t', str(e))
        connect.commit()
        cur.close()
        connect.close()


