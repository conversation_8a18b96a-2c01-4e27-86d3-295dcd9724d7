import math

from playwright.sync_api import Locator, <PERSON><PERSON><PERSON><PERSON><PERSON>, Page
from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.image_utils import snapshot as imageSnapshot
from airtest.core.android.adb import ADB
import threading
import base64
from datetime import datetime
from airtest.core.android.recorder import Recorder
from airtest import aircv
import urllib
from airtest.core.cv import Template
import cv2
from PIL import Image, PngImagePlugin
from io import BytesIO
import requests


class Capture():
    # 设置全局图片精度为80
    ST.SNAPSHOT_QUALITY = 80

    # def __init__(self, case):
    #     self.case = case

    @classmethod
    def setCase(cls, case):
        cls.case = case

    @classmethod
    def getProject_root(cls, case):
        cls.case = case
        return cls.case.project_root

    @classmethod
    def getScreenShot(cls, filename=None, upload=True):
        t = time.time()
        default_directory = '{}{}{}'.format(cls.case.project_root, os.sep, 'screenshot')
        if not os.path.exists(default_directory):
            os.makedirs(default_directory)

        if filename == None:
            default_path = '{}{}{}'.format(default_directory, os.sep,
                                           'screenshot_' + str(int(t)) + '.jpg')
        else:
            default_path = '{}{}{}'.format(default_directory, os.sep, filename if '.jpg' in filename else filename + '.jpg')
            path = os.path.split(default_path)
            if not os.path.exists(path[0]):
                os.makedirs(path[0])

        path = imageSnapshot(default_path)
        print("screenhot:" + str(default_path))
        if upload:
            CmtLogger.logImage(default_path)
        return default_path

    # 取图片全路径，指定screen目录下的路径
    @classmethod
    def getControlImagePath(cls, filename=None):
        name = datetime.now().strftime('%Y%m%d%H%M%S')
        default_directory = '{}{}{}{}{}'.format(cls.case.project_root, os.sep, 'screenshot', os.sep, 'cropImg')
        if not os.path.exists(default_directory):
            os.makedirs(default_directory)

        if filename == None:
            default_path = '{}{}{}'.format(default_directory, os.sep, 'cropImg_' + str(name) + '.png')
        else:
            default_path = '{}{}{}'.format(default_directory, os.sep,
                                           cls.getImgeName(cls, filename))
            path = os.path.split(default_path)
            if not os.path.exists(path[0]):
                os.makedirs(path[0])
        return default_path

    # 获取图片下线的路径
    @classmethod
    def getImgDownloadUrl(cls, filename=None):
        name = datetime.now().strftime('%Y%m%d%H%M%S')
        default_directory = '{}{}{}{}{}'.format(cls.case.project_root, os.sep, 'screenshot', os.sep, 'compare')
        if not os.path.exists(default_directory):
            os.makedirs(default_directory)

        if filename == None:
            default_path = '{}{}{}'.format(default_directory, os.sep, 'compareImg_' + str(name) + '.png')
        else:
            default_path = '{}{}{}'.format(default_directory, os.sep, cls.getImgeName(cls, filename))
            path = os.path.split(default_path)
            if not os.path.exists(path[0]):
                os.makedirs(path[0])
        return default_path

    def getImgeName(self, filename=''):
        if '.jpg' in filename:
            return filename
        if '.png' in filename:
            return filename
        return filename + '.png'


    # 指定控件，返回控件的上下左右坐标
    @classmethod
    def getSidePosByNode(cls, node):
       return cls.get_side_pos(node.get_position(), node.get_size())
    # 指定位置和控件大小，返回控件的上下左右坐标
    @classmethod
    def get_side_pos(cls, pos, size):
        # 计算控件区域的上下左右的坐标,保留两位小数
        left = round(pos[0] - size[0] / 2, 2)
        right = round(pos[0] + size[0] / 2, 2)
        top = round(pos[1] - size[1] / 2, 2)
        bottom = round(pos[1] + size[1] / 2, 2)
        return str(top) + "," + str(bottom) + "," + str(left) + "," + str(right)

    # 根据控件获取 局部截图 ，返回图片的url
    @classmethod
    def screenCropImageUrlByNode(cls, node, filename=None):
        if isinstance(node, Locator):
            if not node.is_visible():
                print("图片URL:{}".format(""))
                return ''
            # 保存局部截图到log文件夹中
            path = Capture.getControlImagePath(filename)
            # web 元素局部截图
            node.screenshot(path=path, type='png')

        else:
            if not node.exists():
                print("图片URL:{}".format(""))
                return ''
            pos = node.get_bounds()  # (top, right, bottom, left)
            path = cls.cropImage(pos, filename)
        if path is None:
            print("图片URL:{}".format(""))
            return ''
        if filename == None:
            name = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = name + '.png'
        url = cls.getImgOnlineUrl(cls, path, filename, 'png')
        print("图片URL:{}".format(url))
        return url

    # 根据控件控件的位置信息 局部截图 ，返回图片的url
    @classmethod
    def getScreenshotCropUrl(cls, pos: tuple, filename=None):
        path = cls.cropImage(pos, filename)
        if path is None:
            return ''
        if filename == None:
            name = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = name + '.png'

        return cls.getImgOnlineUrl(cls, path, filename, 'png')

    # 根据控件控件的位置信息 局部截图 ，返回图片的url
    @classmethod
    def getScreenshotCropUrl2(cls, pos: tuple, size: tuple, filename=None):
        path = cls.cropImage2(pos, size, filename)
        if path is None:
            return ''
        if filename == None:
            name = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = name + '.png'

        return cls.getImgOnlineUrl(cls, path, filename, 'png')

    # 局部截图 ,这里返回本地图片路径
    @classmethod
    def cropImage2(cls, pos, size, filename=None):
        width, height = cls.getScreenSize()
        screen = G.DEVICE.snapshot(quality=99)
        # 局部截图
        pos0 = int(pos[0] * width)
        pos1 = int(pos[1] * height)
        size0 = int(size[0] * width)
        size1 = int(size[1] * height)
        # 局部截图，（0，160）是局部截图左上角的绝对坐标，（1067，551）是右下角的绝对坐标
        rect = [int(pos0 - size0 / 2), int(pos1 - size1 / 2), int(pos0 + size0 / 2), int(pos1 + size1 / 2)]
        local_screen = aircv.crop_image(screen, rect)

        # 保存局部截图到log文件夹中
        path = Capture.getControlImagePath()
        # print('===》path\n', path)
        try:
            aircv.imwrite(path, local_screen)
            return path
        except Exception as e:
            print("save screen error:", repr(e))
        return None

    # 局部截图 ,这里返回本地图片路径
    @classmethod
    def cropImage(cls, pos: tuple, filename=None):
        screen = G.DEVICE.snapshot(quality=99)
        width, height = cls.getScreenSize()
        # 局部截图
        left = int(pos[3] * width)
        top = int(pos[0] * height)
        right = int(pos[1] * width)
        bottom = int(pos[2] * height)
        # 局部截图，（0，160）是局部截图左上角的绝对坐标，（1067，551）是右下角的绝对坐标
        rect = [int(left), int(top), int(right), int(bottom)]
        local_screen = aircv.crop_image(screen, rect)

        # 保存局部截图到log文件夹中
        path = Capture.getControlImagePath(filename)

        try:
            aircv.imwrite(path, local_screen)
            return path
        except Exception as e:
            print("save screen error:", repr(e))
        return None

    # 获取屏幕的宽高
    @classmethod
    def getScreenSize(cls):
        if G.DEVICE.display_info['orientation'] in [1, 3]:
            height = G.DEVICE.display_info['width']
            width = G.DEVICE.display_info['height']
        else:
            height = G.DEVICE.display_info['height']
            width = G.DEVICE.display_info['width']
        return width, height

    @classmethod
    def markScreenshotFrame(cls, element, filename=None):
        if not element.exists():
            return None
        width, height = cls.getScreenSize()
        print(width, height)
        # 注意返回的顺序是[top, right, bottom, left]
        pos = element.get_bounds()
        return cls.markScreenshot(filename, math.ceil(pos[3] * width), math.ceil(pos[1] * width), math.ceil(pos[0] * height), math.ceil(pos[2] * height))

    # 指定控件画框，并截图返回图片的url-这里是本地图片路径
    @classmethod
    def markScreenshot(cls, filename=None, xmin=100, xmax=200, ymin=100, ymax=300):
        # screen_path = cls.getScreenShot(filename)
        img = G.DEVICE.snapshot(quality=99)
        # img = cv2.imread(screen_path)
        cv2.rectangle(img, (xmin, ymin), (xmax, ymax), (0, 0, 255), 8)
        cv2.rectangle(img, (xmin, ymax), (xmax, ymin), (0, 0, 255), 8)
        # name = datetime.now().strftime('%Y%m%d%H%M%S')
        # screen_path = screen_path.replace('.jpg', '') +str(name)+ '_frame.jpg'
        screen_path = cls.getControlImagePath(filename);
        cv2.imwrite(screen_path, img)
        sleep(1)
        try_log_screen(img)

        # 将文件放到log文件夹下
        # G.LOGGER.log("function", {"name": "try_log_screen", "traceback": None, "desc": "shark翻译",
        #                           "call_args": {"screen": screen_path, "quality": '', "max_size": ''},
        #                           "ret": {"screen": screen_path, "resolution": [1080, 2400]}}, depth=1)
        # 将文件放到log文件夹下 并且将文件输出到报告中
        # G.LOGGER.log("function", {"name": "try_log_screen","traceback": None,"desc":"shark翻译", "call_args": {"screen": '', "quality": '', "max_size": ''}, "ret": {"screen": screen_path,"resolution": [1080, 2400]} },depth=2 )
        # G.LOGGER.log("info", {"name":"shark翻译", "traceback": None, "desc": "shark翻译"}, depth=1)
        # CmtLogger.logImage(screen_path)
        return screen_path

    # 指定控件画框，并截图返回图片的url，这里是网络图片地址
    @classmethod
    def markScreenshotUrlByNode(cls, element, filename=None):
        path = cls.markScreenshotFrame(element, filename)
        if path == None or path == '':
            return ''
        return cls.getImgOnlineUrl(cls, path, filename, 'png')

    # getImgOnlineUrl
    @classmethod
    def getScreenShotFileName(cls, filename=None):
        t = time.time()
        default_directory = '{}{}{}'.format(cls.case.project_root, os.sep, 'screenshot')
        if not os.path.exists(default_directory):
            os.makedirs(default_directory)

        if filename == None:
            default_path = '{}{}{}'.format(default_directory, os.sep,
                                           'screenshot_watcher_' + str(int(t)) + '.jpg')
        else:
            default_path = '{}{}{}'.format(default_directory, os.sep,
                                           filename)
            path = os.path.split(default_path)
            if not os.path.exists(path[0]):
                os.makedirs(path[0])

        path = imageSnapshot(default_path)
        print("screenhot:" + str(default_path))
        filename = CmtLogger.logImage(default_path)
        print("filename:" + filename)
        return filename

    # 取图片全路径
    # 参数 filename 为文件名
    @classmethod
    def fullPicturePath(cls, filename):
        default_directory = '{}{}{}{}{}'.format(cls.case.project_root, os.sep, 'res', os.sep, 'img')
        return '{}{}{}'.format(default_directory, os.sep, filename)

    @classmethod
    def screenrecord_start(cls, file_name, size=None, time_limit=180, bit_rate=4000000):
        t = threading.Thread(target=cls.run_screenrecord_start, args=(file_name, size, time_limit, bit_rate))
        t.setDaemon(True)
        t.start()

    @classmethod
    def run_screenrecord_start(cls, file_name, size=None, time_limit=180, bit_rate=4000000):
        file_name = str(base64.b64encode(file_name.encode('utf-8')), 'utf-8').replace("/", "")
        shell_command = "shell screenrecord --size {} --time-limit {} --bit-rate {} /sdcard/{}.mp4".format(size,
                                                                                                           time_limit,
                                                                                                           bit_rate,
                                                                                                           file_name)
        if size is None:
            shell_command = "shell screenrecord --time-limit {} --bit-rate {} /sdcard/{}.mp4".format(time_limit,
                                                                                                     bit_rate,
                                                                                                     file_name)
        ADB(serialno=DeviceInfo.deviceName).cmd(shell_command)

    @classmethod
    def screenrecord_end(cls, file_name):
        adb = ADB(serialno=DeviceInfo.deviceName)
        shell_command = "shell ps -A | grep screenrecord"
        process_info = None
        try:
            process_info = adb.cmd(shell_command)
        except Exception as e:
            print(e)
        if process_info is not None and len(process_info) > 0:
            info_list = process_info.split(" ")
            while "" in info_list:
                info_list.remove("")
            if len(info_list) > 1:
                shell_command = "kill -s 2 {}".format(info_list[1])
                p = adb.start_shell(shell_command)
                p.wait()
        sleep(3)
        default_directory = '{}{}{}{}{}'.format(cls.case.project_root, os.sep, 'res', os.sep, 'video')
        if not os.path.exists(default_directory):
            os.makedirs(default_directory)
        video_file_path = '{}{}{}.mp4'.format(default_directory, os.sep, file_name)
        if os.path.exists(video_file_path):
            os.remove(video_file_path)
        file_name_in_mobile = str(base64.b64encode(file_name.encode('utf-8')), 'utf-8').replace("/", "")
        pullvideo_command = "pull /sdcard/{}.mp4 {}".format(file_name_in_mobile, video_file_path)
        adb.cmd(pullvideo_command)
        return video_file_path

    @classmethod
    def getImgUrl(cls, imageAddress="orderDetail"):
        captureUrl = ''
        if imageAddress == '':
            return captureUrl
        name = datetime.now().strftime('%Y%m%d%H%M%S')
        default_path = '{}{}{}'.format(imageAddress, os.sep,
                                       str(name) + '.png')
        imgname = str(name) + '.png'
        img = cls.getScreenShot(default_path)
        return cls.getImgOnlineUrl(cls, img, imgname)

    @classmethod
    def getImgUrlForWeb(cls,page:Page,imageAddress="orderDetail"):
        captureUrl = ''
        if imageAddress == '':
            return captureUrl
        name = datetime.now().strftime('%Y%m%d%H%M%S')
        default_path = '{}{}{}'.format(imageAddress, os.sep,
                                       str(name) + '.png')
        imgname = str(name) + '.png'
        page.screenshot(path=default_path, type='png', scale="css")
        return cls.getImgOnlineUrl(cls,default_path, imgname, 'png')

    # 对失败case进行截图,并获取url
    @classmethod
    def getImgUrlForFailedCase(cls, self):
        if any(base.__name__ == 'WebBaseCase' for base in self.__class__.__bases__):
            screenshot = self.get_page_screenshot(scale="css")
            # 保存原始截图，并进行压缩
            img = Image.open(BytesIO(screenshot))
            timestamp = int(time.time())
            screenshot_path = self.save_compressed_image(img, f"screenshot_{timestamp}.png")
        else:
            screenshot = self.take_screenshot()
            timestamp = int(time.time())
            screenshot_path = self.save_image(screenshot, f"annotated_{timestamp}.png")

        # 将图像上传到服务器，获取图片url
        image_url = Capture.getImgOnlineUrl(self, screenshot_path, env="fws")
        print(image_url)
        return

    # 获取上传后图片的地址
    def getImgOnlineUrl(self, imgPath, imgname='test.png', imgType='jpg', env="pro"):
        try:
            if os.path.exists(imgPath) is False:
                return ""
            imgText = 'jpeg' if imgType == 'jpg' else 'png'
            with open(imgPath, "rb") as f:
                base64_data = base64.b64encode(f.read())
                base64str = "data:image/" + imgText + ";base64," + str(base64_data, 'utf-8')
                # print(base64str)
            url = 'http://offline.fx.ctripcorp.com/soa2/22336/upLoadingImgData'

            data = {
                'imageInfos': [{
                    'base64str': base64str,
                    'imgtype': 'image/' + imgText + '',
                    'imgname': imgname}],
                'accesstype': 'int'
            }
        except Exception as e:
            print("save screen getImgOnlineUrl  error:", repr(e))
            return ""

        try:
            r = requests.post(url, json=data, verify=False)

            captureUrl = r.json()['upLoadImgInfos'][0]['url']
            captureUrl = captureUrl.replace("https", "http")
            if env == "fws":
                captureUrl = captureUrl.replace("c-ctrip", "fx.ctripcorp")
            return captureUrl
        except Exception as e:
            print("请求接口失败", )

        return ''

    # 网络图片下载到本地
    @classmethod
    def getOnlineImgPath(cls, url, path=None):
        try:
            if path is None:
                path = cls.getImgDownloadUrl()
            urllib.request.urlretrieve(url, path)
        except Exception as e:
            print("下载图片失败", repr(e))
            return ""
        return path
        
    @classmethod
    def getBase64OnlineUrl(cls, base64str, imgname='test.png', imgType='png'):
        """
        将base64编码的图片上传到服务器，并返回图片的url，实现base64的内容上传
        支持将自定义Base64文本嵌入到图片中
        
        注意：如果base64str传递几次都是一样的，那么生成的图片url也是一样的
        """
        try:
            # 获取模板图片的base64（直接通过requests获取不保存本地）
            template_img_url = "http://dimg04.fx.ctripcorp.com/images/1ho1w12000jwi836r4581.png"
            response = requests.get(template_img_url, verify=False)
            
            # 检查响应是否成功
            if response.status_code != 200:
                print(f"获取模板图片失败: HTTP {response.status_code}")
                return ""
                
            # 将响应内容转为base64
            img_data = response.content
            img = Image.open(BytesIO(img_data))
            
            # 准备元数据
            meta = PngImagePlugin.PngInfo()
            # 将用户的base64文本存入Comment字段
            meta.add_text("Comment", base64str)
            
            # 保存为新的PNG (带有元数据)
            buffer = BytesIO()
            img.save(buffer, format="PNG", pnginfo=meta)
            
            # 重新编码为base64
            new_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            # 添加data URI前缀
            imgText = 'jpeg' if imgType == 'jpg' else 'png'
            base64str_ = f"data:image/{imgText};base64,{new_base64}"
            
            url = 'http://offline.fx.ctripcorp.com/soa2/22336/upLoadingImgData'
            data = {
                'imageInfos': [{
                    'base64str': base64str_,
                    'imgtype': 'image/' + imgText + '',
                    'imgname': imgname}],
                'accesstype': 'int'
            }
        except Exception as e:
            print("save screen getImgOnlineUrl error:", repr(e))
            return ""

        try:
            r = requests.post(url, json=data, verify=False)
            captureUrl = r.json()['upLoadImgInfos'][0]['url']
            captureUrl = captureUrl.replace("https", "http")
            # 我们调用upLoadingImgData上传图片，返回的是生产环境的链接，可以改c-ctrip为fx.ctripcorp就转为内网环境的链接了 
            captureUrl = captureUrl.replace("c-ctrip", "fx.ctripcorp")
            return captureUrl
        except Exception as e:
            print("请求接口失败", repr(e))

        return ''
    
    @classmethod
    def extractDataFromImageUrl(cls, img_url):
        """
        从图片URL中提取出自定义数据
        """
        try:
            # 直接通过requests下载图片
            response = requests.get(img_url, verify=False)
            
            # 检查响应是否成功
            if response.status_code != 200:
                print(f"获取图片失败: HTTP {response.status_code}")
                return ""
                
            # 使用PIL打开图片并读取元数据
            img = Image.open(BytesIO(response.content))
            
            # 读取Comment字段中的自定义数据
            if 'Comment' in img.info:
                return img.info['Comment']
            else:
                return ""
        except Exception as e:
            print("提取图片数据失败:", repr(e))
            return ""
            
    # 比对图片
    @classmethod
    def compareImg(cls, url=''):
        if url == '':
            return None
        return Template(cls.getOnlineImgPath(url))

    # 不支持screenrecord命令手机可以尝试该录屏方法
    @classmethod
    def recorderStart(cls):
        adb = ADB(serialno=DeviceInfo.deviceName)
        cls.recorder = Recorder(adb)
        cls.recorder.start_recording()

    @classmethod
    def recorderEnd(cls, videoName):
        videoName = "{}{}".format(videoName, ".mp4")
        cls.recorder.stop_recording(output=videoName)
        video_file_path = os.path.abspath(videoName)
        return video_file_path

if __name__ == "__main__":
    capture = Capture()
    timestamp = int(time.time())
    # print(capture.getImgOnlineUrl("test.png", "test.png", 'png'))
    print(capture.getBase64OnlineUrl("****************************************************************************", f"compressed_data_{timestamp}.png", 'png'))
    # print(capture.extractDataFromImageUrl("http://dimg04.c-ctrip.com/images/1ho4012000jwc2et2C28A.png"))
