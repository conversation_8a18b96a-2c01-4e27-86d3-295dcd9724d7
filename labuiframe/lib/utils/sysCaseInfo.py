# -*- coding: utf-8 -*-
import os
import pkgutil
import importlib

import configparser
import requests
import json

from labuiframe.lib.config.labconfig import Labconfig
from pocounit.utils.misc import get_project_root
# 同步所有用例信息到平台APL

class sysCaseInfo():
    caseInfoList = []

    def listFileDirs(self,filepath,pkgname,branch=''):
        for _, file, _ in pkgutil.iter_modules([filepath]):
            tempath = '{}{}{}'.format(filepath, os.sep, file)
            if os.path.isdir(tempath):
                tempPkgname = pkgname + '.' + file
                self.listFileDirs(self,tempath,tempPkgname,branch)
            else:
                module_name = pkgname + '.' + file
                module = importlib.import_module(module_name) # import module
                try:
                    module_class = getattr(module, file)
                    method = getattr(module_class(),'getMetaInfo')
                    info = method()

                    case_info = {}
                    case_info['className'] = module_name
                    case_info['caseName'] = info['casename']
                    case_info['owner'] = info['author']
                    try:
                        case_info['env'] = info['evn']
                    except Exception:
                        case_info['env'] = ''
                    case_info['category'] = info['category']

                    if info['platform'].lower() == 'ios':
                        case_info['ios'] = 1
                    elif info['platform'].lower() == 'android':
                        case_info['android'] = 1
                    elif info['platform'].lower() == 'androidandios':
                        case_info['ios'] = 1
                        case_info['android'] = 1

                    try:
                        if info['core'].lower() == 'true':
                            case_info['core'] = 1
                        else:
                            case_info['core'] = 0
                    except Exception:
                        case_info['core'] = 0

                    case_info['branch'] = branch
                    case_info['platform'] = "airtest"

                    try:
                        if info['abandon'].lower() == 'true':
                            case_info['enabled'] = 0
                        else:
                            case_info['enabled'] = 1
                    except Exception:
                        case_info['enabled'] = 1
                    case_info['department'] = Labconfig.getGroup()
                    self.caseInfoList.append(case_info)
                except Exception as e:
                    print("Exception: ", e)

    @classmethod
    def addCaseInfo(cls,projectroot,pkgpath,branch=''):
        # pkgpath = os.path.dirname(__file__)
        # projectroot = get_project_root(pkgpath)
        # pkgpath = "{}{}{}{}{}{}{}".format(projectroot, os.sep, "hotelautotest", os.sep, "scripts", os.sep, "case")
        Labconfig.config(projectroot)
        a = Labconfig.getGroup()
        pkgname = "hotelautotest.scripts.case"

        cls.listFileDirs(cls,pkgpath,pkgname,branch)
        print("CaseList Length: " + str(len(cls.caseInfoList)))
    
        # 写入平台
        url = "http://************:8080/api/cap/case/addCaseInfoList"
        headers = {'content-type': 'application/json'}
        r = requests.post(url,data = json.dumps(cls.caseInfoList),headers= headers)




