import json

from labuiframe import CommonAction
from labuiframe.lib.utils.ElementItem import ElementItem
from poco import Po<PERSON>
from playwright.sync_api import Page


class GetCurrentPageData():
    page_elements = None
    percent = 0.7

    @classmethod
    def load_page_elements(cls, poco):
        trees = []

        first_tree = cls.get_element_tree(poco)
        trees.append(first_tree)

        end = first_tree.is_end()
        down_count = 0
        last_hash = 0

        while not end:
            poco.scroll("vertical", percent=cls.percent, duration=1)
            # print("下拉第{}次".format(down_count + 1))
            tree = cls.get_element_tree(poco)
            trees.append(tree)
            end = tree.is_end()
            current_hash = hash(tree)
            if current_hash == last_hash:
                break
            last_hash = current_hash
            down_count += 1
            if down_count > 10:
                end = True

        # print("下拉{}次".format(down_count))

        for i in range(down_count - 1):
            poco.scroll("vertical", percent=-cls.percent, duration=0.5)
            # print("上拉第{}次".format(i + 1))
        # 无需下滑场景，不再做上拉操作
        if down_count > 1:
            for i in range(10):
                poco.scroll("vertical", percent=-0.2, duration=0.1)

        cls.page_elements = json.dumps(trees, default=ElementItem.custom_serializer)

    @classmethod
    def print_page_elements(cls,poco):
        if isinstance(poco, Page):
            tree = cls.get_element_tree(poco)
            cls.page_elements = json.dumps([tree], default=ElementItem.custom_serializer)
            print(cls.page_elements)
            return
        cls.load_page_elements(poco)
        print(cls.page_elements)

    @classmethod
    def get_element_tree(cls, poco):
        if isinstance(poco, Page):
            tree = CommonAction.getWebhierarchy(poco)
        else:
            tree = poco.agent.hierarchy.dump()
        r = getroot(tree)
        if r is not None:
            control_tree = get_element_tree(r)
            return control_tree
        else:
            return None


def getroot(node):
    """
    获取根节点
    """
    if isinstance(node, dict):
        if is_root(node):
            return node

        if 'children' in node and isinstance(node['children'], list):
            # 如果有子节点，遍历子节点
            for child in node['children']:
                r = getroot(child)
                if r is not None:
                    return r

    return None


def is_root(node):
    if not 'name' in node:
        return False

    name = node['name']

    return ":id/rnRootContainer" in name or ":id/content" in name


def get_element_tree(node, parent=None):
    """
    递归遍历控件树并收集 并生成一个缩减版的树。

    :param node: 当前遍历的控件节点。
    :param parent: 当前遍历的控件节点的父节点。
    """
    if isinstance(node, dict) and 'name' in node:
        name = get_element_name(node)

        e = ElementItem(name, parent)

        txt = get_element_text(node)
        if txt and txt != '':
            e.add_texts(txt)

        if 'children' in node and isinstance(node['children'], list):
            # 如果有子节点，遍历子节点
            for child in node['children']:
                sub = get_element_tree(child, e)
                if sub is not None:
                    e.add_child(sub)

        return e

    return None


def get_element_name(node):
    if not 'name' in node:
        return ""

    return node['name']


def get_element_text(node):
    if not 'payload' in node:
        return None

    if not 'text' in node['payload']:
        return None

    return node['payload']['text']
