import json
import random

import requests
from labuiframe.lib.config.labconfig import Labconfig



class AiAgentGenerate():
    HEADER = {'content-type': 'application/json;charset=utf8'}

    # @classmethod
    # def getDebugScriptsNew(cls, labelId, platform):
    #     try:
    #         res = requests.get("http://uiautomationaiservice.fws.qa.nt.ctripcorp.com/getIP")
    #         if res.status_code == 200:
    #             ip = res.json().get("remote_addr")
    #             if len(ip) > 0:
    #                 port = random.randint(10000, 20000)
    #                 if cls.aiAgentGnerateTrigger(ip, port, labelId, platform):
    #                     return ip, port
    #                 else:
    #                     # 回调collect接口上报日志
    #                     cls.updateAiCodeDetail(labelId, platform, "调用agentGenerate接口失败", 5, "agentGenerate服务不可用")
    #                     return "", ""
    #             else:
    #                 printUtil.printCaseDevice("***获取到的ip值非法--为空***")
    #                 return "", ""
    #         else:
    #             printUtil.printCaseDevice("***获取当前机器ip失败***")
    #             return "", ""
    #
    #     except Exception as e:
    #         printUtil.printCaseDevice("*****Debug调试过程失败{}".format(str(e)))
    #         return "", ""

    # @classmethod
    # def aiAgentGnerateTrigger(cls, ip, port, labelId, platForm):
    #     try:
    #         url = "http://************:28080/api/v2/agentGenerate"
    #         # url = "http://127.0.0.1:28080/api/v2/agentGenerate"
    #         data = {
    #             "ip": ip,
    #             "port": port,
    #             "labelID": labelId,
    #             "platform": platForm
    #         }
    #         printUtil.printCaseDevice("开始agentGenerate request:{}".format(json.dumps(data)))
    #         response = requests.post(url, headers=cls.HEADER, data=json.dumps(data))
    #         if response.status_code == 200:
    #             return True
    #         printUtil.printCaseDevice("agentGenerate接口返回失败")
    #         return False
    #     except Exception as e:
    #         printUtil.printCaseDevice("*****agentGenerate接口异常{}".format(str(e)))
    #         return False

    @classmethod
    def updateAiCodeDetail(cls, trace_log_id, platForm, msg, errType=0, errTitle=""):
        # python内部仅处理调用aiAgentGnerateTrigger失败场景，因此传参写死即可
        url = "http://htlmysticmare.fws.qa.nt.ctripcorp.com/api/chat/updateAiCodeDetail"
        data = {
            "code": -200,
            "msg": msg,
            "traceLogID": trace_log_id,
            "platform": platForm,
            "errType": errType,
            "errTitle": errTitle
        }
        print("updateAiCodeDetail request:{}".format(json.dumps(data)))
        response = requests.post(url, headers=cls.HEADER, data=json.dumps(data))
        if response.status_code == 200:
            print("updateAiCodeDetail上报成功")
        else:
            print("updateAiCodeDetail上报失败")

    @classmethod
    def getDebugScriptsNew(cls, trace_log_id: str, platform: int, ai_service_endpoint: str):
        try:
            res = requests.get("http://uiautomationaiservice.fws.qa.nt.ctripcorp.com/getIP")
            if res.status_code == 200:
                ip = res.json().get("remote_addr")
                if len(ip) > 0:
                    port = random.randint(20000, 40000)
                    if cls.aiAgentGenerateTrigger(ip, port, trace_log_id, platform, ai_service_endpoint):
                        return ip, port
                    else:
                        # 回调collect接口上报日志
                        cls.updateAiCodeDetail(trace_log_id, platform, "调用agentGenerate接口失败", 5, "agentGenerate服务不可用")
                        return "", ""
                else:
                    print("***获取到的ip值非法--为空***")
                    return "", ""
            else:
                print("***获取当前机器ip失败***")
                return "", ""

        except Exception as e:
            print("*****Debug调试过程失败{}".format(str(e)))
            return "", ""

    @classmethod
    def aiAgentGenerateTrigger(cls, ip: str, port: int, trace_log_id: str, platform: int, ai_service_endpoint: str):
        try:
            url = f"{ai_service_endpoint}/api/v2/agentGenerate"
            # url = "http://127.0.0.1:28080/api/v2/agentGenerate"
            data = {
                "ip": ip,
                "port": port,
                "traceLogID": trace_log_id,
                "platform": platform
            }
            print("开始agentGenerate request:{}".format(json.dumps(data)))
            response = requests.post(url, headers=cls.HEADER, data=json.dumps(data))
            print("返回的数据：{}".format(response.text))
            if response.status_code == 200:
                return True
                print("agentGenerate接口返回失败")
            return False
        except Exception as e:
            print("*****agentGenerate接口异常{}".format(str(e)))
            return False

    @classmethod
    def compareImage(cls, baseUrl, targetUrl):
        try:
            url = "http://htluiautomationelementmodelmerge.fws.qa.nt.ctripcorp.com/compareImageEqual"
            data = {
                "ori_pic": baseUrl,
                "new_pic": targetUrl
            }
            response = requests.post(url, headers=cls.HEADER, data=json.dumps(data))
            if response.status_code == 200:
                return response.json().get("result")
            return False
        except Exception as e:
            print("*****compareImageEqual接口异常{}".format(str(e)))
            return False

    @classmethod
    def get_page_info(cls, project_name, platform, page_name):
        platform = Labconfig.correct_mpass_platform(platform)
        # url = "http://uiautomationaiservice.fws.qa.nt.ctripcorp.com/api/v2/getPageInfo"
        url = "http://htlmysticmare.fws.qa.nt.ctripcorp.com/api/generateConfig/getPageInfo"
        data = {
            "project_name": project_name,
            "platform": platform,
            "page_name": page_name
        }
        Labconfig.platformId = platform
        print("设置platformId:{}".format(platform))
        print("获取页面信息get_page_info request:{}".format(json.dumps(data)))
        response = requests.post(url, headers=cls.HEADER, data=json.dumps(data))
        if response.status_code == 200:
            responseData = response.json().get("data")
            print("获取页面信息get_page_info response:{}".format(json.dumps(responseData)))
            return {"page_url": responseData.get("jumpUrl",""), "page_flag": responseData.get("pageFlag", "")}
        print("获取页面信息get_page_info response:{}".format(response.text))
        return {"page_url": "", "page_flag": ""}
        # if response.status_code == 200:
        #     print("获取页面信息get_page_info response:{}".format(json.dumps(response.json().get("data"))))
        #     return response.json().get("data")
        # else:
        #     print("获取页面信息get_page_info response:{}".format(response.text))
        # return {"page_url": "", "page_flag": ""}


