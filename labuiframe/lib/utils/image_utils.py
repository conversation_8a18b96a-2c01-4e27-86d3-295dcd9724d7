from __future__ import absolute_import

import base64
import io
import math
import os
import sys

from PIL import Image
from labuiframe.lib.image.errors import WatcherError
from airtest.core.settings import Settings as ST
from airtest.core.helper import G
from six import PY3
import cv2
from airtest.core.cv import try_log_screen
import time
from airtest.aircv.aircv import imwrite as airImWrite


def image_from_file(file):
    return Image.open(file)


def image_from_bytes(png_bytes):
    return Image.open(io.BytesIO(png_bytes))


def image_from_base64(base64_str):
    # type: () -> Image.Image
    return Image.open(io.BytesIO(base64.b64decode(base64_str)))


def scale_image(image,scale_ratio):
    # type: (Image.Image,float) -> Image.Image
    if scale_ratio == 1:
        return image
    image_ratio = float(image.height) / float(image.width)
    scale_width = int(math.ceil(image.width * scale_ratio))
    scale_height = int(math.ceil(scale_width * image_ratio))
    image = image.convert('RGBA')
    scaled_image = image.resize((scale_width,scale_height),resample= Image.BICUBIC)
    return scaled_image

def get_base64_from_image(image):
    # type: (Image.Image) -> str
    image_bytes_stream = io.BytesIO()
    image.save(image_bytes_stream,format='PNG')
    image64 = base64.b64encode(image_bytes_stream.getvalue()).decode('utf-8')
    image_bytes_stream.close()
    return image64

def get_bytes_from_image(image):
    # type: (Image.Image) -> bytes
    image_bytes_stream = io.BytesIO()
    image.save(image_bytes_stream, format='PNG')
    image_bytes = image_bytes_stream.getvalue()
    image_bytes_stream.close()
    return image_bytes

def get_image_part(image,region):
    # type: (Image.Image,Region) -> Image.Image
    if region.is_empty():
        raise WatcherError("Region Is Empty!")
    return image.crop(box=(region.left,region.top,region.right,region.bottom))

def snapshot(filename=None):
    if filename:
        if not os.path.isabs(filename):
            logdir = ST.LOG_DIR or "."
            filename = os.path.join(logdir, filename)
        screen = G.DEVICE.snapshot(filename)
        return try_log_screen(filename, screen)
    else:
        return try_log_screen()

def try_log_screen(filepath=None, screen=None):
    """
    Save screenshot to file
    Args:
        screen: screenshot to be saved
    Returns:
        None
    """
    if not ST.LOG_DIR:
        return
    if screen is None:
        screen = G.DEVICE.snapshot()
    filename = "%(time)d.jpg" % {'time': time.time() * 1000}
    log_filepath = os.path.join(ST.LOG_DIR, filename)
    # airImWrite(log_filepath, screen, ST.SNAPSHOT_QUALITY)
    if filepath is not None:
        imwrite(filepath, screen)
    return filename

def imwrite(filename, img):
    """写出图片到本地路径"""
    if PY3:
        cv2.imencode('.jpg', img)[1].tofile(filename)
    else:
        filename = filename.encode(sys.getfilesystemencoding())
        cv2.imwrite(filename, img)



