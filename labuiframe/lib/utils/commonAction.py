import datetime
import json
import platform
import random
import re
import threading
import time
from urllib.parse import urlparse

import jsonpath
import requests
from airtest.core.android.adb import ADB
from airtest.core.api import clear_app
from airtest.core.api import device as current_device
from airtest.core.api import touch, device, sleep
from airtest.core.helper import G
from playwright.sync_api import Page, expect
from poco import Poco

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.HandleInfo import HandleInfo
from labuiframe.lib.utils.MyIOSPoco import MyIOSPoco
from labuiframe.lib.utils.MySqlConnect import MySqlConnect
from labuiframe.lib.utils.capture import Capture, CmtLogger, DeviceInfo, base64
from labuiframe.lib.utils.cdata_util import Cdata_Util
from labuiframe.lib.utils.getProjectConfigInfoUtil import GetProjectConfigInfoUtil
from labuiframe.lib.utils.loginUtil import LoginUtil
from labuiframe.lib.utils.mcd_util import Mcd_Util
from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.case.simple_proxy import SimpleProxy
from labuiframe.lib.utils.logsDecorator import outer_time
from labuiframe.lib.utils.socketUtil import SocketUtil
from labuiframe.lib.utils.web_element_locator import WebElementLocator
from labuiframe.lib.utils.ElementItem import ElementItem


class CommonAction():
    currentClientid = ""
    currentCaseName = ""
    existSharkList = []
    sharkList = []
    config = {}

    @classmethod
    def getExistSharkList(cls):
        return cls.existSharkList

    @classmethod
    def setSharkListDefault(cls):
        cls.sharkList = []

    @classmethod
    def getSharkList(cls):
        return cls.sharkList

    @classmethod
    def getProjectRootPath(cls):
        return cls.case.project_root

    @classmethod
    def clearCache(cls):
        packageName = Labconfig.getPackageName()
        clear_app(packageName)
        printUtil.printCaseDevice(packageName + "缓存已经清理")
        try:
            printUtil.printCaseDevice(packageName + "定位权限赋予")
            G.DEVICE.shell(['pm', 'grant', packageName, "android.permission.ACCESS_FINE_LOCATION"])
            printUtil.printCaseDevice(packageName + "存储权限赋予")
            G.DEVICE.shell(['pm', 'grant', packageName, "android.permission.READ_EXTERNAL_STORAGE"])
            printUtil.printCaseDevice(packageName + "电话权限赋予")
            G.DEVICE.shell(['pm', 'grant', packageName, "android.permission.READ_PHONE_STATE"])
        except Exception as e:
            printUtil.printCaseDevice("**********有权限赋予失败***********", e)

    # abConfig 这个是输入当前单独配置的
    @classmethod
    def getNewAbConfig(cls, configType=1, page='c-detail', abConfig=[]):

        addList = MySqlConnect.queryAbConfig(configType, page)

        # 传入的配置如果是空的直接返回公共的配置
        if len(abConfig) == 0:
            return addList
        # 当代码单独配置的不在数据库中时，需要处理添加到配置中
        for config in abConfig:
            # print('===',str(config[0]),str(addList))
            if str(config[0]) not in str(addList):
                addList.append(config)

        return addList

    # 返回配置的cid--数据库有则返回库里面的，否则为空
    @classmethod
    def getClientId(cls, appid='********', deviceNo=""):
        clientList = MySqlConnect.queryClientId(appid, deviceNo)
        clientResult = clientList[0] if len(clientList) > 0 else ""
        return clientResult

    @classmethod
    def commonConfig(self, config, name, value, isStr=True):
        string = '['

        i = 0
        for conf in config:
            string += "{\"" + name + "\":\""
            try:
                configName = conf[0]
                configValue = conf[1]
                string += configName
                if isStr:
                    string += "\",\"" + value + "\":\""
                else:
                    string += "\",\"" + value + "\":"
                string += str(configValue)
                string += "\"}" if isStr else "}"
                if (i < len(config) - 1):
                    string += ","
                i = i + 1
            except Exception as e:
                printUtil.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())),
                                          'abconfigerror - str(e):\t\t', str(e))

        string += "]"
        return string

    @classmethod
    def setAndroidCalendar(cls, poco, day):
        '''
        安卓日期切换
        :param day: 距离当前日期后N天
        :return:
        '''

        CmtLogger.logStep(cls.get_date(day))
        checkIndate = cls.get_date(day).strftime('%Y') + "年" + str(int(cls.get_date(day).strftime('%m'))) + "月"
        checkIndateInt = cls.get_date(day).strftime('%Y') + str(int(cls.get_date(day).strftime('%m')))
        checkInDay = cls.get_date(day).strftime('%d')
        # printUtil.printCaseDevice(checkIndate)
        week = cls.get_date(day).isocalendar()
        # printUtil.printCaseDevice("week:" + str(week[2]))
        # printUtil.printCaseDevice(week[1])
        week1 = datetime.date(int(cls.get_date(day).strftime('%Y')), int(cls.get_date(day).strftime('%m')),
                              1).isocalendar()
        # printUtil.printCaseDevice("week1:" + str(week1))
        # printUtil.printCaseDevice(week[1] - week1)

        try:
            calendar_month_title = poco("ctrip.android.view:id/calendar_partlayout").child(
                "ctrip.android.view:id/calendar_month_title_layout").offspring(
                "ctrip.android.view:id/calendar_month_title_left_tv").get_text()
        except:
            # poco().scroll("vertical", 0.1)
            poco.swipe([0.5, 0.55], [0.5, 0.45])
            calendar_month_title = poco("ctrip.android.view:id/calendar_partlayout").child(
                "ctrip.android.view:id/calendar_month_title_layout").offspring(
                "ctrip.android.view:id/calendar_month_title_left_tv").get_text()
        calendar_month_title = calendar_month_title.replace("年", "")
        calendar_month_title = calendar_month_title.replace("月", "")

        a = 0
        while a == 0:
            calendar_week_title_Y = poco("ctrip.android.view:id/calendar_week_title_view").get_position()[1] + \
                                    poco("ctrip.android.view:id/calendar_week_title_view").get_size()[1]
            list = poco("ctrip.android.view:id/calendar_list").child(
                "ctrip.android.view:id/calendar_month_title_layout")
            if len(list):
                for item in list:
                    month_title = item.offspring("ctrip.android.view:id/calendar_month_title_left_tv").get_text()
                    if month_title == checkIndate:
                        # poco().scroll("vertical", item.get_position()[1] - calendar_week_title_Y + 0.02)
                        distance = (item.get_position()[1] - calendar_week_title_Y + 0.02) / 2
                        poco.swipe([0.5, 0.5 + distance], [0.5, 0.5 - distance])
                        a = 1
                        break
            if a == 0:
                if int(calendar_month_title) >= int(checkIndateInt):
                    # poco().scroll("vertical",-0.5)
                    poco.swipe([0.5, 0.35], [0.5, 0.75])
                else:
                    # poco().scroll("vertical", 0.5)
                    poco.swipe([0.5, 0.75], [0.5, 0.35])
        list = poco("ctrip.android.view:id/calendar_list").child("android.view.View")
        week_number = week1[1]
        if week[1] < week_number:
            week_number = 0
        if week[2] == 7:
            if week1[2] == 7:
                item = list[week[1] - week_number]
            else:
                item = list[week[1] - week_number + 1]
            y = item.get_position()[1] + item.get_size()[1] / 3
            # y = item.get_position()[1]
            # printUtil.printCaseDevice(y)
            x = 1 / 14
            # printUtil.printCaseDevice(x)
        else:
            if week1[2] == 7:
                item = list[week[1] - week_number - 1]
            else:
                item = list[week[1] - week_number]
            y = item.get_position()[1] + item.get_size()[1] / 3
            # y = item.get_position()[1]
            CmtLogger.logStep(y)
            x = week[2] / 7 + 1 / 14
            CmtLogger.logStep(x)
        # poco().click([x, y])
        w, h = device().get_current_resolution()
        touch([x * w, y * h])
        sleep(2)

    @classmethod
    def get_date(self, days):
        return datetime.datetime.now() + datetime.timedelta(days=days)

    @classmethod
    def virtualenv_scroll(cls, poco, direction='vertical', percent=0.6, duration=2, steps=5):
        if DeviceInfo.get_is_virtual():
            dev = device()
            x, y = dev.get_current_resolution()
            printUtil.printCaseDevice("设备的宽度和高度是" + str(x) + " " + str(y))
            if direction == "vertical":
                start_y = y * (0.5 + percent / 2)
                end_y = y * (0.5 + percent / 2 - percent)
                dev.touch_proxy.swipe_along([[0.5 * x, start_y], [0.5 * x, end_y]], duration=duration, steps=steps)
            elif direction == "horizontal":
                start_x = x * (0.5 + percent / 2)
                end_x = x * (0.5 + percent / 2 - percent)
                dev.touch_proxy.swipe_along([[start_x, 0.5 * y], [end_x, 0.5 * y]], duration=duration, steps=steps)
        else:
            poco.scroll(direction, percent, duration)

    # platform 1:ctrip 2:trip
    @classmethod
    def getAccountInfo(cls, userName, passWord, accessCode,platform):
        if (Labconfig.getIsHotelWirelessAppGroup() != "True"):
            return
        f_username = userName
        f_password = passWord
        try:
            # Labconfig.set_extention({"group":0,"uiCaseInfoList":[{"id":3671853,"tags":["常规订单"]}],"labelID":0,"jSceneID":0,"operatorInfo":{"displayName":"gh.yu"},"extention":{"hotelId":*********,"roomId":"********","uid":"***********","password":"123456","subEnv":"fat10367","realPayType":0,"cin":"2023-08-31","cout":"2023-09-01"},"cSceneID":10319})
            extention = Labconfig.extention
            if extention != None and "extention" in extention and extention["extention"] != "":
                f_username = extention["extention"]["uid"]
                if str(extention).find("password") > -1:
                    f_password = extention["extention"]["password"]
                else:
                    f_password = extention["extention"]["pwd"]
            else:
                f_username = extention["uid"]
                if "password" in extention:
                    f_password = extention["password"]
                else:
                    f_password = extention["pwd"]
        except Exception:
            printUtil.printCaseDevice("extention存在,但是没有登录信息")
        #如果数据表中已有登录信息，则直接使用
        login_info_dal = MySqlConnect.get_uid_login_info(f_username,platform)
        if login_info_dal and len(login_info_dal) == 2:
            duid = login_info_dal[0]
            ticket = login_info_dal[1]
            printUtil.printCaseDevice(f'dal中已有登录信息，直接使用: duid:{duid}, ticket:{ticket}')
            loginInfo = ",\"LoginInfo\":{\"returnCode\":0,\"message\":\"成功\",\"uid\":\"" + f_username + "\",\"password\":\"" + f_password + "\",\"duid\":\"" + \
                        duid + "\",\"ticket\":\"" + ticket + "\"}"
            # 日志用于自动分析case，请不要变更！
            printUtil.printCaseDevice("登录信息为:" + loginInfo)
            Labconfig.set_loginInfo(loginInfo)
            Labconfig.set_ticket(ticket)
            Labconfig.set_need_logout(False)
            Labconfig.set_duid(duid)
            return ticket, duid
        headers = {'content-type': 'application/json;charset=utf8'}
        url = "http://webapi.soa.fws.qa.nt.ctripcorp.com/api/22160/pwdLogin"
        body = {
            "authenticateInfo": {
                "password": f_password,
                "loginName": f_username
            },
            "accessCode": accessCode
        }
        # 调用接口随机延迟几s，暂时规避1s内接口返回相同ticket问题
        time.sleep(random.randint(1, 10))
        dataInfo = requests.post(url, data=json.dumps(body), headers=headers).json()
        try:
            loginInfo = ",\"LoginInfo\":{\"returnCode\":0,\"message\":\"成功\",\"uid\":\"" + f_username + "\",\"password\":\"" + f_password + "\",\"duid\":\"" + \
                        dataInfo["duid"] + "\",\"ticket\":\"" + dataInfo["ticket"] + "\"}"
            # 日志用于自动分析case，请不要变更！
            printUtil.printCaseDevice("登录信息为:" + loginInfo)
            Labconfig.set_loginInfo(loginInfo)
            Labconfig.set_ticket(dataInfo["ticket"])
            Labconfig.set_duid(dataInfo["duid"])
            return dataInfo.get("ticket", ""), dataInfo.get("duid", "")
        except Exception:
            printUtil.printCaseDevice("pwdLogin接口返回错误：{}".format(str(dataInfo)))

    @classmethod
    def get_loginInfo_pro(cls):
        if Labconfig.getAppid() == "********":
            login_info_dal = MySqlConnect.get_uid_login_info("M6405505246",1)
        elif Labconfig.getAppid() == "37":
            login_info_dal = MySqlConnect.get_uid_login_info("_TIHK1c05b3kzowzv",2)
        if login_info_dal and len(login_info_dal) == 2:
            duid = login_info_dal[0]
            ticket = login_info_dal[1]
            printUtil.printCaseDevice(f'dal中已有登录信息，直接使用: duid:{duid}, ticket:{ticket}')
            loginInfo = ",\"LoginInfo\":{\"returnCode\":0,\"message\":\"成功\",\"uid\":\"" + "M6405505246" + "\",\"password\":\"" + "123456" + "\",\"duid\":\"" + \
                        duid + "\",\"ticket\":\"" + ticket + "\"}"
            return loginInfo
        return ""

    @classmethod
    def setConfigClient(cls, clientId):
        cls.currentClientid = clientId;

    @classmethod
    def getStoreClient(cls):
        return cls.currentClientid

    @classmethod
    def setTestCaseName(cls, caseName):
        cls.currentCaseName = caseName

    @classmethod
    def getTestCaseName(cls):
        return cls.currentCaseName

    @classmethod
    def configForWebCommon(cls, page_url="", mock_key="", login=True, ABList=[]):
        # 将page_url提取域名
        parsed_url = urlparse(page_url)
        domain = parsed_url.netloc

        # 分割域名并保留最后两个部分
        parts = domain.split('.')
        if len(parts) > 2:
            domain = '.' + '.'.join(parts[-2:])
        else:
            domain = '.' + domain
        # web可支持未登录，web环境根据场景判断需要删除哪些key
        if not login:
            login_list = ["cticket", "DUID"]
            Labconfig.web_cookies = [
                cookie for cookie in Labconfig.web_cookies
                if cookie.get('name') not in login_list  # 自动处理缺失键
            ]
        cls.config = {
            "ticket": Labconfig.get_ticket(),
            "page_url": page_url,
            "mock_key": mock_key,
            "domain": domain,
            "duid": Labconfig.get_duid(),
            "cookies": Labconfig.web_cookies,
            "ABList": ABList
        }
        return cls.config

    @classmethod
    def configForCtripCommon(cls, openUrl="", ABList=[], IncrementList=[], switchList=[], mockKey="",
                             openPopLayer="F",mcdConfig=[],
                             login=True, clientid='', subenv="fat5627", projectPage="", latitude="", longitude="",
                             uid="", password="", needLogOut=False, isBds=False,runEnv="fat"):

        s = CommonAction.commonConfig(config=CommonAction.getNewAbConfig(configType=2, page=projectPage, abConfig=switchList), name='key_switch', value='key_status', isStr=False)
        ABList = cls.getNewAbConfig(configType=1, page=projectPage, abConfig=ABList)
        IncrementList = cls.getNewAbConfig(configType=3, page=projectPage, abConfig=IncrementList)
        # loginInfo为测试环境登录信息
        if runEnv != "pro":
            loginInfo = Labconfig.get_loginInfo()
        else:
            loginInfo = cls.get_loginInfo_pro()
        location = ""
        listenPort = 0
        if DeviceInfo.is_ios_run() and DeviceInfo.get_is_simulator() == 1 :
            printUtil.printCaseDevice("仅有ios模拟器场景需要传入listenPort")
            socketUtil = SocketUtil()
            listenPort = socketUtil.check_port()

        if mcdConfig:
            result_dict = {}
            for item in mcdConfig:
                if '#' in item:
                    # 分割模块名和 JSON 字符串
                    module_name, json_str = item.split('#', 1)
                    try:
                        # 尝试解析 JSON 字符串
                        module_data = json.loads(json_str)
                        # 将解析后的数据添加到结果字典中
                        result_dict[module_name] = module_data
                    except json.JSONDecodeError as e:
                        print(f"解析错误：{e}")
            # 将结果字典转换为 JSON 字符串
            result_json = json.dumps(result_dict, indent=4)
        else:
            result_json = ""
        cls.config = {
            "mockKey": mockKey,
            "openPopLayer": openPopLayer,
            "schema": cls.concatUrl(openUrl),
            "abtest": json.loads(CommonAction.commonConfig(config=ABList, name='abnumber', value='abresult')),
            "switchList": json.loads(s),
            "increment": json.loads(CommonAction.commonConfig(config=IncrementList, name='incid', value='incvalue')),
            "replaceMobileConfig":result_json,
            "appenv": {
                "desc": "环境",
                "value": "fat" if runEnv != "pro" else "pro"
            },
            "subenv": {
                "desc": "环境",
                "value": subenv if runEnv != "pro" else ""
            },
            "listenPort":listenPort,
            # app启动参数添加canyonReportID用于代码覆盖率
            "UrlExt":{
                "canyonReportID": str(cls.getTaskId()) + "_mpaas_autotest"
            }
        }
        # 进入crn页面指定buildID，用于pipeline流程mpaas平台下发
        if Labconfig.module_name and Labconfig.module_id:
            cls.config["lockCRNBuildID"] = [{
                "productName": Labconfig.module_name,
                "buildID": Labconfig.module_id,
                "lock": True
            }]
        if clientid:
            cls.config["SetCid"] = int(clientid)
        if runEnv == "pro":
            cls.config["switch2ProductEnv"] = "1"
            cls.config["SetCid"] = 37002129210000100806
        else:
            cls.config["switch2ProductEnv"] = "0"
        cls.config["SetCoordinate"] = {
            "longitude": float(longitude) if longitude else 121.4737,
            "latitude": float(latitude) if latitude else 31.2304
        }
        # 其中uid != "***********"为兼容生产默认账号也走cticket场景
        if uid != "" and uid != "***********" and password != "" and login:
            # case 结束时需要将账号退掉
            HandleInfo.logoutSelfUid = 1
            cls.config["account"] = {
                "desc": "账号",
                "value": uid
            }
            cls.config["password"] = {
                "desc": "密码",
                "value": password
            }
        # 测试及生产均支持ticket登录
        elif login and (loginInfo != ""):
            loginInfo = loginInfo[loginInfo.find("{"):]
            infoBody = json.loads(loginInfo)
            cls.config["LoginInfo"] = infoBody
        # return json.dumps(cls.config)
        if not login:
            cls.clearCache()

        Labconfig.set_openUrl(cls.concatUrl(openUrl))
        if isBds:
            # bds场景case超时时间增加60s，如果case中已设置超时时间，则以case设置为准
            Labconfig.set_caseTimeOut(int(Labconfig.get_caseTimeOut()) + 60)
        return json.dumps(cls.config, ensure_ascii=False)

    @classmethod
    def configForTripCommon(cls, openUrl="", mockKey="", ABList=[], switchList=[], login=True,mainNetworkMainEnv="FAT",ibuNetworkMainEnv="FAT",
                            mainNetworkSubEnv="fat5627", ibuNetworkSubEnv="fat5627", languageCode="zh",
                            flightMockKey="", currency="HKD", mcdConfig=[],enableSOTPOverHttp="false",
                            isForceDisableOneTimePopupWindow="true", clientId="", projectPage="", localeCode="",
                            theme="IBUThemeModeSystem",userName="",password="", isBds=False,runEnv="fat",longitude="",latitude=""):
        switchList = CommonAction.getNewAbConfig(configType=2, page=projectPage, abConfig=switchList)
        ABList = CommonAction.getNewAbConfig(configType=1, page=projectPage, abConfig=ABList)
        # loginInfo为测试环境登录信息
        if runEnv != "pro":
            loginInfo = Labconfig.get_loginInfo()
        else:
            loginInfo = cls.get_loginInfo_pro()
        if CmtLogger.case_result_local:
            localeCode = CmtLogger.case_result_local
            languageCode = CmtLogger.case_result_local.split("-")[0]
        listenPort = 0
        if DeviceInfo.is_ios_run() and DeviceInfo.get_is_simulator() == 1 :
            printUtil.printCaseDevice("仅有ios模拟器场景需要传入listenPort")
            socketUtil = SocketUtil()
            listenPort = socketUtil.check_port()
        if mcdConfig:
            result_dict={}
            for item in mcdConfig:
                if '#' in item:
                    # 分割模块名和 JSON 字符串
                    module_name, json_str = item.split('#', 1)
                    try:
                        # 尝试解析 JSON 字符串
                        module_data = json.loads(json_str)
                        # 将解析后的数据添加到结果字典中
                        result_dict[module_name] = module_data
                    except json.JSONDecodeError as e:
                        print(f"解析错误：{e}")
            # 将结果字典转换为 JSON 字符串
            result_json = json.dumps(result_dict, indent=4)
        else:
            result_json = ""
        if localeCode and "_" in localeCode:
            languageCode = localeCode.split("_")[0]
        cls.config = {
            "languageCode":languageCode,
            "theme":theme,
            "localeCode":localeCode,
            "currency":currency,
            "abTests":json.loads(CommonAction.commonConfig(ABList, 'code', 'version')),
            "mobileConfigSwitchList":json.loads(CommonAction.commonConfig(switchList, 'name', 'enabled')),
            "hotelMockKey":mockKey,
            "targetURI": cls.concatUrl(openUrl),
            "mainNetworkMainEnv":mainNetworkMainEnv if runEnv != "pro" else "PRD",
            "mainNetworkSubEnv":mainNetworkSubEnv if runEnv != "pro" else "",
            "ibuNetworkMainEnv":ibuNetworkMainEnv if runEnv != "pro" else "PRD",
            "ibuNetworkSubEnv":ibuNetworkSubEnv if runEnv != "pro" else "",
            "flightMockKey":flightMockKey,
            "enableSOTPOverHttp":enableSOTPOverHttp,
            "replaceMobileConfig":result_json,
            "isForceDisableOneTimePopupWindow":isForceDisableOneTimePopupWindow,
            "listenPort":listenPort,
            "username":userName ,
            "password":password,
            # app启动参数添加canyonReportID用于代码覆盖率
            "UrlExt":{
                "canyonReportID": str(cls.getTaskId()) + "_mpaas_autotest"
            }
        }
        if clientId:
            cls.config["SetCid"] = int(clientId)
        if runEnv == "pro":
            cls.config["SetCid"] = 37002129210000100806
        cls.config["SetCoordinate"] = {
            "longitude": float(longitude) if longitude else 121.4737,
            "latitude": float(latitude) if latitude else 31.2304
        }
        # 进入crn页面指定buildID，用于pipeline流程mpaas平台下发
        if Labconfig.module_name and Labconfig.module_id:
            cls.config["lockCRNBuildID"] = [{
                "productName": Labconfig.module_name,
                "buildID": Labconfig.module_id,
                "lock": True
            }]
        if userName != "" and password != "" and login:
            HandleInfo.logoutSelfUid = 1
        # 生产/测试均默认已ticket进行登录
        elif login and loginInfo:
            if (loginInfo != ""):
                loginInfo = loginInfo[loginInfo.find("{"):]
                infoBody = json.loads(loginInfo)
                cls.config["LoginInfo"] = infoBody
        else:
            cls.clearCache()
        Labconfig.set_openUrl(openUrl)
        if isBds:
            # bds场景case超时时间增加60s，如果case中已设置超时时间，则以case设置为准
            Labconfig.set_caseTimeOut(int(Labconfig.get_caseTimeOut()) + 60)
        return json.dumps(cls.config,ensure_ascii=False)

    @classmethod
    def configForTripPro(cls, openUrl="", mockKey="", ABList=[], switchList=[], login=True,
                         mainNetworkMainEnv="PRD", ibuNetworkMainEnv="PRD", languageCode="ar",
                         flightMockKey="", currency="AED", enableSOTPOverHttp="false",
                         isForceDisableOneTimePopupWindow="true", username="", password="", clientID="", projectPage="", localeCode="",
                         theme="IBUThemeModeSystem"):
        switchList = CommonAction.getNewAbConfig(configType=2, page=projectPage, abConfig=switchList)
        ABList = CommonAction.getNewAbConfig(configType=1, page=projectPage, abConfig=ABList)
        if CmtLogger.case_result_local:
            localeCode = CmtLogger.case_result_local
            languageCode = CmtLogger.case_result_local.split("-")[0]
        cls.config = {
            "languageCode": languageCode,
            "theme": theme,
            "localeCode": localeCode,
            "currency": currency,
            "abTests": json.loads(CommonAction.commonConfig(ABList, 'code', 'version')),
            "mobileConfigSwitchList": json.loads(CommonAction.commonConfig(switchList, 'name', 'enabled')),
            "hotelMockKey": mockKey,
            "targetURI": cls.concatUrl(openUrl),
            "mainNetworkMainEnv": "PRD",
            "ibuNetworkMainEnv": "PRD",
            "forceLogout": "false",
            "flightMockKey": flightMockKey,
            "enableSOTPOverHttp": enableSOTPOverHttp,
            "isForceDisableOneTimePopupWindow": isForceDisableOneTimePopupWindow
        }
        if clientID:
            cls.config["SetCid"] = int(clientID)
        if (login):
            HandleInfo.logoutSelfUid = 1
            cls.config["username"] = username
            cls.config["password"] = password
        if Labconfig.module_name and Labconfig.module_id:
            cls.config["lockCRNBuildID"] = [{
                "productName": Labconfig.module_name,
                "buildID": Labconfig.module_id,
                "lock": True
            }]
        Labconfig.set_openUrl(openUrl)
        # return config
        return json.dumps(cls.config, ensure_ascii=False)

    @classmethod
    def configForZhiXingApp(cls, open_url="", user_name="", password=""):
        cls.config = {
            "jumpUrl": open_url,
            "UIDLoginInfo": {
                "uid": user_name,
                "pwd": password
            }
        }
        return json.dumps(cls.config, ensure_ascii=False)



    @classmethod
    def getTripAppShark(cls, sharkKey, locale="", appId=""):
        headers = {'content-type': 'application/json;charset=utf8'}
        url = "http://offline.fx.fws.qa.nt.ctripcorp.com/soa2/22336/getTripAppShark"
        # if cls.getCaseLocal():
        #     locale = cls.getCaseLocal();
        if CmtLogger.case_result_local:
            locale = CmtLogger.case_result_local
        for info in Labconfig.getSharkList():
            if sharkKey == info.get("sharkKey"):
                appId = info.get("appId")
                break
        body = {
            "sharkKey": sharkKey,
            "locale": locale,
            "appId": appId
        }
        responseInfo = requests.post(url, data=json.dumps(body), headers=headers).json()
        sharkvalue = responseInfo['sharkValue']
        print("对应的" + sharkKey + "的value值为:", sharkvalue)
        return sharkvalue

    @classmethod
    def concatUrl(cls, openUrl):
        taskId = cls.getTaskId()
        caseId = cls.getCaseId()
        # 判断openurl中是不是已有参数拼接
        taskId_arg = "&taskId=" if "?" in openUrl else "?taskId="
        final_url = openUrl + taskId_arg + str(taskId) + "&caseId=" + str(caseId) + "&canyonReportID=" + str(taskId) + "_mpaas_autotest"
        return final_url

    @classmethod
    def getTaskId(cls):
        # 获取taskid
        taskId = 0
        if Labconfig.mpaas_main_taskid > 0 :
            # 优先获取mpaas平台args中下发的主taskId
            return Labconfig.mpaas_main_taskid
        if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            taskId = Labconfig.get_ctaskid()
        return taskId

    @classmethod
    def getCaseId(cls):
        # 获取caseid
        # print("caseId is:" + str(CmtLogger.case_result_id))
        return CmtLogger.case_result_id

    @classmethod
    def getCaseLocal(cls):
        # 获取caseLocal
        print("caseLocal is:" + str(CmtLogger.case_result_local))
        return CmtLogger.case_result_local

    @classmethod
    def getPrimaryCaseId(cls):
        projectId = 905
        if Labconfig.getAppid() == "********" and not Labconfig.get_is_mpass():
            projectId = 905
        elif Labconfig.getAppid() == "********" and Labconfig.get_is_mpass():
            projectId = 1356
        elif Labconfig.getAppid() == "37" and not Labconfig.get_is_mpass():
            projectId = 906
        elif Labconfig.getAppid() == "37" and Labconfig.get_is_mpass():
            projectId = 1359
        # 获取case在表中的主键,仅限制自助下单调用
        caseName = cls.currentCaseName
        if HandleInfo.caseInfoList == None:
            url = "http://collects.fws.qa.nt.ctripcorp.com/api/getUiCaseInfoListFromMpaas" if Labconfig.get_is_mpass() else "http://collects.fws.qa.nt.ctripcorp.com/api/getUiCaseInfoList"
            req = {
                "branch": "master",
                "projectId": projectId
            }
            response = requests.post(url, data=json.dumps(req), headers={"Content-Type": "application/json"}).json()
            HandleInfo.caseInfoList = response
        for i in HandleInfo.caseInfoList:
            if i["caseName"] == caseName and (HandleInfo.currentTag in i["tags"]):
                HandleInfo.primaryCaseId = i["id"]
                return
        printUtil.printCaseDevice("未找到主键id")
        return

    @classmethod
    def isAppCrash(cls):
        if (DeviceInfo.getPlatform().lower() == "ios"):
            return False
        packageName = Labconfig.getPackageName()
        deviceName = DeviceInfo.getDevice().serialno
        adbres = ADB(serialno=deviceName).cmd("shell dumpsys window | grep mCurrentFocus")
        # 启动或跳转时偶尔会产生以下情况,等5s再判断
        if "mCurrentFocus=null" in adbres:
            sleep(5)
            adbres = ADB(serialno=deviceName).cmd("shell dumpsys window | grep mCurrentFocus")
        return packageName not in adbres

    @classmethod
    def isShareSuccess(self, poco, traceKey='htl_detail_share_pop', imageUrl="", webUrl="", title=""):
        result = poco(text='分享至').wait(timeout=5).exists()
        printUtil.printCaseDevice('check traceKey is ' + traceKey)
        traceValue = HandleInfo.getCurrentTraceValue(traceKey)
        result = result and (imageUrl in jsonpath.jsonpath(traceValue, "$.imageUrl")[0], "分享图片url有图片")
        result = result and (webUrl in jsonpath.jsonpath(traceValue, "$.webUrl")[0], "分享url有酒店id")
        result = result and (title in jsonpath.jsonpath(traceValue, "$.title")[0], "酒店title")
        return result

    @classmethod
    def isEnterPhoneCallPage(cls, poco):
        return poco(name='com.android.contacts:id/digits').wait(timeout=5).exists()

    @classmethod
    def isEnterEmailApp(cls, poco):
        result = poco(name='com.huawei.android.internal.app:id/icon').wait(timeout=5).exists()
        poco(text='通过邮件发送').wait(timeout=5).click()
        result = result and poco(text='QQ邮箱').wait(timeout=5).exists()
        return result

    @classmethod
    def caseTraceCheck(cls, req):
        req = json.dumps(req)
        headers = {'content-type': 'application/json'}
        url = "http://offline.fx.ctripcorp.com/soa2/22336/caseTraceCheck"
        printUtil.printCaseDevice("埋点校验接口请求:" + str(req))
        response = requests.post(url, data=req, headers=headers).json()
        printUtil.printCaseDevice("埋点校验返回结果:" + str(response))
        # if response["message"] == "fail":
        #     print("伏羲接口返回失败")

    @classmethod
    def findElement(cls, poco, textName, type='id', timeout=1):
        if DeviceInfo.getPlatform().lower() == "ios":
            if type == "name" or type == "text":
                return MyIOSPoco(poco, HandleInfo.IOSSession(name=textName), "name=" + textName, timeout=timeout)
            if type == "value":
                return MyIOSPoco(poco, HandleInfo.IOSSession(value=textName), "value=" + textName, timeout=timeout)
            # 匹配情况下poco的速度更快
            if type == "nameMatches" or type == "textMatches":
                return MyIOSPoco(poco, HandleInfo.IOSSession(value=textName), "nameMatches=" + textName, timeout=timeout)
        elif DeviceInfo.getPlatform().lower() == "android":
            printUtil.printCaseDevice('==开始查找元素：' + textName)
            if type is None:
                type = "name"
            if type == "id":
                if poco(id=textName).wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(textName)
                else:
                    return poco(textName)
            elif type == 'name':
                if poco(name=textName).wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(name=textName)
                else:
                    return poco(name=textName)
            elif type == 'text':
                if poco(text=textName).wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(text=textName)
                else:
                    return poco(text=textName)
            elif type == 'nameMatches':
                if poco(nameMatches=textName).wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(nameMatches=textName)
                else:
                    return poco(nameMatches=textName)
            elif type == 'textMatches':
                if poco(textMatches=textName).wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(textMatches=textName)
                else:
                    return poco(textMatches=textName)
            elif type == "clickId":
                if poco(nameMatches="([\s\S]*)" + textName + "([\s\S]*)").wait(timeout=timeout).exists():
                    printUtil.printCaseDevice('^=^查到元素' + type + "=" + textName)
                    return poco(nameMatches="([\s\S]*)" + textName + "([\s\S]*)")
                else:
                    return poco(nameMatches="([\s\S]*)" + textName + "([\s\S]*)")
            else:
                return poco(name=textName)

    @classmethod
    def searchElementwithType(self, poco, location, type='nameMatches', timeout=10, pageBottomElement=None):
        printUtil.printCaseDevice("我要滑动查找元素定位啦啦。。" + location)
        i = 0
        while i < timeout:
            i = i + 1
            if type == "id":
                if poco(location).exists():
                    printUtil.printCaseDevice('找到了不用滑动。。' + location)
                    # RunElement.insertElementPosition(poco(location), location)
                    return True

            elif type == 'name':
                if poco(name=location).exists():
                    printUtil.printCaseDevice('查到元素了' + location)
                    # RunElement.insertElementPosition(poco(name=location), location)
                    return True
            elif type == 'text':
                if poco(text=location).exists():
                    printUtil.printCaseDevice('查到元素了' + location)
                    return True
            elif type == 'nameMatches':
                if poco(nameMatches=location).exists():
                    printUtil.printCaseDevice('查到元素了' + location)
                    # RunElement.insertElementPosition(poco(nameMatches=location), location)
                    return True
            elif type == 'textMatches':
                if poco(textMatches=location).exists():
                    printUtil.printCaseDevice('查到元素了' + location)
                    # RunElement.insertElementPosition(poco(textMatches=location), location)
                    return True
            if pageBottomElement != None:
                if pageBottomElement.exists():
                    printUtil.printCaseDevice('已经滑动到底部了，还没有找到元素。。')
                    return False
            poco.scroll("vertical", 0.5)
            printUtil.printCaseDevice('元素未找到，继续滑动半屏。。')

            if i == timeout:
                return False

    @classmethod
    def loginForCtrip(cls, poco, userName, passWord):
        if cls.findElement(poco, "ctrip.android.view:id/tvLoginMiddle", type="name").exists():
            cls.findElement(poco, "ctrip.android.view:id/tvLoginMiddle", type="name").click()
        if cls.findElement(poco, "ctrip.android.view:id/tvLeftBtn", type="name").exists():
            cls.findElement(poco, "ctrip.android.view:id/tvLeftBtn", type="name").click()
        if cls.findElement(poco, "清除", type="name").exists():
            cls.findElement(poco, "清除", type="name").click()
        cls.findElement(poco, "ctrip.android.view:id/cetAccountEdit", type="name").click()
        cls.findElement(poco, "境内手机号/用户名/邮箱/卡号", type="text").set_text(userName)
        cls.findElement(poco, "ctrip.android.view:id/cetPasswordEdit", type="name").click()
        cls.findElement(poco, "登录密码", type="text").set_text(passWord)
        cls.findElement(poco, "ctrip.android.view:id/tvDoLogin", type="name").click()
        cls.findElement(poco, "ctrip.android.view:id/tvDialogRightBtn", type="name").click()
        sleep(2)
        return not cls.findElement(poco, "ctrip.android.view:id/tvDoLogin", type="name", timeout=3).exists()

    @classmethod
    def loginForTrip(cls, poco, userName, passWord, type=1):
        cls.findElement(poco, "以電郵／電話號碼登入", type="text").click()
        if (type == 2):
            cls.findElement(poco, '電話', type='text').click()
        cls.findElement(poco, "ctrip.english.debug:id/auto_complete_bottom", type="name")[0].set_text(userName)
        cls.findElement(poco, "ctrip.english.debug:id/auto_complete_bottom", type="name")[1].set_text(passWord)
        cls.findElement(poco, "ctrip.english.debug:id/login_btn", type="name").click()
        sleep(2)
        return not cls.findElement(poco, "ctrip.english.debug:id/login_btn", type="name").exists()

    # 根据list中的bunleName,获取对应的buildId
    @classmethod
    def getCrnBundleIdByChannelCode(cls, channelCode):
        channelInfosList = Labconfig.getCrnChannelBuildle()
        for channel in channelInfosList:
            if channel["channelCode"] == channelCode:
                return channel["bundleId"]
        return ""

    # @classmethod
    # def login(self, appId):
    #     if appId == "********":
    #         loginInfo = Labconfig.get_loginInfo()
    #         loginConfig = "{" + loginInfo[1:] + "}"
    #         config_str = str(base64.b64encode(loginConfig.encode('utf-8')), 'utf-8')
    #         ADB(serialno=current_device().serialno).shell(
    #             ['am', 'start', '-n',
    #              '%s/%s' % (Labconfig.getPackageName(), Labconfig.get_activity()),
    #              '--es',
    #              'configEnv %s' % (config_str)])
    #         sleep(3)

    @classmethod
    def transferBranchName(self, channelCode):
        branchName = ""
        # presaleBundleName = ["rn_hotel_packageList", "rn_hotel_packageOrders", "rn_hotel_package", "rn_ibu_hotel_package"]
        # presaleBundleNameOldBranch = ["rn_boom"]
        tripPresaleBundleNameBranch = ["rn_boom", "rn_ibu_hotel_package"]
        if (datetime.datetime.today().weekday()) == 3:
            # 3是周四
            printUtil.printCaseDevice("周四，获取rel分支")
            if channelCode in tripPresaleBundleNameBranch:
                branchName = "rel/trip_" + Labconfig.getAppversion() + "_prod"
            elif channelCode == "rn_search":
                branchName = "dev"
            else:
                branchName = "rel/0.7"
        else:
            printUtil.printCaseDevice("非周四，获取dev分支")
            if channelCode in tripPresaleBundleNameBranch:
                branchName = "rel/trip_" + Labconfig.getAppversion() + "_dev"
            elif channelCode == "rn_search":
                branchName = "dev"
            else:
                branchName = "dev/0.7"
        del tripPresaleBundleNameBranch
        return branchName

    @classmethod
    def findEndPath(cls, path, plit):
        class_sep_list = path.split(plit)
        class_name = class_sep_list[len(class_sep_list) - 1]
        return class_name

    @classmethod
    def findFirstPath(cls, path, plit):
        endName = path.find(plit)
        firstPath = path[0:endName - 1]
        return firstPath

    @classmethod
    def getSubString(cls, val, start, end):
        si = val.find(start)
        ei = val.find(end, si + len(start))
        return val[si + len(start):ei]

    # 截取字符串（包含指定字符)
    @classmethod
    def getSubStringKey(cls, val, start, end):
        si = val.find(start)
        ei = val.find(end)
        return val[si:ei]

    @classmethod
    def getSubStringEnd(cls, val, start):
        si = val.find(start)

        return val[si + len(start):len(val)]

    # 获取指定字符串开始的字符串(返回的内容不包含指定的字符）
    @classmethod
    def getSubStringEnd(cls, val, start):
        si = val.find(start)

        return val[si + len(start):len(val)]

    # 获取指定字符串开始的字符串(返回的内容包含指定的字符）
    @classmethod
    def getSubStringToEndKey(cls, val, start):
        ei = val.find(start)
        return val[ei:len(val)]

    @classmethod
    def getCaseName(cls, val):
        list = val.split("\\n")

        for item in list:
            if 'class ' in item:
                print('item leng' + str(len(item)))
                start = item.find('class ')
                end = item.find('(')
                name = item[start + len('class '): end]
                return name
        return ''

    @classmethod
    def get_os_platform(cls):

        sys_platform = platform.platform().lower()
        if "windows" in sys_platform:
            print("Windows")
            return "win"
        elif "macos" in sys_platform:
            print("Mac os")
            return "mac"
        elif "linux" in sys_platform:
            print("Linux")
            return "lin"
        else:
            print("其他系统")
            return 'other'

    # 页面红屏检测方法
    @classmethod
    def checkRetry(self, poco):
        errorInfo = "True"
        if poco(nameMatches="([\s\S]*)rn_redbox_dismiss_button([\s\S]*)").exists() or self.findElement(poco, "BOOM!!!",
                                                                                                       type='name',
                                                                                                       timeout=10).exists():
            printUtil.printCaseDevice('页面报错了')
            errorInfo = "红屏,无需重试"
        return errorInfo

    # 网络问题导致页面失败
    # pageType表示页面类型，因为flutter与RN元素不一致，因此需要区分
    @classmethod
    def networkError(cls, poco, pageType):
        if pageType == 0 and poco(nameMatches="([\s\S]*)网络不给力([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 1 and (poco(textMatches="([\s\S]*)网络不给力([\s\S]*)").exists() or poco(textMatches="([\s\S]*)请检查您的网络([\s\S]*)").exists()
                                or poco(name='数据获取失败，请重试').exists()):
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 2 and poco(textMatches="([\s\S]*)請檢查您的網絡設置或重新嘗試([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 3 and poco(textMatches="([\s\S]*)加載失敗([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 4 and poco(textMatches="([\s\S]*)抱歉，網絡連線有問題，請重試([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 5 and poco(nameMatches="([\s\S]*)網絡連線有問題([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 6 and poco(textMatches="([\s\S]*)加载失败，请再试试吧([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 7 and poco(textMatches="([\s\S]*)页面加载失败，请稍候重试([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        elif pageType == 8 and poco(textMatches="([\s\S]*)加载未成功([\s\S]*)").exists():
            printUtil.printCaseDevice("网络问题")
            return True
        return False

    @classmethod
    def checkNetworkError(self, poco):
        regex = r"([\s\S]*)(网络不给力|请检查您的网络|数据获取失败，请重试|請檢查您的網絡設置或重新嘗試|加載失敗|抱歉，網絡連線有問題，請重試|網絡連線有問題|加载失败，请再试试吧|页面加载失败，请稍候重试|加载未成功)([\s\S]*)"
        if poco(textMatches=regex).exists() or poco(nameMatches=regex).exists():
            printUtil.printCaseDevice("网络问题")
            return True

    # 页面黑屏检测方法
    @classmethod
    def checkBlackScreen(self, poco):
        errorInfo = "True"
        if len(poco(textMatches=".*")) <= 2:
            printUtil.printCaseDevice('页面黑屏了')
            errorInfo = "黑屏,无需重试"
        return errorInfo

    @classmethod
    def getWebhierarchy(cls, page):
        """
        获取web页面的dom树
        :param page: 页面对象
        :return: dom树
        """
        script = """
        () => {
            function getTextFromIframeUrlSync(iframeUrl) {
                try {
                    // 创建同步 XMLHttpRequest
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', iframeUrl, false); // false 表示同步请求
                    xhr.send();

                    if (xhr.status !== 200) {
                        return null;
                    }

                    const html = xhr.responseText;
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');

                    // 移除所有 <script> 元素
                    const scripts = doc.querySelectorAll('script');
                    scripts.forEach(script => script.remove());

                    return doc.body.innerText.split('\\n')
                        .map(line => line.trim()) // 去除每行前后的空格
                        .filter(line => line !== ""); // 过滤掉空字符串
                } catch (error) {
                    return null;
                }
            }

            function getAllIframeTextsSync() {
                // 获取所有 iframe 的 src 属性
                const iframes = document.querySelectorAll('iframe');
                const iframeUrls = Array.from(iframes)
                    .map(iframe => iframe.src)
                    .filter(src => src); // 过滤掉空的 src

                // 同步处理每个 URL
                const results = [];
                for (const url of iframeUrls) {
                    const textList = getTextFromIframeUrlSync(url);
                    if (textList !== null) {
                        results.push(...textList);
                    }
                }
                
                return results;
            }

            function getElementInfo(element) {
                if (element instanceof Element) {
                    let htmlText = [];
                    let testid = 'default';
                    // 判断是否为 body 元素，解决兜底获取Root节点下文本内容
                    // 或包含 taro-text 类的直接子元素，解决web页面分段渲染的场景
                    if (element.tagName.toLowerCase() === 'body') {
                        // 使用递归方法获取所有文本节点
                        function getAllTextNodes(node) {
                            let result = [];
                            // 如果是文本节点且内容不为空，则添加到结果中
                            if (node.nodeType === Node.TEXT_NODE) {
                                const text = node.textContent.trim();
                                if (text) result.push(text);
                            } else if (node.nodeType === Node.ELEMENT_NODE) {
                                // 跳过隐藏元素
                                const style = window.getComputedStyle(node);
                                if (style.display === 'none' || style.visibility === 'hidden') {
                                    return result;
                                }
                                
                                // 递归处理所有子节点
                                for (let i = 0; i < node.childNodes.length; i++) {
                                    const childResults = getAllTextNodes(node.childNodes[i]);
                                    for (let j = 0; j < childResults.length; j++) {
                                        result.push(childResults[j]);
                                    }
                                }
                            }
                            return result;
                        }
                        // 获取所有文本节点
                        htmlText = getAllTextNodes(element).filter(text => text !== '');
                        // 获取iframe的文本
                        htmlText.push(...getAllIframeTextsSync());
                    } else if (Array.from(element.childNodes)
                        .some(node =>
                            node.nodeType === Node.ELEMENT_NODE &&
                            node.tagName.toLowerCase() === 'span' &&
                            node.classList.contains('taro-text')
                        )) {
                        const bodyText = element.innerText;
                        htmlText = [bodyText];
                        element.children = [];
                    } else {
                        // 对于其他元素，只获取第一层级的文本内容
                        const textNodes = Array.from(element.childNodes)
                            .filter(node => node.nodeType === Node.TEXT_NODE)
                            .map(node => node.textContent.trim())
                            .filter(text => text !== '');

                        htmlText = textNodes;
                    }
                    if (element.getAttribute('testid')) {
                        if (element.getAttribute('testid').includes("viewID")) {
                            testid = JSON.parse(element.getAttribute('testid')).viewID;
                        } else {
                            testid = element.getAttribute('testid');
                        }
                    }
                    if (element.getAttribute('page-module')) {
                        testid = element.getAttribute('page-module');
                    }
                    if (element.getAttribute('data-exposure')) {
                        testid = JSON.parse(element.getAttribute('data-exposure')).ubtKey;
                    }
                    if (element.getAttribute('data-testid')) {
                        testid = element.getAttribute('data-testid');
                    }
                    const children = Array.from(element.children).map(getElementInfo).filter(child => child !== null);
                    if (testid === "default" && htmlText.length === 0 && children.length === 0) {
                        return null;
                    }
                    return {
                        tag: element.tagName,
                        name: testid,
                        payload: {
                            name: testid,
                            text: htmlText.join('\\n')
                        },
                        text_list: htmlText,
                        children: children
                    };
                } else {
                    return null;
                }
            }
            
            function isNotScriptOrIframe(element) {
                const tagName = element.tagName.toLowerCase();
                if (tagName === 'script') {
                    return false;
                }
                if (tagName === 'iframe') {
                    return false;
                }
                // 清理meta、style、link、title等标签
                if (tagName === 'meta' || tagName === 'style' || tagName === 'link' || tagName === 'title') {
                    return false;
                }
                return true;
            }
            
            // 会递归遍历DOM树的所有层级，清理所有不符合条件的节点
            function traverseAndCollect(selector) {
                const parentElement = document.querySelector(selector);
                if (!parentElement) {
                    return { tree: null };
                }

                // 创建DOM树的副本
                // 避免影响浏览器页面内容
                const clonedParent = parentElement.cloneNode(true);
                
                // 递归清理非法节点
                function cleanElement(element) {
                    const children = Array.from(element.children);
                    children.forEach((child) => {
                        if (!isNotScriptOrIframe(child)) {
                            element.removeChild(child);
                        } else {
                            cleanElement(child); // 递归处理子节点
                        }
                    });
                }

                // 清理整个树
                cleanElement(clonedParent);

                // 获取清理后的树结构
                const tree = getElementInfo(clonedParent);
                return { tree };
            }

            function traverseAndCollectText(node) {
                function traverse(node) {
                    if (!node) return;

                    if (node.children && node.children.length > 0) {
                        node.children.forEach(child => traverse(child));
                    }

                    if (node.name === "default" && node.text_list && node.text_list.filter(item => item !== "").length > 0) {
                        if (node.parent) {
                            node.parent.text_list = node.parent.text_list.concat(node.text_list).filter(item => item !== "");
                            node.parent.payload.text = node.parent.text_list.join('\\n');
                        }
                    }
                }

                function addParentReferences(node, parent = null) {
                    // 添加空节点检查
                    if (!node) return;
                    
                    node.parent = parent;
                    if (node.children && Array.isArray(node.children)) {
                        // 确保children是数组且过滤掉null值
                        node.children = node.children.filter(child => child != null);
                        node.children.forEach(child => addParentReferences(child, node));
                    }
                }

                // 添加空节点检查
                if (!node) return { tree: null, string_list: [] };

                addParentReferences(node);
                traverse(node);

                function removeParentReferences(node) {
                    // 添加空节点检查
                    if (!node) return;
                    
                    delete node.parent;
                    if (node.children && Array.isArray(node.children)) {
                        node.children.forEach(child => removeParentReferences(child));
                    }
                }

                removeParentReferences(node);

                return {
                    tree: node,
                    string_list: node.text_list || [],
                };
            }

            function removeEmptyTestIdNodes(node) {
                function traverse(node) {
                    if (!node || !node.children) return node;

                    node.children = node.children
                        .map(child => traverse(child))
                        .filter(child => child && child.name !== "default");

                    return node;
                }

                return traverse(node);
            }

            function filterTree(node) {
                if (!node) return null;

                if (node.name === "default") {
                    let filteredChildren = node.children.map(filterTree).filter(child => child !== null);
                    return filteredChildren.length > 0 ? filteredChildren : null;
                }

                return {
                    tag: node.tag,
                    name: node.name,
                    text_list: node.text_list,
                    children: node.children.map(filterTree).filter(child => child !== null).flat()
                };
            }
            // 获取页面body的第一个div=document.querySelector("body > div"); （旧规则）
            // 新规则：获取页面body下所有非script、iframe的元素
            return traverseAndCollectText(traverseAndCollect('body').tree).tree;
        }
        """
        hierarchy = page.evaluate(script)
        if hierarchy and hierarchy.get('payload'):
            hierarchy.get('payload')['name'] = "<Root>"
            hierarchy['name'] = "<Root>"
        return hierarchy

    @classmethod
    def getAllTextInModel(cls, poco, rootName):
        # 获取页面控件树
        hierarchy = None
        if isinstance(poco, Poco):
            hierarchy = poco.agent.hierarchy.dump()
        else:
            hierarchy = cls.getWebhierarchy(poco)
        result = ""
        targetElement = cls.findTargetElement(hierarchy, rootName)
        if targetElement is not None:
            result = cls.concatTextInModel(targetElement, result)
        return result.replace(" ", " ").replace("￼", "").replace("\n", "").replace("\t", "").replace("\r", "")


    @classmethod
    def findTargetElement(cls, hierarchy, targetElementName):
        if isinstance(hierarchy, dict):
            payload = hierarchy.get('payload')
            if targetElementName in payload.get('name', '') or targetElementName in payload.get('desc', ''):
                return hierarchy
            elif hierarchy.get('children') is not None:
                return cls.findTargetElement(hierarchy.get('children'), targetElementName)
            else:
                return None
        elif isinstance(hierarchy, list):
            if hierarchy is None or len(hierarchy) == 0:
                return None
            for i in hierarchy:
                payload = i.get('payload')
                if targetElementName in payload.get('name', '') or targetElementName in payload.get('desc', ''):
                    return i
                elif i.get('children') is not None:
                    find = cls.findTargetElement(i.get('children'), targetElementName)
                    if find is not None:
                        return find
            return None

    GAP = "GAP"

    @classmethod
    def concatTextInModel(cls, hierarchy, result):
        if not isinstance(hierarchy, dict):
            return result  # 如果传入的hierarchy不是字典类型，直接返回result

        payload = hierarchy.get('payload', {})
        text_list = payload.get('text_list', [])
        # 判断是否是app，如果是web无需判断是否是isValidNodeName
        if hierarchy.get('tag', "") == 'BODY':
            if payload.get('text') is not None:
                result = result + payload.get('text').replace("\n", cls.GAP) + cls.GAP
        else:
            if payload.get('text') is not None and cls.isValidNodeName(payload.get('name')):
                result = result + payload.get('text').replace("\n", cls.GAP) + cls.GAP
        if payload.get('name') is not None and cls.isValidName(payload.get('name')):
            result = result + payload.get('name').replace("\n", cls.GAP) + cls.GAP
        if payload.get('desc') is not None and cls.isValidName(payload.get('desc')):
            result = result + payload.get('desc').replace("\n", cls.GAP) + cls.GAP
        if text_list is not None and cls.isValidNodeName(payload.get('name')):
            # 将text_list中的元素拼接成一个字符串，每个元素之间用cls.GAP分隔
            result = result + cls.GAP.join(text_list) + cls.GAP

        # 对children进行遍历处理
        children = hierarchy.get('children', [])
        if children is not None and cls.isValidNode(payload.get('name')):
            for child in children:
                result = cls.concatTextInModel(child, result)  # 递归调用处理每个子节点

        return result

    @classmethod
    def isValidName(cls, name):
        if name is None:
            return True
        invalid_name = ["android", "clickId"]
        for i in invalid_name:
            if i in name:
                return False
        return True

    @classmethod
    def isValidNodeName(cls, name):
        if name is None:
            return True
        invalid_node_name = ["id/clock", "<Root>"]
        for i in invalid_node_name:
            if i in name:
                return False
        return True

    @classmethod
    def isValidNode(cls, name):
        invalid_node_name = ["systemui:id/statusIcons"]
        for i in invalid_node_name:
            if i in name:
                return False
        return True

    @classmethod
    def is_element_id(cls, element_id: str) -> bool:
        if element_id in ['<Root>']:
            return True
        if element_id.startswith("{") and element_id.endswith("}"):
            element_id_key = ["clickId", "__abt_custom_key"]
            if any(key in element_id for key in element_id_key):
                return True
            return False
        return re.match(r'^[a-z][a-z0-9_A-Z|\.\/\:]*$', element_id) is not None

    # @classmethod
    # def remove_special_char(cls, text: str) -> str:
    #     return text.replace(" ", " ").replace("￼", "").replace("\n", "").replace("\t", "").replace("\r", "")
    @classmethod
    def is_invalid_node_prefix(cls, node_name) -> bool:
        # 优化以下面列表前缀开头的内容，改成android:id/content
        invalid_node_prefix = ["com.android.systemui", "com.android", "<Root>", "android:id/content", "default"]
        for prefix in invalid_node_prefix:
            if node_name.startswith(prefix):
                return True
        return False

    @classmethod
    def is_invalid_node_prefix_web(cls, node_name) -> bool:
        # web平台，过滤掉default节点
        invalid_node_prefix = ["default"]
        for prefix in invalid_node_prefix:
            if node_name.startswith(prefix):
                return True
        return False

    @classmethod
    def extractTextControls(cls, poco, rootName: str) -> (dict, list):
        # 获取页面控件树
        hierarchy = None
        if isinstance(poco, Poco):
            hierarchy = poco.agent.hierarchy.dump()
        else:
            hierarchy = cls.getWebhierarchy(poco)
        targetElement = cls.findTargetElement(hierarchy, rootName)
        if targetElement is None:
            return {}, []

        element_id_text_list_mapping = {}
        unnamed_text_set = set()
        queue = [(targetElement, None)]  # 初始化队列，加入根节点及其父级控件ID（根节点没有父级控件，所以为None）

        while queue:
            current_node, parent_id = queue.pop(0)  # 取出当前节点及其父级控件ID
            payload = current_node.get('payload', {})
            # Flutter 技术栈的控件中，不存在 text 属性，需要把下级控件的 name 属性作为控件的文本
            # CRN 技术栈的控件中，同时存在 name 和 text 属性，text 可能为控件的文本
            name = payload.get("name", "")
            text = payload.get("text", "")
            maybe_element_id = name if len(name) > 0 else text
            if maybe_element_id == "":
                continue

            if len(current_node.get('children', [])) > 0:
                # 将当前节点的子节点加入队列
                # 开发加的 maybe_element_id 可能乱七八糟，为了保证所有节点都能被扫描到，不再使用正则做匹配了，直接将父级控件的 ID 传入
                for child in current_node.get('children', []):
                    queue.append((child, maybe_element_id))

            if isinstance(poco, Poco):
                # 安卓平台需要过滤的节点
                if cls.is_invalid_node_prefix(maybe_element_id):
                    continue
            else:
                # web平台需要过滤的节点
                if cls.is_invalid_node_prefix_web(maybe_element_id):
                    continue

            # 判断当前节点是否是父级控件
            if cls.is_element_id(maybe_element_id):  # CRN 技术栈，name 一定符合正则表达式，因为文本放在 text 字段中
                # 确保 element_id_text_list_mapping 中存在 key 为 maybe_element_id 的键
                if maybe_element_id not in element_id_text_list_mapping:
                    element_id_text_list_mapping[maybe_element_id] = []
                if len(text) > 0 and not cls.is_element_id(text):  # CRN 技术栈的控件中，同时存在 name 和 text 属性，text 可能为控件的文本
                    element_id_text_list_mapping[maybe_element_id].append(text)
            elif parent_id is not None and len(parent_id) > 0 and not cls.is_invalid_node_prefix(parent_id):  # Flutter 技术栈，文本都是单独挂在下级控件。如果当前节点 maybe_element_id 不是 ID，且有父级控件，则将其添加到父级控件的文本列表中
                if parent_id not in element_id_text_list_mapping:
                    element_id_text_list_mapping[parent_id] = []
                final_text = text if len(text) > 0 else name  # 优先取 text
                element_id_text_list_mapping[parent_id].append(final_text)  # 将文本控件的name添加到对应父级控件的列表中

        # 过滤 element_id_text_list_mapping 中的空列表
        element_id_text_list_mapping = {k: v for k, v in element_id_text_list_mapping.items() if len(v) > 0}
        if not isinstance(poco, Poco) and '<Root>' in element_id_text_list_mapping:
            # 将 element_id_text_list_mapping 中的 value 根据\\n\\n分割成一个列表
            element_id_text_list_mapping['<Root>'] = element_id_text_list_mapping['<Root>'][0].split('\n')
        return element_id_text_list_mapping, list(unnamed_text_set)

    @classmethod
    def getSharkValueByLocale(cls, shark_key, app_id):
        shark_info = {
            "sharkKey": shark_key,
            "value": "",
            "appId": app_id
        }
        for info in cls.getSharkList():
            if info.get("sharkKey") == shark_info.get("sharkKey") and info.get("appId") == shark_info.get("appId"):
                shark_info = info
                break
        if shark_info.get("value") == "":
            shark_info["value"] = cls.getTripAppShark(shark_info.get("sharkKey"))
            cls.getSharkList().append(shark_info)
        return shark_info.get("value")

    @classmethod
    def getPicUpload(cls, poco):
        for info in Labconfig.getSharkList():
            try:
                locale = "zh_HK"
                if CmtLogger.case_result_local:
                    locale = CmtLogger.case_result_local
                if (info.get("sharkKey") + "_" + locale) in cls.getExistSharkList():
                    continue
                sharkKey = cls.getSharkValueByLocale(info.get("sharkKey"), info.get("appId"))
                print("本次截图的sharkkey是 %s %s 翻译后是 %s" % (info.get("sharkKey"), locale, sharkKey))
                element = poco(name=sharkKey) if poco(name=sharkKey).exists() else poco(text=sharkKey)
                if element.exists():
                    # 截图
                    pic = Capture.markScreenshotUrlByNode(element)
                    # 上传
                    labelId = ""
                    if HandleInfo.getMetaInfo():
                        labelId = HandleInfo.getMetaInfo()['label']
                    t = threading.Thread(target=cls.sendRequestToHotelCode,
                                         args=(pic, locale, info.get("appId"), labelId, info.get("sharkKey"),
                                               Labconfig.get_ctaskid()))
                    t.setDaemon(True)
                    t.start()
                    cls.getExistSharkList().append((info.get("sharkKey") + "_" + locale))
            except Exception:
                print("screenshot and upload error.....")

    @classmethod
    def sendRequestToHotelCode(cls, pic, locale, appid, labelId, sharkKey, taskId):
        UploadSharkInfo = {
            "picUrl": pic,
            "local": locale,
            "appid": appid,
            "labelId": labelId,
            "sharkkey": sharkKey,
            "taskId": taskId
        }
        print("截图request--------------" + json.dumps(UploadSharkInfo))
        url = "http://casrunner.fat2.qa.nt.ctripcorp.com/casemanage/saveUiSharkResult"
        requests.post(url, data=json.dumps(UploadSharkInfo), headers={"Content-Type": "application/json"}).json()

    @classmethod
    def set_labelId_and_platform(cls, label_id, platform_code):
        Labconfig.set_label_id(label_id)
        Labconfig.set_case_platform(platform_code)

    @classmethod
    def set_trace_id_and_platform(cls, trace_log_id: str, platform_code: int):
        platform_code = Labconfig.correct_mpass_platform(platform_code)
        Labconfig.set_trace_log_id(trace_log_id)
        Labconfig.set_case_platform(platform_code)

    @classmethod
    def getIosPackageName(cls, appid, isSim):
        if appid == "********":
            if isSim:
                return "ctrip.com"
            else:
                return "com.ctrip.inner.wireless"
        elif appid == "37":
            if isSim:
                return "com.ctrip.EBooking"
            else:
                return "com.inter.EBooking"
        elif appid == '5125':
            return 'com.qunar.iphoneclient8'

    """
        获取configEnv方法，当前用于ai生成场景，无需UI项目中配置跳转url，通过生成case中带入
        eg. get_config(page_url="https://m.ctrip.com/webapp/hotel/hoteldetail/123456.html",mockKey="")
        """

    @classmethod
    def get_config(cls, page_url="", ABList=[], IncrementList=[], switchList=[], mockKey="",mcdConfig=[],
                   openPopLayer="F", login=True, latitude="", longitude="", bds=None,flightMockKey="",runEnv="fat",localeCode="",uid = "",userName = "",password="",pro = "", bu=None, projectName=None, platform=None, aiGenerateExtension=None):
        if bu:
            # 设置bu
            Labconfig.buId = int(bu)
        printUtil.printCaseDevice("页面跳转链接为：" + page_url)
        automationType = "web" if DeviceInfo.getDeviceName() == "web" else "android"
        # web场景，根据mpaas下发的pipeline信息获取真实运行环境，并替换原url--暂时仅支持酒店场景
        if automationType == "web" and Labconfig.buId == 1 and Labconfig.pipeline_env:
            page_url = re.sub(r'\.fat\d+\.', f'.{Labconfig.pipeline_env.lower()}.', page_url)
            printUtil.printCaseDevice("替换实际运行环境后的页面跳转链接为：" + page_url)
        Labconfig.web_cookies = []
        if platform:
            # 兼容android用例，H5共用的场景
            platform = Labconfig.correct_mpass_platform(int(platform), automationType)
            Labconfig.platformId = int(platform)
            
        if automationType == "web":
            # 所有web自动化case，设置_auto_test_=True到cookie，用于区分web自动化case
            LoginUtil.addWebCookies("_auto_test_", "True", page_url)
            
            # web端设置ab实验，参考格式：ABList=[["240729_HTL_CFBFF","B"], ["240729_HTL_CFBFF","A"]]
            # 设置到cookie的_abtest_internal_ 字段，格式如下："240729_HTL_CFBFF:B,240729_HTL_CFBFF:A"
            if ABList:
                ab_test_internal = ""
                for i, ab in enumerate(ABList):
                    ab_test_internal += f"{ab[0]}:{ab[1]}"
                    if i < len(ABList) - 1:
                        ab_test_internal += ","
                LoginUtil.addWebCookies("_abtest_internal_", ab_test_internal, page_url)
        
        # 首先判断url是否有labuiframeLogin并且是web
        if "labuiframeLogin" in page_url and DeviceInfo.getDeviceName() == "web":
            printUtil.printCaseDevice("当前运行平台为web-labuiframeLogin")                
            return cls.configForWebCommon(page_url=LoginUtil.labuiframeLogin(page_url), mock_key=mockKey if mockKey else flightMockKey)

        # 判断metaInfo是否有bu和projectName
        if bu and projectName and platform:
            # 有的话调用接口获取项目配置信息，拿到项目配置的extension
            extension = GetProjectConfigInfoUtil.getProjectConfigInfo(projectName, int(platform), automationType,  int(bu))
            # 判断是否拿到了extension
            if extension and extension.get("loginType"):
                # 其次判断metaInfo是否有aiGenerateExtension，有的话和extension合并，相同key的值以aiGenerateExtension为准
                if aiGenerateExtension:
                    printUtil.printCaseDevice("aiGenerateExtension:{}".format(aiGenerateExtension))
                    extension = {**extension, **aiGenerateExtension}
                printUtil.printCaseDevice("extension:{}".format(extension))
                #  最后根据extension的loginType进行登录
                # ctrip登录
                if extension.get("loginType") == "commonForCtripLogin" and (extension.get("account") and extension.get("password") or (extension.get("ticket") and extension.get("duid"))):
                    # 判断执行自动化类型为web
                    if DeviceInfo.getDeviceName() == "web":
                        printUtil.printCaseDevice("当前运行平台为CTrip Web")
                        if extension.get("ticket") and extension.get("duid"):
                            Labconfig.set_ticket(extension.get("ticket"))
                            Labconfig.set_duid(extension.get("duid"))
                        else:
                            if (uid or userName) and password:
                                LoginUtil.commonForCtripLogin(uid or userName, password)
                            else:
                                LoginUtil.commonForCtripLogin(extension.get("account"), extension.get("password"))
                        LoginUtil.addWebCookies("cticket", Labconfig.get_ticket(), page_url)
                        LoginUtil.addWebCookies("DUID", Labconfig.get_duid(), page_url)
                        return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                    else:
                        printUtil.printCaseDevice("当前运行平台为CTrip App")
                        if runEnv == "pro" and pro:
                            try:
                                password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                            except Exception as e:
                                printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                            return CommonAction.configForCtripCommon(openUrl=page_url.format(**pro), ABList=ABList,switchList=switchList, IncrementList=IncrementList,
                                                                    mockKey=mockKey,openPopLayer=openPopLayer, login=login, latitude=latitude,
                                                                    longitude=longitude, isBds=True,runEnv=runEnv,uid=uid,password=password)

                        try:
                            # 优先使用传入的uid和password，其次使用Labconfig中的账号密码，最后使用原密码
                            if uid and password:
                                password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                            elif extension.get("account") and extension.get("password"):
                                uid = extension.get("account")
                                try:
                                    password = base64.b64decode(extension.get("password").encode('utf-8')).decode('utf-8')
                                except Exception as e:
                                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                                    password = extension.get("password")
                            elif Labconfig.password and Labconfig.account:
                                uid = Labconfig.account
                                try:
                                    password = base64.b64decode(Labconfig.password.encode('utf-8')).decode('utf-8')
                                except Exception as e:
                                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                                    password = Labconfig.password
                        except Exception as e:
                            printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")

                        if bds == None:
                            page_url = page_url.replace("{hotelId}","1")
                            page_url = page_url.replace("{roomId}", "1")
                            return cls.configForCtripCommon(openUrl=page_url, ABList=ABList, IncrementList=IncrementList, switchList=switchList, mockKey=mockKey,
                                                            openPopLayer=openPopLayer, login=login, latitude=latitude, longitude=longitude, isBds=False,mcdConfig=mcdConfig,runEnv=runEnv,uid=uid,password=password)
                        cls.bds_config = bds
                        # bds 场景不传mockKey，且使用环境fat10000;openUrl拼接暂仅支持bds场景
                        return CommonAction.configForCtripCommon(openUrl=page_url.format(**bds), ABList=ABList, switchList=switchList, IncrementList=IncrementList, mockKey=mockKey,
                                                                openPopLayer=openPopLayer, login=login, latitude=latitude, longitude=longitude, isBds=True,uid=uid,password=password)
                # trip登录
                elif extension.get("loginType") == "commonForTripLogin" and (extension.get("account") and extension.get("password") or (extension.get("ticket") and extension.get("duid"))):
                    if DeviceInfo.getDeviceName() == "web":
                        printUtil.printCaseDevice("当前运行平台为Trip Web")
                        if extension.get("ticket") and extension.get("duid"):
                            Labconfig.set_ticket(extension.get("ticket"))
                            Labconfig.set_duid(extension.get("duid"))
                        else:
                            if (uid or userName) and password:
                                LoginUtil.commonForTripLogin(uid or userName, password)
                            else:
                                LoginUtil.commonForTripLogin(extension.get("account"), extension.get("password"))
                        LoginUtil.addWebCookies("cticket", Labconfig.get_ticket(), page_url)
                        LoginUtil.addWebCookies("DUID", Labconfig.get_duid(), page_url)
                        return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                    else:
                        printUtil.printCaseDevice("当前运行平台为Trip App")
                        if runEnv == "pro" and pro:
                            try:
                                password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                            except Exception as e:
                                printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                            return CommonAction.configForTripCommon(openUrl=page_url.format(**pro), ABList=ABList,flightMockKey=flightMockKey, switchList=switchList,
                                                                    mockKey=mockKey,login=login, isBds=True, mcdConfig=mcdConfig,
                                                                    localeCode=localeCode,runEnv=runEnv,userName=userName,password=password, latitude=latitude, longitude=longitude)

                        try:
                            # 优先使用传入的uid和password，其次使用Labconfig中的账号密码，最后使用原密码
                            if userName and password:
                                password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                            elif extension.get("account") and extension.get("password"):
                                userName = extension.get("account")
                                try:
                                    password = base64.b64decode(extension.get("password").encode('utf-8')).decode('utf-8')
                                except Exception as e:
                                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                                    password = extension.get("password")
                            elif Labconfig.password and Labconfig.account:
                                userName = Labconfig.account
                                try:
                                    password = base64.b64decode(Labconfig.password.encode('utf-8')).decode('utf-8')
                                except Exception as e:
                                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                                    password = Labconfig.password
                        except Exception as e:
                            printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                            password = password
                        if bds == None:
                            page_url = page_url.replace("{hotelId}", "1")
                            page_url = page_url.replace("{roomId}", "1")
                            return cls.configForTripCommon(openUrl=page_url, ABList=ABList, flightMockKey=flightMockKey, switchList=switchList, mockKey=mockKey, login=login,
                                                           mcdConfig=mcdConfig,runEnv=runEnv,localeCode=localeCode,userName=userName,password=password, latitude=latitude, longitude=longitude)
                        return CommonAction.configForTripCommon(openUrl=page_url.format(**bds), ABList=ABList, flightMockKey=flightMockKey, switchList=switchList, mockKey=mockKey,
                                                                login=login, isBds=True,mcdConfig=mcdConfig,localeCode=localeCode,userName=userName,password=password, latitude=latitude, longitude=longitude)
                # 酒店订单offline登录
                elif extension.get("loginType") == "commonForOfflineLogin":
                    if DeviceInfo.getDeviceName() == "web":
                        printUtil.printCaseDevice("当前运行平台为Offline")
                        if (uid or userName) and password:
                            LoginUtil.commonForOfflineLogin(uid or userName, password, "web")
                        else:
                            LoginUtil.commonForOfflineLogin(extension.get("account"), extension.get("password"), "web")
                        LoginUtil.addWebCookies("FAT_cas_principal", Labconfig.get_ticket(), page_url)
                        return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                    else:
                        printUtil.printCaseDevice("当前只支持web类型自动化登录")
                        return
                # 酒店ebk web登录
                elif extension.get("loginType") == "commonForEbkLogin":
                        if DeviceInfo.getDeviceName() == "web":
                            printUtil.printCaseDevice("当前运行平台为Ebk")
                            if (uid or userName) and password:
                                return cls.configForWebCommon(page_url=LoginUtil.commonForEbkLogin(page_url, uid or userName, password, "web"), mock_key=mockKey if mockKey else flightMockKey, login=login)
                            else:
                                return cls.configForWebCommon(page_url=LoginUtil.commonForEbkLogin(page_url, extension.get("account"), extension.get("password"), "web"), mock_key=mockKey if mockKey else flightMockKey, login=login)
                        else:
                            printUtil.printCaseDevice("当前只支持web类型自动化登录")
                            return
                # 市场营销链接中台登录
                elif extension.get("loginType") == "commonForZongHengUatLogin":
                        if DeviceInfo.getDeviceName() == "web":
                            printUtil.printCaseDevice("当前运行平台为携程营销链接中台")
                            userName = (uid or userName) if (uid or userName) else extension.get("account")
                            password = password if password else extension.get("password")
                            _UAT_sso_lat_assertion_,_UAT_sso_lat_assertion_signature_ = LoginUtil.commonForZongHengUatLogin(userName, password)
                            LoginUtil.addWebCookies("_UAT_sso_lat_assertion_", _UAT_sso_lat_assertion_, page_url)
                            LoginUtil.addWebCookies("_UAT_sso_lat_assertion_signature_", _UAT_sso_lat_assertion_signature_, page_url)
                            return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                        else:
                            printUtil.printCaseDevice("当前只支持web类型自动化登录")
                            return
                # 商旅web登录
                elif extension.get("loginType") == "commonForTripBizLogin":
                    if DeviceInfo.getDeviceName() == "web":
                        printUtil.printCaseDevice("当前运行平台为商旅web")
                        userName = (uid or userName) if (uid or userName) else extension.get("account")
                        password = password if password else extension.get("password")
                        LoginUtil.commonForTripBizLogin(userName, password)
                        LoginUtil.addWebCookies("cticket", Labconfig.get_ticket(), page_url)
                        LoginUtil.addWebCookies("DUID", Labconfig.get_duid(), page_url)
                        return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                    else:
                        printUtil.printCaseDevice("当前只支持web类型自动化登录")
                        return
                # 租车宝web登录
                elif extension.get("loginType") == "commonForSbuLogin":
                    if DeviceInfo.getDeviceName() == "web":
                        login_info_dal = MySqlConnect.get_uid_login_info("SBUAccountInfo", 18)
                        if login_info_dal and len(login_info_dal) == 2:
                            citckt = login_info_dal[1]
                            LoginUtil.addWebCookies("bticket", citckt, page_url)
                            return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login)
                        else:
                            printUtil.printCaseDevice("SBU web 登录信息获取失败")
                            return
                    else:
                        printUtil.printCaseDevice("当前只支持web类型自动化登录")
                        return
                #智行火车票登录
                elif extension.get("loginType") == "commonForZhiXingLogin":
                    if DeviceInfo.getDeviceName() == "web":
                        # todo
                        printUtil.printCaseDevice("当前只支持app类型自动化登录")
                        return
                    else:
                        user_name = (uid or userName) if (uid or userName) else extension.get("account")
                        password = password if password else extension.get("password")
                        return CommonAction.configForZhiXingApp(page_url, user_name=user_name, password=password)

        # 酒店默认登录
        # 判断是web还是app自动化
        if DeviceInfo.getDeviceName() == "web":

            # 为防止url占位符出现keyerror，通过replace替换
            if bds != None:
                if 'hotelId' in bds:
                    page_url = page_url.replace("{hotelId}", bds['hotelId'])
                if 'roomId' in bds:
                    page_url = page_url.replace("{roomId}", bds['hotelId'])
            elif pro != "":
                if 'hotelId' in pro:
                    page_url = page_url.replace("{hotelId}", pro['hotelId'])
                if 'roomId' in pro:
                    page_url = page_url.replace("{roomId}", pro['hotelId'])
            else:
                page_url = page_url.replace("{hotelId}", "1")
                page_url = page_url.replace("{roomId}", "1")

            #web场景，当使用mook数据，增加两个cookie
            if mockKey or flightMockKey:
                LoginUtil.addWebCookies("MockCaseId", mockKey if mockKey else flightMockKey, page_url)
                LoginUtil.addWebCookies("x-ctrip-mock-caseid", mockKey if mockKey else flightMockKey, page_url)
            if localeCode and "_" in localeCode:
                region = localeCode.split("_")[1]
                page_url = page_url.replace('{region}', region).replace('{locale}', localeCode)
            # 对于T-H5，增加cookie关闭弹窗
            if Labconfig.platformId == 6:
                LoginUtil.addWebCookies("ibu-h5-pop-hotel", "0", page_url)
            #判断是否是web生产环境
            printUtil.printCaseDevice("当前运行平台为web")
            if runEnv == "pro":
                pro_ticket, pro_duid = "", ""
                # web生产根据根据不同的uid获取C或者T的登录信息
                if int(Labconfig.platformId) == 3 or int(Labconfig.platformId) == 5:
                    printUtil.printCaseDevice("当前运行平台为web,运行环境为pro,渠道为Ctrip")
                    pro_ticket, pro_duid = LoginUtil.getAccountInfo("M6405505246", "", "", 1)
                elif int(Labconfig.platformId) == 4 or int(Labconfig.platformId) == 6:
                    printUtil.printCaseDevice("当前运行平台为web,运行环境为pro,渠道为Trip")
                    pro_ticket, pro_duid = LoginUtil.getAccountInfo("_TIHK1c05b3kzowzv", "", "", 2)
                printUtil.printCaseDevice(f"获取到的登录信息: {pro_ticket}, uid: {pro_duid}")
                # 对原有的ticket和duid进行重置
                Labconfig.set_ticket(pro_ticket)
                Labconfig.set_duid(pro_duid)
                LoginUtil.addWebCookies("cticket", Labconfig.get_ticket(), page_url,"pro")
                LoginUtil.addWebCookies("DUID", Labconfig.get_duid(), page_url,"pro")
                # 生产环境，首先判断page_url中是否有autotestEnvForPro，不关心autotestEnvForPro大小写，如果有，用autotestEnvForPro中指定的cookie，否则C端：默认rb, T端默认sgp
                match = re.search(r'autotestEnvForPro=(\w+)', page_url, re.IGNORECASE)
                temp_cookie = ""
                if match:
                    temp_cookie = match.group(1)
                else:
                    if int(Labconfig.platformId) == 3 or int(Labconfig.platformId) == 5:
                        temp_cookie = "rb"
                    elif int(Labconfig.platformId) == 4 or int(Labconfig.platformId) == 6:
                        temp_cookie = "sgp"
                printUtil.printCaseDevice(f"生产环境，指定访问的堡垒cookie是：{temp_cookie}")
                LoginUtil.addWebCookies("env",temp_cookie, page_url)
                return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey,
                                              login=login, ABList=ABList)
            # 兼容一个任务中同时有生产和测试的case，需要重新读表设置cticket和DUID
            if int(Labconfig.platformId) == 3 or int(Labconfig.platformId) == 5:
                printUtil.printCaseDevice("当前运行平台为web,运行环境为fat,渠道为Ctrip")
                fat_ticket, fat_duid = LoginUtil.getAccountInfo("htlautotest", "", "", 1)
            elif int(Labconfig.platformId) == 4 or int(Labconfig.platformId) == 6:
                printUtil.printCaseDevice("当前运行平台为web,运行环境为fat,渠道为Trip")
                fat_ticket, fat_duid = LoginUtil.getAccountInfo("_TIHK105r80r5y5bd", "", "", 2)
            LoginUtil.addWebCookies("cticket", Labconfig.get_ticket(), page_url)
            LoginUtil.addWebCookies("DUID", Labconfig.get_duid(), page_url)
            if bds == None:
                page_url = page_url.replace("{hotelId}", "1")
                page_url = page_url.replace("{roomId}", "1")
                return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login, ABList=ABList)
            else:
                cls.bds_config = bds
                return cls.configForWebCommon(page_url=page_url, mock_key=mockKey if mockKey else flightMockKey, login=login, ABList=ABList)
        if Labconfig.getAppid() == "********":
            printUtil.printCaseDevice("当前运行平台为CTrip")

            # 为防止url占位符出现keyerror，通过replace替换
            if bds != None:
                if 'hotelId' in bds:
                    page_url = page_url.replace("{hotelId}", bds['hotelId'])
                if 'roomId' in bds:
                    page_url = page_url.replace("{roomId}", bds['hotelId'])
            elif pro != "":
                if 'hotelId' in pro:
                    page_url = page_url.replace("{hotelId}", pro['hotelId'])
                if 'roomId' in pro:
                    page_url = page_url.replace("{roomId}", pro['hotelId'])
            else:
                page_url = page_url.replace("{hotelId}", "1")
                page_url = page_url.replace("{roomId}", "1")

            if runEnv == "pro" and pro:
                try:
                    password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                except Exception as e:
                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                return CommonAction.configForCtripCommon(openUrl=page_url, ABList=ABList,switchList=switchList, IncrementList=IncrementList,
                                                         mockKey=mockKey,openPopLayer=openPopLayer, login=login, latitude=latitude,
                                                         longitude=longitude, isBds=True,runEnv=runEnv,uid=uid,password=password)

            try:
                # 优先使用传入的uid和password，其次使用Labconfig中的账号密码，最后使用原密码
                if uid and password:
                    password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                elif Labconfig.password and Labconfig.account:
                    uid = Labconfig.account
                    try:
                        password = base64.b64decode(Labconfig.password.encode('utf-8')).decode('utf-8')
                    except Exception as e:
                        printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                        password = Labconfig.password
            except Exception as e:
                printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")

            if bds == None:
                return cls.configForCtripCommon(openUrl=page_url, ABList=ABList, IncrementList=IncrementList, switchList=switchList, mockKey=mockKey,
                                                openPopLayer=openPopLayer, login=login, latitude=latitude, longitude=longitude, isBds=False,mcdConfig=mcdConfig,runEnv=runEnv,uid=uid,password=password)
            cls.bds_config = bds
            # bds 场景不传mockKey，且使用环境fat10000;openUrl拼接暂仅支持bds场景
            return CommonAction.configForCtripCommon(openUrl=page_url, ABList=ABList, switchList=switchList, IncrementList=IncrementList, mockKey=mockKey,
                                                     openPopLayer=openPopLayer, login=login, latitude=latitude, longitude=longitude, isBds=True,uid=uid,password=password)
        elif Labconfig.getAppid() == "37":
            printUtil.printCaseDevice("当前运行平台为Trip")

            # 为防止url占位符出现keyerror，通过replace替换
            if bds != None:
                if 'hotelId' in bds:
                    page_url = page_url.replace("{hotelId}", bds['hotelId'])
                if 'roomId' in bds:
                    page_url = page_url.replace("{roomId}", bds['hotelId'])
            elif pro != "":
                if 'hotelId' in pro:
                    page_url = page_url.replace("{hotelId}", pro['hotelId'])
                if 'roomId' in pro:
                    page_url = page_url.replace("{roomId}", pro['hotelId'])
            else:
                page_url = page_url.replace("{hotelId}", "1")
                page_url = page_url.replace("{roomId}", "1")

            if runEnv == "pro" and pro:
                try:
                    password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                except Exception as e:
                    printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                return CommonAction.configForTripCommon(openUrl=page_url, ABList=ABList,flightMockKey=flightMockKey, switchList=switchList,
                                                        mockKey=mockKey,login=login, isBds=True, mcdConfig=mcdConfig,
                                                        localeCode=localeCode,runEnv=runEnv,userName=userName,password=password, latitude=latitude, longitude=longitude)

            try:
                # 优先使用传入的uid和password，其次使用Labconfig中的账号密码，最后使用原密码
                if userName and password:
                    password = base64.b64decode(password.encode('utf-8')).decode('utf-8')
                elif Labconfig.password and Labconfig.account:
                    userName = Labconfig.account
                    try:
                        password = base64.b64decode(Labconfig.password.encode('utf-8')).decode('utf-8')
                    except Exception as e:
                        printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                        password = Labconfig.password
            except Exception as e:
                printUtil.printCaseDevice(f"密码解码发生异常：{e},已经使用原密码登录")
                password = password
            if bds == None:
                return cls.configForTripCommon(openUrl=page_url, ABList=ABList, flightMockKey=flightMockKey, switchList=switchList, mockKey=mockKey, login=login,mcdConfig=mcdConfig,
                                               runEnv=runEnv,localeCode=localeCode,userName=userName,password=password, latitude=latitude, longitude=longitude)
            return CommonAction.configForTripCommon(openUrl=page_url, ABList=ABList, flightMockKey=flightMockKey, switchList=switchList, mockKey=mockKey,
                                                    login=login, isBds=True,mcdConfig=mcdConfig,localeCode=localeCode,userName=userName,password=password, latitude=latitude, longitude=longitude)

    """
    检测页面是否进入及有无异常问题
    """

    @classmethod
    @outer_time('判断打开的页面是否正常进入方法')
    def waitPageLoad(self, poco, page_flag, time_out=60):
        printUtil.printCaseDevice("页面标识为:" + page_flag)
        #兼容配置多个page_flag的场景
        page_flag_list = re.split(r'[，,]', page_flag)
        if isinstance(poco, Page):
            # web类型自动化等待页面加载
            for pageflag in page_flag_list:
                poco.wait_for_load_state("load", timeout=time_out * 1000)
                poco.wait_for_load_state("domcontentloaded", timeout=time_out * 1000)
                # poco.wait_for_load_state("networkidle", timeout=time_out * 1000)
                target_element = WebElementLocator.elementLocator(poco, pageflag)
                if target_element:
                    return True
                else:
                    continue
            return False
        if page_flag is None or len(page_flag) == 0 :
            return False
        result = False
        for pageflag in page_flag_list:
            HandleInfo.getClientid()
            target_proxy = SimpleProxy(poco, pageflag, False)
            if target_proxy.wait(timeout=time_out).exists():
                return True
            else:
                continue
        # 判断网络失败、红屏、crash方法
        if (not result) and self.checkNetworkError(poco):
            return CmtLogger.net_message_error
        if (not result) and "无需重试" in self.checkRetry(poco):
            return "红屏,无需重试"
        if (not result) and self.isAppCrash():
            return "app crash,无需重试"
        else:
            return result

    # 针对订单详情页进入页面关闭弹窗
    @classmethod
    def close_popups(self,testbasecase):
        if Labconfig.suitPlatform.lower() == "android" or Labconfig.suitPlatform.lower() == "ios":
            printUtil.printCaseDevice("app和H5共用case关闭弹窗开始等待页面渲染")
            time.sleep(60)
            printUtil.printCaseDevice("app和H5共用case关闭弹窗等待结束")
            testbasecase.clickBack()

    #针对复杂指令打印页面树
    @classmethod
    def print_msg_for_method_result(self,testbasecase):
        print("复杂指令发生页面跳转")
        if any(base.__name__ == 'WebBaseCase' for base in testbasecase.testCase.__class__.__bases__):
            tree = CommonAction.getWebhierarchy(testbasecase.page)
        else:
            tree = testbasecase.poco.agent.hierarchy.dump()
        print("ui_tree_info:{}".format(json.dumps(tree, default=ElementItem.custom_serializer)))
        
    @classmethod
    def assert_is_retry(cls):
        # 调用接口查询当前taskId是否在表里面已有了，如果已有则表示当前case是重试，需要中断
        # http://127.0.0.1:8080/api/generateConfig/getAiGenerateCaseByTaskId?taskId=79430
        try:
            taskId = Labconfig.mpaas_main_taskid
            if not taskId:
                printUtil.printCaseDevice("当前taskId为空，无法查询是否是重试")
                return True
            url = "http://htlmysticmare.fws.qa.nt.ctripcorp.com/api/generateConfig/getAiGenerateCaseByTaskId?taskId={}".format(taskId)
            response = requests.get(url)
            if response.status_code == 200:
                if response.text:
                    data = response.json()
                    printUtil.printCaseDevice("当前case是mpaas平台job重试，需要中断（当前AI生成项目使用的mpaas job重试开关为打开状态）")
                    printUtil.printCaseDevice(f"上次执行结果：{data.get('errMsg', '')}")
                    return False
                else:
                    return True
            else:
                return True
        except Exception as e:
            printUtil.printCaseDevice(f"查询mpaas平台job重试失败：{e}")
            return True