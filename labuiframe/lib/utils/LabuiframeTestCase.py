# coding=utf-8

import re
import traceback
import unittest
import warnings

import eventlet
import time

from pocounit.fixture import FixtureUnit
from pocounit.result.metainfo import MetaInfo
from pocounit.result.collector import PocoResultCollector
from pocounit.result.trace import ScriptTracer
from pocounit.result.action import ActionRecorder
from pocounit.result.app_runtime import AppRuntimeLog
from pocounit.result.runner_runtime import Runner<PERSON>unt<PERSON>Log
from pocounit.result.assertion import AssertionRecorder
from pocounit.result.site_snapshot import SiteSnapshot

from pocounit.case import PocoTestCase

from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.utils.LabRecord import LabScreenRecorder

from pocounit.case import PocoTestCase
from pocounit.result import PocoTestResult
from pocounit.utils.outcome import Outcome
from labuiframe.lib.config.labconfig import Labconfig
from airtest.core.api import stop_app

from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.utils.HandleInfo import HandleInfo
from labuiframe.lib.utils.capture import  CmtLogger
SPECIAL_CHARS = re.compile(r'[\/\\\.:*?"<>|]')
class LabuiframeTestCase(PocoTestCase):
    def __init__(self):
        unittest.TestCase.__init__(self)
        FixtureUnit.__init__(self)

        # check testcase name
        self.testcase_name = re.sub(SPECIAL_CHARS, lambda g: '-', self.name())

        collector = PocoResultCollector(self.project_root, [""], self.testcase_name,
                                        self.test_case_dir)
        self.set_result_collector(collector)

        meta_info_emitter = MetaInfo(collector)
        runner_runtime_log = RunnerRuntimeLog(collector)
        tracer = ScriptTracer(collector)
        action_recorder = ActionRecorder(collector)
        app_runtime_log = AppRuntimeLog(collector)
        assertion_recorder = AssertionRecorder(collector)
        site_snapshot = SiteSnapshot(collector)

        self.add_result_emitter('metaInfo', meta_info_emitter)
        self.add_result_emitter('runnerRuntimeLog', runner_runtime_log)
        self.add_result_emitter('tracer', tracer)
        self.add_result_emitter('actionRecorder', action_recorder)
        self.add_result_emitter('appRuntimeLog', app_runtime_log)
        self.add_result_emitter('assertionRecorder', assertion_recorder)
        self.add_result_emitter('siteSnapshot', site_snapshot)
        self.meta_info_emitter = meta_info_emitter
        self._exceptions = set()

    def initScreenRecorder(self):
        collector = PocoResultCollector(self.project_root, [""], self.testcase_name,
                                        self.test_case_dir)
        screen_recorder = LabScreenRecorder(collector)
        # if Labconfig.getIsHotelWirelessAppGroup() == "False":
        #     self.add_result_emitter('screenRecorder', screen_recorder)
        # 非酒店或酒店mpaas且模拟器场景
        if Labconfig.getIsHotelWirelessAppGroup() == "False" or (Labconfig.getIsHotelWirelessAppGroup() == "True" and Labconfig.get_is_mpass() and Labconfig.get_mpass_device_type() == 1 ):
            self.add_result_emitter('screenRecorder', screen_recorder)
            printUtil.printCaseDevice("开启录屏")


    def run(self, result=None):
        run_count = 1
        eventlet.monkey_patch(time=True)
        while True:
            # 如果是重试，需要执行setUpClass操作
            if (Labconfig.retry > 0 and run_count > 1) or (Labconfig.getIsHotelWirelessAppGroup() == "True" and HandleInfo.currentCirculate > 1 and CmtLogger.getcase_result_bds()=="") :
                self.__init__()
                self.setUpClass()
            result = result or self.defaultTestResult()
            errBegin = len(result.errors) + len(result.failures) + len(result.detail_errors)
            if not isinstance(result, PocoTestResult):
                raise TypeError('Test result class should be subclass of PocoTestResult. '
                                'Current test result instance is "{}".'.format(result))

            # 自动把当前脚本add到collector的脚本watch列表里
            collector = self.get_result_collector()
            collector.add_testcase_file(self.test_case_filename)

            self.meta_info_emitter.set_testcase_metainfo(self.testcase_name, HandleInfo.getMetaInfo())

            # register addon
            if not self.__class__._addons:
                self.__class__._addons = []
            for addon in self._addons:
                addon.initialize(self)

            self.meta_info_emitter.test_started(self.testcase_name)

            # start result emitter
            for name, emitter in self._result_emitters.items():
                try:
                    emitter.start()
                except Exception as e:
                    warnings.warn('Fail to start result emitter: "{}". You can report this error to the developers or just '
                                  'ignore it. Error message: \n"{}"'
                                  .format(emitter.__class__.__name__, traceback.format_exc()))


            # run test
            # this method never raises


            # ret = self._super_run_modified(result)
            try:
                timeout = 1800 if self.isDebug() else Labconfig.get_finalTimeOut()
                printUtil.printCaseDevice("计时开始:{}".format(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))
                with eventlet.Timeout(timeout, True):
                    ret = self._super_run_modified(result)
                printUtil.printCaseDevice("计时终止:{}".format(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))))
            except Exception as e:
                ret = self.defaultTestResult()
                ret.detail_errors.append(["", "", "{}".format(str(e))])
                printUtil.printCaseDevice("用例执行失败:{}", str(e))


            self.record_exceptions(result.errors)

            # stop result emitter
            for name, emitter in self._result_emitters.items():
                try:
                    emitter.stop()
                except Exception as e:
                    warnings.warn('Fail to stop result emitter: "{}". You can report this error to the developers or just '
                                  'ignore it. Error message: \n"{}"'
                                  .format(emitter.__class__.__name__, traceback.format_exc()))

            self.meta_info_emitter.test_ended(self.testcase_name)

            # handle result
            # 这里的result是累计错误，单个case进入重试逻辑,result里面需要有新增错误
            errFinal = len(result.errors) + len(result.failures) + len(result.detail_errors)
            if (errBegin != errFinal) and (result.detail_errors or result.errors or result.failures):
                unretry_message = "无需重试"
                try:
                    printUtil.printCaseDevice("打印detail_errors", str(ret.detail_errors))
                    if "eventlet.timeout.Timeout" in str(ret.detail_errors) or "用例耗时超出" in str(ret.detail_errors[0][2]) or unretry_message in str(result.detail_errors[0][2]):
                        run_count = Labconfig.retry + 1
                    #兼容aiagent调试成功场景不再重试
                    printUtil.printCaseDevice("get_ai_agent_result的值为:{}".format(str(Labconfig.get_ai_agent_result())))
                    if Labconfig.get_ai_agent_result():
                        run_count = Labconfig.retry + 1
                except Exception as e:
                    printUtil.printCaseDevice("用例执行成功，teardown失败", str(e))
                if run_count > 0 and run_count <= Labconfig.retry:
                    printUtil.printCaseDevice("重试第" + str(run_count) + "次！")
                    # 判断是web还是app
                    if DeviceInfo.getDeviceName().lower() == "web":
                        # 重试前退出web浏览器
                        self.close()
                    else:
                        # 重试前退出app清除数据
                        HandleInfo.clearBdsData()
                        HandleInfo.clearSocketMessageDict()
                        stop_app(self.package_name)
                    run_count += 1
                    result.testsRun -= 1
                    if len(result.errors) > 0:
                        result.errors.pop()
                    if len(result.failures) > 0:
                        result.failures.pop()
                    if len(result.detail_errors) > 0:
                        result.detail_errors.pop()
                    continue
                if (HandleInfo.currentCirculate < HandleInfo.circulateTimes) and Labconfig.getIsHotelWirelessAppGroup() == "True":
                    HandleInfo.clearBdsData()
                    HandleInfo.clearSocketMessageDict()
                    HandleInfo.setCirculateInfos(HandleInfo.currentCirculate+1, HandleInfo.circulateTimes, HandleInfo.TagLists)
                    stop_app(self.package_name)
                    continue
                self.meta_info_emitter.test_fail(self.testcase_name)
                pass
            elif (HandleInfo.currentCirculate < HandleInfo.circulateTimes) and Labconfig.getIsHotelWirelessAppGroup() == "True":
                HandleInfo.clearBdsData()
                HandleInfo.clearSocketMessageDict()
                HandleInfo.setCirculateInfos(HandleInfo.currentCirculate+1, HandleInfo.circulateTimes, HandleInfo.TagLists)
                continue
            else:
                self.meta_info_emitter.test_succeed(self.testcase_name)
                pass
            return ret



    # @func_set_timeout(Labconfig.get_caseTimeOut())
    # def super_run_modified(self,result):
    #     return self._super_run_modified(result)

