# -*- coding: utf-8 -*-
import requests
import json

class MarsMockUtil:
    MARS_MOCK_URL = "http://mars.ibu.ctripcorp.com"

    @classmethod
    def enableMockInfo(cls, mockIds, isUseMock):
        request = {
            "mockIds": mockIds,
            "useMock": isUseMock
        }
        try:
            response = requests.post(cls.MARS_MOCK_URL + "/enableMockInfo", data = json.dumps(request),
                                     headers={"Content-Type": "application/json"})
            if response.status_code == 200:
                return response.text
            else:
                print(response.text)
                return None
        except Exception as e:
            print("Enable Mars Mock Error!")
            return None

    @classmethod
    def searchRecentHistory(cls, mockId):
        request = {
            "mockId": mockId
        }
        try:
            response = requests.post(cls.MARS_MOCK_URL + "/searchRecentHistory", data = json.dumps(request),
                                     headers={"Content-Type": "application/json"})
            if response.status_code == 200:
                return response.text
            else:
                print(response.text)
                return None
        except Exception as e:
            print("Mars searchRecentHistory Error!")
            return None

    @classmethod
    def enablePackage(cls, soleId, suiteId, isUseMock):
        request = {
            "soleId": soleId,
            "pid": suiteId,
            "enable": isUseMock
        }
        try:
            response = requests.post(cls.MARS_MOCK_URL + "/enablePackage", data = json.dumps(request),
                                     headers = {"Content-Type": "application/json"})
            if response.status_code == 200:
                return response.text
            else:
                print(response.text)
                return None
        except Exception as e:
            print("Mars Enable Package Mock Error!")
            return None

    @classmethod
    def searchPackageRecentHistory(cls, suiteId):
        request = {
            "pkgId": suiteId
        }
        try:
            response = requests.post(cls.MARS_MOCK_URL + "/searchPackageRecentHistory", data = json.dumps(request),
                                     headers = {"Content-Type": "application/json"})
            if response.status_code == 200:
                return response.text
            else:
                print(response.text)
                return None
        except Exception as e:
            print("Mars Mock searchPackageRecentHistory Error!")
            return None
