from labuiframe.lib.utils.printUtil import printUtil
from playwright.sync_api import Page, Locator
from typing import Union

class WebElementLocator:
    # 将元素定位属性收口到WebElementLocator的类属性
    preserved_keys = ['data-exposure', 'page-module', 'data-testid', 'testid', 'text']

    @classmethod
    def elementLocator(cls, pageLocator: Union[Page, Locator], target: str):
        """
        解析元素
        :param target: 目标元素标识
        :return: 定位到的元素或None
        """
        # 去除target中的前后的'\"'
        target = target.strip('\\"')
        
        # 定义精确匹配选择器
        exact_selectors = [
            f'[testid="{target}"]',
            f'[page-module="{target}"]', 
            f'[id="{target}"]',
            f'[name="{target}"]',
            f'[data-testid="{target}"]'
        ]
        query_selector_by_testId = ', '.join(exact_selectors)
        
        # 定义模糊匹配选择器
        partial_selectors = [
            f'[testid*="{target}"]',
            f'[page-module*="{target}"]',
            f'[id*="{target}"]',
            f'[name*="{target}"]',
            f'[placeholder*="{target}"]',
            f'[value*="{target}"]',
            f'[alt*="{target}"]',
            f'[data-exposure*="{target}"]',
            f'[data-testid*="{target}"]'
        ]
        query_selector_by_testId_match = ', '.join(partial_selectors)

        # 定义文本和XPath选择器
        query_selector_by_text = f'//*[text() = "{target}"]'
        query_selector_by_text_match = f'//*[contains(text(), "{target}")]'
        query_selector_by_xpath = f'{target}'

        # 按优先级尝试不同的定位策略
        locator_strategies = [
            (query_selector_by_testId, "精确属性匹配"),
            (query_selector_by_text, "文本内容精准匹配"),
            (query_selector_by_testId_match, "模糊属性匹配"),
            (query_selector_by_text_match, "文本内容模糊匹配"),
            (query_selector_by_xpath, "XPath匹配")
        ]

        for selector, strategy in locator_strategies:
            try:
                locator = pageLocator.locator(selector).locator('visible = true')
                if locator.count() > 0:
                    locator.first.scroll_into_view_if_needed()
                    printUtil.printCaseDevice(f"通过{strategy}找到元素: {target}")
                    return locator
            except Exception:
                continue
        #如果监听到新的iframe，先定位到iframe，再在iframe下按照上述寻找策略寻找元素
        if pageLocator.evaluate("window._iframeInfo") and len(pageLocator.evaluate("window._iframeInfo")) > 0:
            for iframe in pageLocator.evaluate("window._iframeInfo"):
                iframe_xpath = iframe["xpath"]
                try:
                    iframe_locator = pageLocator.frame_locator(f"xpath={iframe_xpath}")
                    if iframe_locator:
                        for selector, strategy in locator_strategies:
                            try:
                                locator = iframe_locator.locator(selector).locator('visible = true')
                                if locator.count() > 0:
                                    locator.first.scroll_into_view_if_needed()
                                    printUtil.printCaseDevice(f"在iframe下寻找元素，通过{strategy}找到元素: {target}")
                                    return locator
                            except Exception:
                                continue
                except Exception:
                    continue
            
        return None
