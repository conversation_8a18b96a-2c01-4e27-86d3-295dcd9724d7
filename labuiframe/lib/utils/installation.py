# coding=utf-8
__author__ = 'lnx3032'


import re
import os

try:
    import subprocess32 as subprocess
except:
    import subprocess

from airtest.utils.apkparser.apk import APK
from airtest.core.api import install,uninstall,sleep

from labuiframe.lib.utils.device import DeviceInfo

def install_app(localpath,platform=None):
    if platform == None:
        platform = DeviceInfo.getPlatform()

    if platform.lower() == 'android':
        install(localpath, install_options=["-g"])
    elif platform.lower() == 'ios':
        install_ios_app(localpath)
    else:
        raise RuntimeError("'platform' cant be none!!!")

def uninstall_app(bundleid,platform=None):
    if platform == None:
        platform = DeviceInfo.getPlatform()

    if platform.lower() == 'android':
        try:
            uninstall(bundleid)
        except:
            print("***** uninstall error")
    elif platform.lower() == 'ios':
        uninstall_ios_app(bundleid)
    else:
        raise RuntimeError("'platform' cant be none!!!")

def install_ios_app(localpath):
    # ucmd = 'idevice_id -l'
    outputUrl = os.path.dirname(localpath) + '/' + DeviceInfo.get_ios_udid()
    # 参照idb那个方法封装判断type simulator
    if (DeviceInfo.get_is_simulator() == 1):
        # localpath是一个zip，先解压缩
        import zipfile
        zip_file = zipfile.ZipFile(localpath)
        zip_list = zip_file.namelist()  # 得到压缩包里所有文件
        for f in zip_list:
            # 只解压 .app.zip 结尾的文件
            if f.endswith('.app.zip'):
                # 将该zip文件也解压
                zip_file.extract(f, outputUrl)
                appFile = outputUrl + '/' + f
                # 解压 .app.zip 文件 为 .app 文件
                appFile_app = zipfile.ZipFile(appFile)
                appFile_app.extractall(outputUrl + '/' + f[:-4])
                appFile_app.close()
                os.remove(appFile)
                localpath = outputUrl + '/' + f[:-4]
                if "CTRIP_WIRELESS" in localpath:
                    localpath = localpath + "/CTRIP_WIRELESS.app"
            if f.endswith('.app/'):
                zip_file.extractall(outputUrl)
                localpath = outputUrl + '/' + f[:-1]
        zip_file.close()
    if not os.path.exists(localpath):
        raise EnvironmentError('file "%s" not exists.' % localpath)
    print("In IOS app installer")
    os.system(subprocess.list2cmdline(['idb', 'install', '--udid', DeviceInfo.get_ios_udid(), localpath]))
    print("In IOS app installer End!")
    if os.path.exists(outputUrl):
        try:
            shutil.rmtree(outputUrl)
        except:
            pass
    if os.path.exists(localpath):
        try:
            shutil.rmtree(localpath)
        except:
            pass
    sleep(10)
    # raise NotImplementedError

def uninstall_ios_app(bundleid):
    print("In IOS app Uninstaller")
    os.system(subprocess.list2cmdline(['idb', 'uninstall', '--udid', DeviceInfo.get_ios_udid(), bundleid]))
    print("In IOS app Uninstaller End!")
    sleep(3)


def install_android_app(adb_client, localpath, force_reinstall=False):
    apk_info = APK(localpath)
    package_name = apk_info.package

    def _get_installed_apk_version(package):
        package_info = adb_client.shell(['dumpsys', 'package', package])
        matcher = re.search(r'versionCode=(\d+)', package_info)
        if matcher:
            return int(matcher.group(1))
        return None

    try:
        apk_version = int(apk_info.androidversion_code)
    except (RuntimeError, ValueError):
        apk_version = 0
    installed_version = _get_installed_apk_version(package_name)
    print('installed version is {}, installer version is {}. force_reinstall={}'.format(installed_version, apk_version, force_reinstall))
    if installed_version is None or apk_version > installed_version or force_reinstall:
        if installed_version is not None:
            force_reinstall = True
        if hasattr(adb_client, 'install_app'):
            adb_client.install_app(localpath, force_reinstall)
        else:
            adb_client.install(localpath, force_reinstall)
        return True
    return False


def uninstall_android_app(adb_client, package):
    if hasattr(adb_client, 'uninstall_app'):
        adb_client.uninstall_app(package)
    else:
        adb_client.uninstall(package)
