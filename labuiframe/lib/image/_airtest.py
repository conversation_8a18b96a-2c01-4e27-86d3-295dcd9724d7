from __future__ import absolute_import

import math

from PIL import Image
from labuiframe.lib.utils.capture import Capture
from poco.pocofw import *
from poco.proxy import *
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from labuiframe.lib.utils.device import DeviceInfo

from labuiframe.lib.utils import image_utils
from .image import Region
from ._triggers import *

if tp.TYPE_CHECKING:
    from .watcher import Watcher

__all__ = ["WatcherPoco", "AndroidUiautomationPoco", "WatcherScreenShot"]


class WatcherPoco(object):
    def __init__(self, poco, watcher=None):
        self._poco = poco  # type:Poco
        self.width = poco.get_screen_size()[0]
        self.height = poco.get_screen_size()[1]
        self._ui_object = None # type:UIObjectProxy
        self._watcher_ui_object = None
        self._watcher = watcher

    def __call__(self, name=None, **kw):
        self._ui_object = self._poco(name, **kw)
        self._watcher_ui_object = WatcherUIObject(self._ui_object, self, self._watcher)
        return self._watcher_ui_object

    def wait_for_any(self, objects, timeout=120):
        self._poco.wait_for_all(objects,timeout)

    def wait_for_all(self, objects, timeout=120):
        self._poco.wait_for_all(objects,timeout)

    def freeze(self):
        return WatcherPoco(self._poco.freeze(), self._poco)

    def wait_stable(self):
        self._poco.wait_stable()

    def sleep_for_polling_interval(self):
        self._poco.sleep_for_polling_interval()

    def agent(self):
        return self._poco.agent()

    def click(self, pos):
        self._poco.click(pos)

    def swipe(self, p1, p2=None, direction=None, duration=2.0):
        return self._poco.swipe(p1, p2, direction, duration)

    def long_click(self, pos, duration=2.0):
        return self._poco.long_click(pos, duration)

    def snapshot(self, width=720):
        return self._poco.snapshot(width)

    def snapshot_suitable_width(self):
        return self._poco.snapshot(self.get_size()[0])

    def get_size(self):
        return self._poco.get_screen_size()


class WatcherUIObject(object):
    def __init__(self, ui_object, watcher_poco, watcher, ):
        # type:(UIObjectProxy,WatcherPoco, Watcher)->None
        self._ui_object = ui_object  # type:UIObjectProxy
        self._watcher = watcher  # type:Watcher
        self._watcher_poco = watcher_poco # type:WatcherPoco

    def get_ui_object(self):
        return self._ui_object

    def child(self, name=None, **attrs):
        return WatcherUIObject(self._ui_object.child(name, **attrs), self._watcher_poco, self._watcher)

    def children(self):
        return WatcherUIObject(self._ui_object.children(), self._watcher_poco,  self._watcher)

    def offspring(self, name=None, **attrs):
        return WatcherUIObject(self._ui_object.offspring(name), self._watcher_poco,  self._watcher)

    def sibling(self, name=None, **attrs):
        return WatcherUIObject(self._ui_object.offspring(name, **attrs), self._watcher_poco,  self._watcher)

    def __getitem__(self, item):
        return WatcherUIObject(self._ui_object[item], self._watcher_poco,  self._watcher)

    def click(self, focus=None, sleep_interval=None):
        if self._watcher is not None:
            trigger = MouseTrigger(MouseAction.click)
            self._watcher.add_mouse_trigger(MouseAction.click)
        self._ui_object.click(focus, sleep_interval)

    def long_click(self, duration=2.0):
        if self._watcher is not None:
            self._watcher.add_mouse_trigger("long_click", self)
        self._ui_object.long_click(duration)

    def swipe(self, dir, focus, duration=0.5):
        if self._watcher is not None:
            self._watcher.add_mouse_trigger("swipe",self)

    def get_region(self):
        width = self._watcher_poco.get_size()[0]
        height = self._watcher_poco.get_size()[1]
        x = math.ceil(self._ui_object.get_position(False)[0] * width)
        y = math.ceil(self._ui_object.get_position(False)[1] * height)
        width = math.ceil(self._ui_object.get_size()[0] * width)
        height = math.ceil(self._ui_object.get_size()[1] * height)
        return Region(x, y, width, height)




class WatcherScreenShot(object):

    def __init__(self, poco):
        # type: (Poco,Image.Image) -> None
        self._poco = poco  # type:Poco
        self._watcher_poco = WatcherPoco(self._poco)
        if DeviceInfo.getPlatform().lower() == 'android':
            self._screenshot_base64 = self._poco.snapshot(self._poco.get_screen_size()[0])[0]
        elif DeviceInfo.getPlatform().lower() == 'ios':
            self._screenshot_base64 = None
        self._screenshot_filename = Capture.getScreenShotFileName()



    @staticmethod
    def create_from_base64(poco, base64):
        return WatcherScreenShot(poco, base64)

    @staticmethod
    def create_from_image(poco, image):
        return WatcherScreenShot(poco, image)

    def get_sub_screenshot_by_region(self, region):
        # type:()->WatcherScreenShot
        screen_shot = image_utils.get_image_part(self._screenshot, region)
        return WatcherScreenShot(self._poco, screen_shot)

    def get_image(self):
        return self._screenshot

    def get_base64(self):
        return self._screenshot_base64

    def get_fileName(self):
        return self._screenshot_filename

    def get_bytes(self):
        return image_utils.get_base64_from_image(self._screenshot)

    def get_ui_object_region(self, ui_object):
        # type:(UIObjectProxy)->Region
        self._watcher_poco.get_region_by_ui_object(ui_object)
