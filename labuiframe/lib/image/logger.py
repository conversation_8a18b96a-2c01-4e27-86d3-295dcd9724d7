from __future__ import absolute_import

import sys
import logging.handlers
import functools

_DEFAULT_WATCHER_LOGGER_NAME = 'Watcher'
_DEFAULT_WATCHER_FORMATTER = logging.Formatter('%(asctime)s [%(levelname)s] %(name)s: %(message)s')


class _Logger(object):

    def __init__(self, name=__name__,
                level=logging.DEBUG,
                handler_factory=lambda: None,
                formatter=None):
        self._name = name
        self._logger = None
        self._handler_factory = handler_factory
        self._handler = None
        self._formatter = formatter
        self._level = level

    def open(self):
        # Actually create the handler
        self._handler = self._handler_factory()
        if self._handler:
            self._handler.setLevel(self._level)
            self._logger = logging.getLogger(self._name)
            self._logger.setLevel(self._level)
            if self._formatter is not None:
                self._handler.setFormatter(self._formatter)
            self._logger.addHandler(self._handler)

    def close(self):
        if self._logger:
            self._handler.close()
            self._logger.removeHandler(self._handler)
            self._logger = None
            self._handler = None

    def info(self, msg):
        if self._logger:
            self._logger.info(msg)

    def debug(self, msg):
        if self._logger:
            self._logger.debug(msg)

    def error(self, msg):
        if self._logger:
            self._logger.error(msg)

    def warning(self,msg):
        if self._logger:
            self._logger.warning(msg)


class StdoutLogger(_Logger):
    def __init__(self, name=_DEFAULT_WATCHER_LOGGER_NAME, level=logging.DEBUG):
        handler_factory = functools.partial(logging.StreamHandler, sys.stdout)
        super(StdoutLogger, self).__init__(name, level, handler_factory, _DEFAULT_WATCHER_FORMATTER)


class FileLogger(_Logger):
    def __init__(self, filename="watcher.log", mode='a', encoding=None, delay=0,
                 name=_DEFAULT_WATCHER_LOGGER_NAME, level=logging.DEBUG):

        handler_factory = functools.partial(logging.FileHandler, filename, mode, encoding, delay)
        super(FileLogger, self).__init__(name, level, handler_factory, _DEFAULT_WATCHER_FORMATTER)


class NullLogger(_Logger):
    def __init__(self, name=_DEFAULT_WATCHER_LOGGER_NAME, level=logging.DEBUG):
        super(NullLogger, self).__init__(name, level)


class ServerLogger(_Logger):
    def __init__(self,host='',url='',name=_DEFAULT_WATCHER_LOGGER_NAME,level=logging.DEBUG):
        handler_factory = functools.partial(logging.handlers.HTTPHandler, host, url, "Get")
        super(ServerLogger, self).__init__(name,level,handler_factory,_DEFAULT_WATCHER_FORMATTER)


_logger_to_use = None
_logger = None


def set_logger(logger=None):
    global _logger_to_use  # type:_Logger
    _logger_to_use = logger


def open_():
    global _logger
    _logger = _logger_to_use
    if _logger is not None:
        _logger.open()


def close():
    global _logger  # type:_Logger
    if _logger is not None:
        _logger.close()
        _logger = None


def info(msg):
    if _logger is not None:
        _logger.info(msg)


def debug(msg):
    if _logger is not None:
        _logger.debug(msg)


def error(msg):
    if _logger is not None:
        _logger.error(msg)


def warning(msg):
    if _logger is not None:
        _logger.warning(msg)
