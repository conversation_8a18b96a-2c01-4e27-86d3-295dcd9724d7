from __future__ import absolute_import

import typing as tp
from collections import OrderedDict
from enum import Enum

_TRIGGER_TYPE_FIELD_NAME = "triggerType"


class MouseAction(Enum):
    click = 'Click'
    longClick = 'LongClick'
    swipe = 'Swipe'
    drag = 'Drag'


class TriggerType(Enum):
    unKnown = 'UnKnown'
    mouse = "Mouse"
    text = 'Text'


class MouseTrigger(object):
    def __init__(self, mouse_action, region, location):
        # type:(MouseAction,Region,tp.Text)->None
        self.action = mouse_action.value
        self.region = region
        self.location = location

    def __getstate__(self):
        return OrderedDict([(_TRIGGER_TYPE_FIELD_NAME,TriggerType.mouse.value),
                            ("action", self.action), ("region", self.region),
                            ("location", self.location)])

    def __str__(self):
        return "%s [%s] %s" %(self.action, self.region, self.location)


class TextTrigger(object):
    def __init__(self,region,text):
        self.region = region
        self.text = text

    def __getstate__(self):
        return OrderedDict([(_TRIGGER_TYPE_FIELD_NAME, TriggerType.text.value),
                            ("region", self.region), ("text", self.text)])

    def __str__(self):
        return "[%s] %s" % (self.region, self.text)





