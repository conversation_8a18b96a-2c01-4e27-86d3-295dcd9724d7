from __future__ import absolute_import

import typing as tp
import math

from collections import OrderedDict
from .errors import WatcherError


class Point(object):
    """
    图像坐标的表示类
    """

    def __init__(self, x=0, y=0):
        self.x = int(round(x))
        self.y = int(round(y))

    def __getstate__(self):
        return {"x": self.x, "y": self.y}

    def __add__(self, other):
        return Point(self.x + other.x, self.y + other.y)

    def __iadd__(self, other):
        return Point(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Point(self.x - other.y, self.y - other.y)

    def __mul__(self, scalar):
        return Point(self.x * scalar, self.y * scalar)

    def __div__(self, scalar):
        return Point(self.x / scalar, self.y / scalar)

    def __str__(self):
        return "({0}, {1})".format(self.x, self.y)

    def __bool__(self):
        return self.x and self.y

    @classmethod
    def create_top_left(cls):
        return cls(0, 0)

    def length(self):
        return math.sqrt(self.x ** 2 + self.y ** 2)


class Region(object):
    """
    图像的区域表示类
    """

    def __init__(self, left=0, top=0, width=0, height=0):
        self.left = left
        self.top = top
        self.width = width
        self.height = height

    def __getstate__(self):
        return OrderedDict([("top", self.top), ("left", self.left), ("width", self.width),
                            ("height", self.height)])

    def __setstate__(self, state):
        raise WatcherError('Cannot create Region instance from dict!')

    @classmethod
    def create_empty_region(cls):
        return cls(0, 0, 0, 0)

    @classmethod
    def from_location_size(cls, location, size):
        return cls(location.x, location.y, size.width, size.height)

    @property
    def left(self):
        return self.left

    @property
    def top(self):
        return self.top

    @property
    def width(self):
        return self.width

    @property
    def height(self):
        return self.height

    @property
    def right(self):
        return self.left + self.width

    @property
    def bottom(self):
        return self.top + self.height

    @property
    def location(self):
        """Return the top-left corner as a Point."""
        return Point(self.left, self.top)

    @location.setter
    def location(self, p):
        """Sets the top left corner of the region"""
        self.left, self.top = p.x, p.y

    @property
    def bottom_right(self):
        """Return the bottom-right corner as a Point."""
        return Point(self.right, self.bottom)

    @property
    def size(self):
        """
        :return: The size of the region.
        """
        return dict(width=self.width, height=self.height)

    def is_empty(self):
        return self.left == self.top == self.width == self.height == 0


class FloatingRegion(object):
    def __init__(self, region):
        self.region = region

    def get_region(self, watcher_screenshot):
        return self

    def __getstate__(self):
        return dict(top=self.region.top,
                    left=self.region.left,
                    width=self.region.width,
                    height=self.region.height)

    def __setstate__(self, state):
        raise WatcherError('Cannot create FloatingRegion instance from dict!')

    def _str_(self):
        return "{0} {{region: {1}}}".format(self.__class__.__name__, self.region)


class FloatingRegionByUIObject(object):
    def __init__(self, ui_object):
        self._ui_object = ui_object

    def get_region(self, watcher_screenshot):
        # type:(WatcherScreenShot) ->Region
        watcher_screenshot.get_ui_object_region(self._ui_object)


class IgnoreRegionByUIObject(object):
    def __init__(self, ui_object):
        self._ui_object = ui_object

    def get_region(self, watcher_screenshot):
        # type:(WatcherScreenShot) ->Region
        watcher_screenshot.get_ui_object_region(self._ui_object)


class RegionWrapper(object):
    def __init__(self, region):
        # type: (Region) -> None
        self.region = region

    def get_region(self, watcher_screenshot):
        # type: (WatcherScreenShot) -> Any
        return self.region

    def __str__(self):
        return str(self.region)


class Target(object):
    """
    用于表示图像验证时忽略区域以及比较区域
    """

    def __init__(self):
        self._ignore_caret = True
        self._ignore_regions = []
        self._floating_regions = []

    def ignore(self, *regions):
        for region in regions:
            if region is None:
                continue
            if isinstance(region, Region):
                self._ignore_regions.append(RegionWrapper(region))
            else:
                self._ignore_regions.append(region)
        return self

    def floating(self, *regions):
        for region in regions:
            if region is None:
                continue
            if isinstance(region, Region):
                self._floating_regions.append(RegionWrapper(region))
            else:
                self._floating_regions.append(region)
        return self

    @property
    def ignore_regions(self):
        # type: () -> tp.List
        return self._ignore_regions

    @property
    def floating_regions(self):
        # type: () -> tp.List
        return self._floating_regions

    def ignore_caret(self, ignore=True):
        # type: (bool) -> Target
        self._ignore_caret = ignore
        return self

    def get_ignore_caret(self):
        return self._ignore_caret


class MatchLevel(object):
    NONE = "None"
    CONTENT = "Content"
    STRICT = "Strict"
    EXACT = "EXACT"


class ScreenShotType(object):
    ELEMENT_SCREENSHOT = 'ElementScreenshot'
    REGION_SCREENSHOT = 'RegionScreenshot'
    FULLPAGE_SCREENSHOT = "FullPageScreenshot"
    VIEWPORT_SCREENSHOT = "ViewportScreenshot"


class ImageMatchSettings(object):
    def __init__(self, match_level=MatchLevel.STRICT):
        self.match_level = match_level

    def __getstate__(self):
        return dict(matchLevel=self.match_level)


class ExactMatchSettings(object):
    """
    Encapsulates settings for the "Exact" match level.
    """

    def __init__(self, min_diff_intensity=0, min_diff_width=0, min_diff_height=0, match_threshold=0.0):
        # type: (int, int, int, float) -> None
        self.min_diff_intensity = min_diff_intensity
        self.min_diff_width = min_diff_width
        self.min_diff_height = min_diff_height
        self.match_threshold = match_threshold

    def __getstate__(self):
        return dict(minDiffIntensity=self.min_diff_intensity,
                    minDiffWidth=self.min_diff_width,
                    minDiffHeight=self.min_diff_height,
                    matchThreshold=self.match_threshold)

    def __setstate__(self, state):
        raise WatcherError('Cannot create ExactMatchSettings instance from dict!')

    def __str__(self):
        return "[min diff intensity: %d, min diff width: %d, min diff height: %d, match threshold: %f]" % (
            self.min_diff_intensity, self.min_diff_width, self.min_diff_height, self.match_threshold)
