from __future__ import absolute_import

import abc
import hashlib

import os

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.image._airtest import *
from labuiframe.lib.image._agent_connector import AgentConnector
from labuiframe.lib.image.errors import *
from labuiframe.lib.utils.device import DeviceInfo

from .image import *
from ._match_window_task import MatchW<PERSON>owTask
from . import logger
from labuiframe.lib.utils import general_utils


class FailureReports(object):
    """
    Failures are either reported immediately when they are detected, or when the test is closed.
    """
    IMMEDIATE = "Immediate"
    ON_CLOSE = "OnClose"


class WatcherBase(abc.ABC):
    DEFAULT_WATCHER_SERVER = "http://watcher.fat2.qa.nt.ctripcorp.com"

    def __init__(self, server_url=DEFAULT_WATCHER_SERVER):
        self._server_url = server_url
        self._app_name = None
        self._app_version = None
        self._test_name = None
        self._test_suite = None
        self._host_os = None
        self._browser = None
        self._is_open = False
        self._running_session = None
        self._start_info = None
        self.is_disabled = False
        self.failure_reports = FailureReports.ON_CLOSE
        self._properties = []
        self._agent_connector = AgentConnector(server_url)
        self.default_match_settings = ImageMatchSettings()
        self._user_inputs = []  # type: UserInputs
        self._device = None
        self._poco = None
        self._watcherPoco = None
        self._region_to_check = None
        self._viewport_size = None
        self.api_key = "123"
        self.createBy = None
        self.env = ""
        self.uiCase = ""

    def getUiCase(self):
        return self.uiCase

    def setUiCase(self,uiCase):
        self.uiCase = uiCase

    def setEnv(self,env):
        self.env = env

    def getEnv(self):
        return self.env

    @property
    def match_level(self):
        return self.default_match_settings.match_level

    @property
    def server_url(self):
        return self._agent_connector.server_url

    @server_url.setter
    def server_url(self, server_url):
        if server_url is None:
            self._agent_connector.server_url = WatcherBase.DEFAULT_WATCHER_SERVER
        else:
            self._agent_connector.server_url = server_url

    @property
    def api_key(self):
        return self._agent_connector.api_key

    @api_key.setter
    def api_key(self, api_key):
        self._agent_connector.api_key = api_key

    @property
    def get_createBy(self):
        return self.createBy

    def setCreateBy(self, createBy):
        self.createBy = createBy

    @property
    def test_suite(self):
        return self._test_suite

    @test_suite.setter
    def test_suite(self,test_suite):
        self._test_suite = test_suite

    @abc.abstractmethod
    def _get_environment(self):
        pass

    @abc.abstractmethod
    def get_screenshot(self):
        pass

    def is_open(self):
        return self._is_open

    def add_property(self, name, value):
        self._properties.append({'name': name, 'value': value})

    def get_poco(self):
        #  type: ()-> Poco
        return self._poco

    def get_device(self):
        return self._device

    def get_phone_info(self):
        if DeviceInfo.getPlatform().lower() == 'android':
            return "%s %s" % (self._device.shell("getprop ro.product.brand"), self._device.shell("getprop ro.product.model"))
        elif DeviceInfo.getPlatform().lower() == 'ios':
            result = os.popen('ideviceinfo -k DeviceName')
            phone_name = result.read().replace("\n", "")
            result.close()
            result = os.popen('ideviceinfo -k ProductVersion')
            phone_version = result.read().replace("\n", "")
            result.close()
            return "%s %s" % (phone_name, phone_version)

    def get_serial_no(self):
        if DeviceInfo.getPlatform().lower() == 'android':
            serialno = ""
            try:
                serialno = self._device.shell("getprop ro.serialno")
            except Exception as e:
                print("***serialno:" + serialno)
            return serialno
        elif DeviceInfo.getPlatform().lower() == 'ios':
            result = os.popen('idevice_id -l')
            serialno = result.read()
            serialno = serialno.replace("\n","")
            result.close()
            return serialno

    def get_os_info(self):
        return DeviceInfo.getPlatform().lower()

    def get_os_version(self):
        if DeviceInfo.getPlatform().lower() == 'android':
            os_version = self._device.shell("getprop ro.build.version.release")
            os_version = os_version.replace("\n","")
            os_version = os_version.replace("\t","")
            os_version = os_version.replace("\r","")
            os_version = os_version.replace(" ","")
        elif DeviceInfo.getPlatform().lower() == 'ios':
            result = os.popen('ideviceinfo -k ProductVersion')
            os_version = result.read().replace("\n", "")
            result.close()
        return os_version

    def get_app_name(self):
        if DeviceInfo.getPlatform().lower() == 'android':
            app_name = Labconfig.getPackageName()
        elif DeviceInfo.getPlatform().lower() == 'ios':
            app_name = Labconfig.getIosPackageName()
        return app_name

    def get_app_version(self):
        if DeviceInfo.getPlatform().lower() == 'android':
            app_version = self._device.shell("dumpsys package " + Labconfig.getPackageName() + " | grep versionName")
            app_version = app_version.replace("\n", "")
            app_version = app_version.replace("\t", "")
            app_version = app_version.replace("\r", "")
            app_version = app_version.replace("versionName=", "")
            app_version = app_version.replace(" ", "")
        elif DeviceInfo.getPlatform().lower() == 'ios':
            app_version = ""
            result = os.popen('ideviceinstaller -l')
            applist = result.read().split("\n")
            result.close()
            for item in applist:
                if Labconfig.getIosPackageName() in item:
                    app_version = item.replace(Labconfig.getIosPackageName(), "")
                    app_version = app_version.replace("携程旅行", "")
                    app_version = app_version.replace(" ", "")
                    app_version = app_version.replace("-", "")
                    break
            del applist
            print(app_version)
        return app_version

    def get_resolution(self):
        return self._poco.get_screen_size()

    def get_test_run_guid(self):
        info = self._app_name + "_" + self._test_name + "_" + self.get_serial_no()
        m = hashlib.md5(info.encode("utf8"))
        return m.hexdigest()

    def _create_start_info(self):
        if Labconfig().is_watcher_debug() == "True":
            runId = Labconfig().is_runId_debug()
        elif Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            runId = int(Labconfig.get_ctaskid())
        else:
            try:
                # runId=1234
                runId = os.environ['runId']
            except Exception as e:
                print("runId Exception:", e)
                return None
        testSuite = self._test_name if self._test_suite is None else self._test_suite
        self._start_info = {'testRunGuid': self.get_test_run_guid(), 'appIdOrName': self._app_name,
                            'testName': self._test_name, 'testSuite': testSuite,
                            'environment': self._get_environment(), 'serialno': self.get_serial_no(),
                            'matchSettings': self.default_match_settings, 'properties': self._properties,
                            "runBy":self.createBy,"runId":runId, "platform": "airtest", "viewport":general_utils.list_to_str(self.get_resolution())}

    def _start_session(self):
        self._create_start_info()
        print(self._start_info)
        self._running_session = self._agent_connector.start_session(self._start_info)

    def _reset_last_screenshot(self):
        self._user_inputs = []

    def open(self, poco, device, app_name, test_name, viewport_size=None):
        logger.open_()
        self._device = device
        self._poco = poco
        self._watcherPoco = WatcherPoco(self._poco)
        if self.is_disabled:
            logger.debug('open(): ignored (disabled)')
            return self._poco

        if self.api_key is None:
            raise WatcherError("Api Key Is Not Set!")
        if self._is_open:
            raise WatcherError("A Test Is Already Running!")
        self._app_name = app_name
        self._test_name = test_name
        self._viewport_size = viewport_size
        self._is_open = True
        return self._watcherPoco

    def close(self):
        if self.is_disabled:
            logger.info("close():ignored (disabled)")
            return None
        try:
            if not self._is_open:
                raise WatcherError("Watcher is not open")

            self._is_open = False
            self._reset_last_screenshot()

            results = self._agent_connector.stop_session(self._running_session, False)
        finally:
            logger.close()


class Watcher(WatcherBase):

    def __init__(self, server_url=WatcherBase.DEFAULT_WATCHER_SERVER):
        super(Watcher, self).__init__(server_url)
        self._screenshot_type = ScreenShotType.FULLPAGE_SCREENSHOT
        self._match_window_task = None  # type: tp.Optional[MatchWindowTask]

    def _get_environment(self):
        if self._device is None:
            return {}
        phone_version = self.get_phone_info()
        os_info = self.get_os_info()
        os_version = self.get_os_version()
        app_name = self.get_app_name()
        app_version = self.get_app_version()
        env = self.getEnv()
        viewport_size = general_utils.list_to_str(self.get_viewport_size())
        return {'osInfo': os_info,'osVersion':os_version, 'viewportSize':viewport_size,"appName":app_name,"appVersion": app_version, "evn": env}

    def get_viewport_size(self):
        return self._poco.get_screen_size()

    def _get_full_page_screenshot(self):
        return WatcherScreenShot(self._poco)

    def get_screenshot(self):
        if self._screenshot_type == ScreenShotType.FULLPAGE_SCREENSHOT:
            return self._get_full_page_screenshot()

    def _prepare_to_check(self):
        if not self.is_open():
            raise WatcherError("Watcher Is Not Open!")
        if not self._running_session:
            self._start_session()
            self._match_window_task = MatchWindowTask(self,self._agent_connector, self._running_session)

    def _handle_match_result(self, result, tag):
        # type: (MatchResult, tp.Text) -> None
        self._last_screenshot = result['screenshot']
        as_expected = result['as_expected']
        self._user_inputs = []
        if not as_expected:
            if self._running_session:
                if self.failure_reports == FailureReports.IMMEDIATE:
                    raise TestFailedError("Mismatch found in '%s' of '%s'" %
                                          (self._start_info['scenarioIdOrName'],
                                           self._start_info['appIdOrName']))

    def log_test_suite(self, run_id, suite_name, appIDOrName, department, run_from):
        if self.is_disabled:
            logger.info("log_test_suite{}: ignored (disabled)")
            return
        return self._agent_connector.log_suite_run(run_id, suite_name, appIDOrName, department, run_from)

    def stopSuite(self, run_id, suite_name, appIDOrName):
        if self.is_disabled:
            logger.info("stopSuite{}: ignored (disabled)")
            return
        return self._agent_connector.stopSuite(run_id, suite_name, appIDOrName)


    def check_window(self, tag, match_timeout=-1, target=None):
        if self.is_disabled:
            logger.info("check_window(%s): ignored (disabled)" % tag)
            return
        logger.info("check_window('%s')" % tag)
        self._screenshot_type = ScreenShotType.FULLPAGE_SCREENSHOT
        self._prepare_to_check()
        self._match_window_task.match_window(match_timeout, tag, self._start_info, self._user_inputs,
                                                      self.default_match_settings, target, self.uiCase)

    def check_window_sync(self, tag, match_timeout=-1, target=None):
        if self.is_disabled:
            logger.info("check_window(%s): ignored (disabled)" % tag)
            return
        logger.info("check_window('%s')" % tag)
        self._screenshot_type = ScreenShotType.FULLPAGE_SCREENSHOT
        self._prepare_to_check()
        return self._match_window_task.match_window_sync(match_timeout, tag, self._start_info, self._user_inputs,
                                                      self.default_match_settings, target, self.uiCase)

    def check_region(self, region, tag=None, match_timeout=-1, target=None):
        if self.is_disabled:
            logger.info('check_region(): ignored (disabled)')
            return
        logger.info('check_region([%s], "%s"' % (region, tag))
        if region.is_empty():
            raise WatcherError('region cannot be empty!')
        self._screenshot_type = ScreenShotType.REGION_SCREENSHOT
        self._region_to_check = region
        self._prepare_to_check()
        result = self._match_window_task.match_window(match_timeout, tag, self._start_info, self._user_inputs,
                                                      self.default_match_settings, target, self.uiCase)
        if result == None:
            return
        self._handle_match_result(result, tag)

    def check_region_by_ui_object(self, ui_object, tag=None, match_timeout=-1, target=None):
        # type: (AnyElement,tp.Text,int,Target) -> None
        if self.is_disabled:
            logger.info('check_region_by_element(): ignored (disabled)')
            return
        logger.info("check_region_by_element('%s')" % tag)
        self._screenshot_type = ScreenShotType.ELEMENT_SCREENSHOT
        self._prepare_to_check()
        self._region_to_check = ui_object
        result = self._match_window_task.match_window(match_timeout, tag, self._start_info, self._user_inputs,
                                                      self.default_match_settings, target, self.uiCase)
        if result == None:
            return
        self._handle_match_result(result, tag)
