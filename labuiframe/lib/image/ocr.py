# -*- coding: utf-8 -*-
import base64
from labuiframe.lib.image._agent_connector import AgentConnector
import re
from labuiframe.lib.image.errors import TestFailedError

class OcrUtil(object):
    DEFAULT_OCR_SERVER = "http://hotelocr.fat43.qa.nt.ctripcorp.com/api/tr-run/"

    @classmethod
    def getBase64(cls, img_path):
        try:
            with open(img_path, "rb") as f:
                base64_data = base64.b64encode(f.read())
                return str(base64_data,'utf-8')
        except:
            return None

    @classmethod
    def get_ocr_result(cls, img_path):
        base64_data = cls.getBase64(img_path)
        if base64_data is None:
            raise TestFailedError("Read image data error!!")
        # print("image data: " + base64_data)
        agent_connnect = AgentConnector(cls.DEFAULT_OCR_SERVER)
        image = base64_data
        ocr_result = agent_connnect.get_ocr_result(image)
        if ocr_result is None:
            raise TestFailedError("调用OCR服务异常!!")
        print("ocr results: " + str(ocr_result["raw_out"]))
        return ocr_result["raw_out"]

    @classmethod
    def get_locations_by_text(cls, img_path, text, regex = False):
        results = []
        if img_path is None:
            return None
        ocr_result = cls.get_ocr_result(img_path)
        for item in ocr_result:
            item_result = {}
            if not regex:
                if item[1] == text and item[2] > 0.95:
                    item_result['rec'] = item[1]
                    item_result['loc'] = (int(item[0][0]), int(item[0][1]))
                    results.append(item_result)
            else:
                if not text.startswith('^') and not text.startswith('.*'):
                    text = '.*' + text
                if not text.endswith('$') and not text.endswith('.*'):
                    text = text + '.*'
                pattern = re.compile(text)
                match = pattern.search(item[1])
                if match is not None and item[2] > 0.85:
                    item_result['rec'] = item[1]
                    item_result['loc'] = [int(item[0][0]), int(item[0][1])]
                    results.append(item_result)
        print("match results: " + str(results))
        return results


if __name__ == "__main__":
    result = OcrUtil.get_ocr_result(r"E:\Hotel\APPLab\ocr\ocr\pay\1.jpg")
    print(result)
