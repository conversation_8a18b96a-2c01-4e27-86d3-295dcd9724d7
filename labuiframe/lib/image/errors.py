
class WatcherError(Exception):
    """
    Base Exception.
    """

class OutOfBoundsError(WatcherError):
    """

    """

class TestFailedError(Exception):
    """
    Indicates that a test did not pass (i.e., test either failed or is a new test).
    """
    def __init__(self, message, test_results=None, ):
        self.message = message
        self.test_results = test_results

    def __str__(self):
        return "%s , %s" % (self.message, self.test_results)