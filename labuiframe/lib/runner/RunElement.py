import threading

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.case.element_type import ViewId
from labuiframe.lib.utils.HandleInfo import HandleInfo
from labuiframe.lib.utils.MySqlConnect import MySqlConnect
from labuiframe.lib.utils.capture import Capture


class RunElement:

    # 指定元素，把元素控件信息存储到列表中，控件的位置截图，控件本身截图
    @classmethod
    def insertElementPosition(cls, elementNode, elementId):
        try:

            if not isinstance(elementId, ViewId):
                return
            pocoId = elementId.view_id

            elementInfo = MySqlConnect.queryElementById(pocoId)  # 查询数据库是否存在该元素
            if elementInfo == None or elementInfo.get('id', 0) == 0:
                return
            elementPoco = ''  # 控件本身截图
            elementPos = ''  # 控件位置所在标记截图
            if elementInfo.get('clickImgID', 0) == 0:
                mockKey = HandleInfo.getMockKey()
                appVersion = Labconfig.getAppversion()
                if elementPoco == '' or elementPos == '':
                    elementPoco, elementPos = cls.getElementPositionImg(cls, elementNode)
                editStatus = 1 if str(elementNode.attr('enabled')) == 'True' else 0
                primaryId = MySqlConnect.insertNewElementInfo(click_poco_name=pocoId, poco_text=str(elementNode.get_text()), editable=editStatus, is_union=0, click_poco_img=elementPoco,
                                                              click_positon_img=elementPos, resolution=cls.getResolution(cls), click_poco_position=Capture.getSidePosByNode(elementNode), mockkey=mockKey, appversion=appVersion)  # 插入数据库
                if primaryId != 0:
                    MySqlConnect.updateElementClickImg(primaryId, elementInfo.get('id'))

            if elementInfo.get("elementPocoImgURL", '') == '' or elementInfo.get("elementPocoImgURL", '') == None:
                if elementPoco == '' or elementPos == '':
                    elementPoco, elementPos = cls.getElementPositionImg(cls, elementNode)
                MySqlConnect.updateElementImage(elementInfo, elementPoco, elementPos)


        except Exception as e:
            print('insertElementPosition:\t\t', str(e))

    # 获取元素控件相关截图
    def getElementPositionImg(self, elementNode):
        elementPoco = Capture.screenCropImageUrlByNode(elementNode)  # 控件本身截图
        elementPos = Capture.markScreenshotUrlByNode(elementNode)  # 控件位置所在标记截图
        return elementPoco, elementPos

    # 获取屏幕分辨率
    def getResolution(self):
        width, height = Capture.getScreenSize()
        return "%s*%s" % (width, height)
