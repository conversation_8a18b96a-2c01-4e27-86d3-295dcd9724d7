import functools
import time

from hrpc.exceptions import TransportDisconnected
from poco.drivers.android import uiautomation


class AndroidUiautomationPoco(uiautomation.AndroidUiautomationPoco):
    """
    Android设备连接方法
    Args:
        uuid: 连接参数
        is_simulator: 是否模拟器
    """
    def __init__(self, *args, **kwargs):
        self._init_args = args
        self._init_kwargs = kwargs
        super(AndroidUiautomationPoco, self).__init__(*args, **kwargs)
        def retry(func):
            @functools.wraps(func)
            def wrapper(*a, **k):
                for _ in range(5):
                    try:
                        return func(*a, **k)
                    except TransportDisconnected as e:
                        error_info = str(e)
                        if 'Connection aborted.' in error_info:
                            time.sleep(1)
                            print(f"触发重试，触发信息{error_info}")
                            continue
                        raise
            return wrapper

        self.agent.client.transport.send = retry(self.agent.client.transport.send)