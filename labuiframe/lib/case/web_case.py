# -*- coding: utf-8 -*-
from functools import partial
import re

import jsonpath
from WirelessPlatformWebUI import Device
from WirelessPlatformWebUI.Utils import web, ContextArgs
from urllib.parse import urlparse, parse_qs
from labuiframe.lib.config.reportConfig import ReportConfig
from labuiframe.lib.utils.HandleInfo import HandleInfo

from pocounit.result import PocoTestResult

from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.commonAction import CommonAction
from labuiframe.lib.utils.device import DeviceInfo

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.capture import Capture
from labuiframe.lib.utils.LabuiframeTestCase import LabuiframeTestCase
from labuiframe.lib.utils.getBuListCacheUtil import GetBuListCacheUtil
from labuiframe.lib.utils.printUtil import printUtil

import sys
import os
import traceback
import threading

from airtest.core.api import *
from playwright.sync_api import Page

import socket
import select


class WebTestCase(LabuiframeTestCase):
    caseResult = PocoTestResult()
    mySqlConnect = MySqlConnect()
    casename = ""
    start_time = 0
    end_time = 0
    _instance = None  # 存储实例的类变量
    _forward_server = None  # 存储转发服务器实例
    _clients = []  # 存储客户端连接
    _running = True  # 控制转发服务运行状态

    def __init__(self):
        LabuiframeTestCase.__init__(self)
        self.caseResult = LabuiframeTestCase()
        Labconfig.config(self.project_root)  # load labconfig.ini
        Labconfig.set_config_data(Labconfig.get_config_file_path())
        Labconfig.isStopApp = False
        Labconfig._is_mpass = True
        self.cmt_capture = Capture()  # 初始化截图类
        self.cmt_capture.setCase(self)
        WebTestCase._instance = self  # 保存实例引用
        self.dom_tree = None

    @classmethod
    def handle_client(cls, client_socket, addr):
        """处理单个客户端连接"""
        target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            target_socket.connect(('127.0.0.1', 9223))  # 连接到本地的 Chrome 调试端口
            printUtil.printCaseDevice(f"客户端 {addr} 已连接到目标端口 9223")
            
            while cls._running:
                readable, _, _ = select.select([client_socket, target_socket], [], [], 1)
                
                for sock in readable:
                    try:
                        data = sock.recv(4096)
                        if not data:
                            raise ConnectionError("连接已关闭")
                        
                        if sock == client_socket:
                            target_socket.send(data)
                        else:
                            client_socket.send(data)
                    except Exception as e:
                        printUtil.printCaseDevice(f"传输数据时发生错误: {e}")
                        return
                        
        except Exception as e:
            printUtil.printCaseDevice(f"处理客户端 {addr} 时发生错误: {e}")
        finally:
            printUtil.printCaseDevice(f"客户端 {addr} 断开连接")
            if client_socket in cls._clients:
                cls._clients.remove(client_socket)
            client_socket.close()
            target_socket.close()

    @classmethod
    def start_forward_server(cls):
        """启动转发服务器"""
        try:
            cls._forward_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            cls._forward_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            cls._forward_server.bind(('0.0.0.0', 9222))  # 监听所有接口的 9222 端口
            cls._forward_server.listen(5)
            cls._forward_server.settimeout(1)
            printUtil.printCaseDevice("转发服务已启动: 0.0.0.0:9222 -> 127.0.0.1:9223")
            
            while cls._running:
                try:
                    client_socket, addr = cls._forward_server.accept()
                    printUtil.printCaseDevice(f"新的客户端连接: {addr}")
                    cls._clients.append(client_socket)
                    client_thread = threading.Thread(
                        target=cls.handle_client,
                        args=(client_socket, addr)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except socket.timeout:
                    continue
                except Exception as e:
                    if cls._running:
                        printUtil.printCaseDevice(f"接受客户端连接时发生错误: {e}")
                    break
                    
        except Exception as e:
            printUtil.printCaseDevice(f"启动转发服务器时发生错误: {e}")
        finally:
            if cls._forward_server:
                cls._forward_server.close()

    @classmethod
    def setUpClass(cls):
        """
        改写此方法来自定义appCase初始化

        :return:
        """
        try:
            # playwright官方问题，https://github.com/microsoft/playwright/issues/16669，先添加5s等待在初始化浏览器
            time.sleep(3)
            cls.start_time = int(round(time.time() * 1000))
            cls.startTime = time.time() # 调用公共报告库使用的日期格式
            super(WebTestCase, cls).setUpClass()
            # cls.delReportDirs(cls)
            DeviceInfo.set_deviceName("web")
            metaInfo = cls.getMetaInfo()
            # 下面的代码是自助下单使用，获取tags
            try:
                tags = metaInfo['tags']
                # 初始化第一个tag
                if Labconfig.getIsHotelWirelessAppGroup() == "True":
                    CommonAction.getPrimaryCaseId()
                    taglist = tags.split("|")
                    HandleInfo.setCirculateInfos(HandleInfo.currentCirculate, len(taglist), taglist, cls.casename)
                    metaInfo = cls.getMetaInfo()
            except Exception as e:
                cls.printCaseDevice(str(e))
                cls.printCaseDevice("************case没有关联的tag信息***********")
            HandleInfo.setMetaInfo(metaInfo)
            printUtil.printCaseDevice("********初始化web浏览器**************")
            
            CmtLogger.createCaseInfo(metaInfo, class_name=cls.__module__)
            config = metaInfo.get('config')
            # 日志用于自动化分析case，请勿变更
            if 'tracer' in cls._result_emitters:
                cls._result_emitters.pop('tracer')
            printUtil.printCaseDevice("metaInfo config:{}".format(config))
            cls.mockKey = "0"
            custom_headers = {}
            if config.get("mock_key") and config.get("mock_key") != "0":
                custom_headers.update({"X-Ctrip-Mock-CaseId": config.get("mock_key")})
                cls.mockKey = config.get("mock_key")
                
            if config.get("ABList") and len(config.get("ABList")) > 0:
                ab_test_internal = ""
                ABList = config.get("ABList")
                for i, ab in enumerate(ABList):
                    ab_test_internal += f"{ab[0]}:{ab[1]}"
                    if i < len(ABList) - 1:
                        ab_test_internal += ","
                custom_headers.update({"x-ctx-abtest": ab_test_internal})
                
            cookies = config.get("cookies")
            page_url = config.get("page_url")            
            
            # 添加权限设置
            permissions = [
                'accelerometer',
                'ambient-light-sensor',
                'background-sync',
                'camera',
                'clipboard-read',
                'clipboard-write',
                'geolocation',
                'gyroscope',
                'magnetometer',
                'microphone',
                'midi-sysex',
                'midi',
                'notifications',
                'payment-handler',
                'storage-access'
            ]
            # h5页面的case默认使用移动设备展示，根据platform以及ini配置的H5列表来判断
            is_h5 = GetBuListCacheUtil.getIsH5(Labconfig.platformId, Labconfig.buId)
            if Labconfig.platformId and is_h5:
                printUtil.printCaseDevice("web_is_mobile is true")
                Labconfig.set_web_is_mobile(is_h5)
                context_args = {
                        "ignore_https_errors": Labconfig.get_ignore_https_errors(),    # 用于指定是否忽略https错误
                        "bypass_csp": Labconfig.get_bypass_csp(),   # 用于指定是否绕过CSP
                        "is_mobile": True,  # 用于指定设备是否为移动设备
                        "no_viewport": False,  # 用于指定是否禁用视口
                        "viewport": {"width": 375, "height": 812},
                        "permissions": permissions, # 添加权限设置
                        "device_scale_factor": 3, # 设置设备像素比
                        "has_touch": True, # 启用触摸事件支持
                        "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1@auto-test" # 设置移动设备的User Agent
                    }
            else:
                context_args = {
                        "ignore_https_errors": Labconfig.get_ignore_https_errors(),    # 用于指定是否忽略https错误
                        "bypass_csp": Labconfig.get_bypass_csp(),   # 用于指定是否绕过CSP
                        "is_mobile": False,  # 用于指定设备是否为移动设备
                        "no_viewport": False,  # 用于指定是否禁用视口
                        "viewport": {"width": 1920, "height": 1080},  # 用于指定视口的宽度和高度
                        "permissions": permissions, # 添加权限设置
                        "user_agent": "@auto-test"
                    }
            printUtil.printCaseDevice("context_args:{}".format(context_args))
            printUtil.printCaseDevice("custom_headers:{}".format(custom_headers))
            printUtil.printCaseDevice("cookies:{}".format(cookies))
            # 清除cls._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + cls.__name__目录
            try:
                shutil.rmtree(cls._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + cls.__name__)
                printUtil.printCaseDevice("清除tracing_path目录成功")
            except Exception as e:
                pass

            # route代理前置捕获进入页面前的请求
            cls.route_list = []
            cls.route = []
            # 判断metaInfo.get('route')是否为"True",如果为True则将所有请求都存储到route_list，只存储接口不含.的请求
            if metaInfo.get('route', "False") == "True":
                cls.route = [{"url":re.compile(r"^(?!.*\.(js|css|json)$).*$"),"handler":cls.route_list_handler}]
            
            # 启动转发服务器
            forward_thread = threading.Thread(target=cls.start_forward_server)
            forward_thread.daemon = True
            forward_thread.start()
            
            web_ = web(
                browser_args=Device.BrowserArgs(
                    kwargs={
                        "headless": Labconfig.get_headless(),
                        "slow_mo": Labconfig.get_slow_mo(),    # 用于指定每个步骤之间的延迟时间
                    },
                    args=["--remote-debugging-port=9223", "--remote-debugging-address=127.0.0.1", "--disable-web-security", "--allow-file-access-from-files"] #用于指定browser_use连接端口
                ), 
                context_args=ContextArgs(
                    url=page_url,
                    context_args=context_args,
                    headers=custom_headers,
                    default_timeout=Labconfig.get_web_default_timeout(),
                    tracing_path=cls._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + cls.__name__, 
                    cookies=cookies,
                    routes=cls.route
            ))
            cls.page_list = []
            cls.browser = web_.browser
            cls.tracing_stop = partial(web_.tracing_stop)
            cls.context = web_.context
            cls.context.grant_permissions(permissions)  # 授予所有权限
            cls.context.on("page", lambda page_: cls.page_list.append(page_))
            # 监听新页面打开事件
            cls.context.on("page", lambda page: cls.inject_monitor_script(page, True))
            # 对当前页面注入监听脚本
            cls.inject_monitor_script(web_.page)
            # 监听新页面打开事件
            cls.context.on("page", lambda page: cls.inject_monitor_script_for_iframe(page, True))
            # 对当前页面注入监听脚本
            cls.inject_monitor_script_for_iframe(web_.page)
            cls.page: Page = web_.page
            # cls.poco仅用于兼容app和H5共用的case
            cls.poco = cls.page
            # 设置playwright方法默认超时时间30s
            cls.page.set_default_timeout(30 * 1000)
            cls.console = []
            # 监听console事件
            cls.page.on("console", cls.console_handler)
            # 增加MockId获取日志
            cls.printCaseDevice("mockId信息**" + config.get('mock_key') + "**")
            # 增加openUrl获取日志
            cls.printCaseDevice("openUrl信息**" + page_url + "**")
            # 增加labelid获取日志
            cls.printCaseDevice("labelId信息**" + metaInfo.get('label') + "**")
            printUtil.printCaseDevice("********初始化web浏览器成功**************")
        except Exception as e:
            printUtil.printCaseDevice("********初始化浏览器失败**************")
            stack_trace = traceback.format_exc()
            printUtil.printCaseDevice("初始化异常堆栈信息：{}".format(stack_trace))
        
        HandleInfo.clearData()
        # auto_setup(logdir=True, project_root=cls._resule_collector.project_root, basedir=cls._resule_collector.root)

    @classmethod
    def route_list_handler(cls, route, request):
        """
        请求的路径最后不含.的请求，则将请求和响应存储到route_list
        """
        # 先继续处理路由请求，确保请求不被阻塞
        route.continue_()
        
        # 如果route_list已初始化且存在
        if cls.route_list is not None:
            try:
                from urllib.parse import urlparse
                path = urlparse(request.url).path
                
                # 检查路径是否有效且最后一部分不包含点号
                if path and '.' not in path.split('/')[-1] and "/bee/collect" not in request.url:
                    cls.route_list.append({
                        'url': request.url,
                        'method': request.method,
                        'path': path,
                        'response': request.response(),
                        'request': request
                    })
            except Exception as e:
                print(f"路由处理异常: {str(e)}")

    @classmethod
    def console_handler(cls, console):
        message = console.text
        # 检查日志消息是否与 toast 相关
        if 'toast' in message.lower() or 'ant-message-notice' in message.lower():
            cls.console.append(console)
    
    @classmethod
    def inject_monitor_script(cls, page, init=False):
        # 注入监听代码到页面，用于检查toast
        script = """
        // 创建全局变量存储观察者
        window._pageObserver = null;
        
        function initObserver() {
            // 如果已经存在观察者，先断开连接
            if (window._pageObserver) {
                window._pageObserver.disconnect();
            }
            
            // 创建新的观察者
            window._pageObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            checkElement(node);
                            const allElements = node.querySelectorAll('*');
                            allElements.forEach(element => {
                                checkElement(element);
                            });
                        }
                    });
                });
            });
            
            // 检查元素函数
            function checkElement(element) {
                let found = false;
                let info = {
                    element: element,
                    textContent: element.textContent.trim(),
                    details: {}
                };
            
                // 检查class
                const classNames = Array.from(element.classList);
                if (classNames.some(className => 
                    className.toLowerCase().includes('toast') || 
                    className.toLowerCase().includes('ant-message-notice')
                )) {
                    found = true;
                    info.details.className = element.className;
                    console.log('发现 class 包含 toast 或 ant-message-notice 的元素:', {
                        element: element,
                        className: element.className,
                        textContent: info.textContent,
                        html: element.innerHTML,
                        outerHTML: element.outerHTML,
                        url: window.location.href  // 添加当前页面URL
                    });
                }
                
                // 检查id
                if (element.id && element.id.toLowerCase().includes('toast')) {
                    found = true;
                    info.details.id = element.id;
                    console.log('发现 id 包含 toast 的元素:', {
                        element: element,
                        id: element.id,
                        textContent: info.textContent,
                        html: element.innerHTML,
                        outerHTML: element.outerHTML,
                        url: window.location.href
                    });
                }
            
                //if (found) {
                //    console.log('元素的 DOM 路径:', getDomPath(element));
                //    console.log('页面URL:', window.location.href);
                //    console.log('------------------------');
                //}
            }
            
            // 获取DOM路径函数
            function getDomPath(element) {
                let path = [];
                while (element && element.nodeType === Node.ELEMENT_NODE) {
                    let selector = element.nodeName.toLowerCase();
                    if (element.id) {
                        selector += '#' + element.id;
                    } else {
                        let sibling = element;
                        let siblingIndex = 1;
                        while (sibling = sibling.previousElementSibling) {
                            if (sibling.nodeName.toLowerCase() === selector) siblingIndex++;
                        }
                        if (siblingIndex !== 1) selector += ':nth-of-type(' + siblingIndex + ')';
                    }
                    path.unshift(selector);
                    element = element.parentNode;
                }
                return path.join(' > ');
            }
            
            // 开始观察
            window._pageObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // 检查现有元素
            console.log('检查现有元素...');
            checkElement(document.body);
            document.querySelectorAll('*').forEach(element => {
                checkElement(element);
            });
            
            console.log('开始监听 DOM 变化...');
        }
        
        // 页面加载完成后初始化观察者
        if (document.readyState === 'complete') {
            initObserver();
        } else {
            window.addEventListener('load', initObserver);
        }
        """
        if init:
            page.add_init_script(script)
        else:
            page.evaluate_handle(script)

    @classmethod
    def inject_monitor_script_for_iframe(cls, page, init=False):
        # 注入监听代码到页面，用于检查 iframe
        script = """
                // 创建全局变量存储观察者和 iframe 信息
                window._iframeObserver = null;
                window._iframeInfo = [];

                function initObserver() {
                    // 如果已经存在观察者，先断开连接
                    if (window._iframeObserver) {
                        window._iframeObserver.disconnect();
                    }

                    // 创建新的观察者
                    window._iframeObserver = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            mutation.addedNodes.forEach((node) => {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    checkNodeAndChildren(node);
                                }
                            });
                        });
                    });

                    // 开始观察
                    window._iframeObserver.observe(document.body, {
                        childList: true,
                        subtree: true
                    });

                    // 检查现有的 <iframe> 元素
                    console.log('检查现有的 <iframe> 元素...');
                    document.querySelectorAll('iframe').forEach(iframe => {
                        const xpath = getXPath(iframe);
                        const info = {
                            element: iframe,
                            src: iframe.src,
                            outerHTML: iframe.outerHTML,
                            xpath: xpath,
                            url: window.location.href
                        };
                        window._iframeInfo.push(info);
                        console.log('现有 <iframe> 元素:', info);
                    });

                    console.log('开始监听 DOM 变化...');
                }

                // 递归检查节点和子节点
                function checkNodeAndChildren(node) {
                    if (node.tagName === 'IFRAME') {
                        const xpath = getXPath(node);
                        const info = {
                            element: node,
                            src: node.src,
                            outerHTML: node.outerHTML,
                            xpath: xpath,
                            url: window.location.href
                        };
                        window._iframeInfo.push(info);
                        console.log('发现新的 <iframe> 元素:', info);
                    }
                    node.childNodes.forEach(child => {
                        if (child.nodeType === Node.ELEMENT_NODE) {
                            checkNodeAndChildren(child);
                        }
                    });
                }

                // 计算元素的 XPath
                function getXPath(element) {
                    if (element.id) {
                        return 'id("' + element.id + '")';
                    }
                    const parts = [];
                    while (element && element.nodeType === Node.ELEMENT_NODE) {
                        let index = 0;
                        let sibling = element;
                        while (sibling) {
                            if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === element.nodeName) {
                                index++;
                            }
                            sibling = sibling.previousSibling;
                        }
                        const tagName = element.nodeName.toLowerCase();
                        const part = tagName + '[' + index + ']';
                        parts.unshift(part);
                        element = element.parentNode;
                    }
                    return '/' + parts.join('/');
                }

                // 页面加载完成后初始化观察者
                if (document.readyState === 'complete') {
                    initObserver();
                } else {
                    window.addEventListener('load', initObserver);
                }
                """
        if init:
            page.add_init_script(script)
        else:
            page.evaluate_handle(script)

    @classmethod
    def close(cls):
        """
        关闭webCase浏览器, 销毁实例化对象
        """
        # 停止转发服务
        cls._running = False
        
        # 停止route
        try:
            cls.context.unroute("**")
        except:
            pass
        
        # 关闭所有客户端连接
        for client in cls._clients:
            try:
                client.close()
            except:
                pass
        cls._clients.clear()
        
        # 关闭转发服务器
        if cls._forward_server:
            try:
                cls._forward_server.close()
            except:
                pass
        
        try:
            if cls._instance and hasattr(cls._instance, 'page'):
                if cls._instance.page is cls.page:
                    printUtil.printCaseDevice("cls._instance.page地址和cls.page一致")
                else:
                    printUtil.printCaseDevice("cls._instance.page地址和cls.page不一致")
                    del cls._instance.page
                    printUtil.printCaseDevice("cls._instance.page已删除")
            else:
                printUtil.printCaseDevice("cls._instance不存在或者cls._instance没有page属性")
        except Exception as e:
            printUtil.printCaseDevice("********销毁实例化对象page失败**************")
            printUtil.printCaseDevice("销毁实例化对象page异常信息：{}".format(str(e)))
            stack_trace = traceback.format_exc()
            printUtil.printCaseDevice("销毁实例化对象page异常堆栈信息：{}".format(stack_trace))
        
        try:
            cls.tracing_stop(coverage_report_id=str(Labconfig.mpaas_main_taskid))
            cls.context.close()
            cls.browser.close()
            cls.browser.playwright.stop()
            printUtil.printCaseDevice("********关闭web浏览器成功**************")
        except Exception as e:
            printUtil.printCaseDevice("********关闭web浏览器失败**************")
            printUtil.printCaseDevice("关闭浏览器异常信息：{}".format(str(e)))
            stack_trace = traceback.format_exc()
            printUtil.printCaseDevice("关闭异常堆栈信息：{}".format(stack_trace))
                    
    @classmethod
    def printCaseDevice(cls, *printMessage):
        """
        打印case信息
        """
        printUtil.printCaseDevice(" ".join(printMessage))

    def delReportDirs(self):
        """
        删除报告文件夹
        """
        printUtil.printCaseDevice("********del reportDirs**************")
        log_path = self._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + self.__name__
        try:
            if os.path.exists(log_path):
                shutil.rmtree(log_path)
        except Exception as e:
            printUtil.printCaseDevice("Unable to delete report dirs. %s" % e)

    @classmethod
    def isDebug(cls):
        return True if sys.gettrace() else False

    @classmethod
    def tearDownClass(cls, poco=None):
        """
        改写此方法来自定义webCase结束
        """
        cls.close()
        upload_log_dir = cls._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + cls.__name__
        metaInfo = cls.getMetaInfo()
        # 获取upload_log_dir目录下所有后缀为 .webm 的文件
        try:
            records = [file for file in os.listdir(upload_log_dir) if file.endswith(".webm")]
        except Exception as e:
            printUtil.printCaseDevice("获取视频文件失败：{}".format(str(e)))
            records = []
        if cls.isDebug():
            test_result = Labconfig.aiGenerateResult
        else:
            test_result = False if len(cls.caseResult.detail_errors) > 0 else True
        case_info = {
            "upload_log_dir": upload_log_dir, # 日志目录
            "case_abs_path": os.path.abspath(sys.modules[cls.__module__].__file__), # 脚本文件绝对路径
            "path": cls.__name__ + ".py", # 脚本文件绝对路径
            "title": cls.__name__, # 标题
            "author": metaInfo.get("author", ""), # 作者
            "desc": "--mockKey=" + cls.mockKey, # 描述信息
            "run_start": cls.startTime, # 开始时间戳
            "run_end": time.time(), # 结束时间戳
            "records": records, # 生成的视频
            "playwright": "trace.zip", # 生成的trace.zip
            "test_result": test_result
        }
        printUtil.printCaseDevice("case_info: {}".format(case_info))
        # 调用公共方法生成report.html
        path = ReportConfig().commonReportJsonForWeb(upload_log_dir + os.sep + "report.json", case_info)
        if os.path.exists(path):
            t = threading.Thread(target=CmtLogger.uploadWebReport, args=(upload_log_dir, CmtLogger.case_result_id))
            t.setDaemon(True)
            t.start()
            Labconfig.threading_list.append(t)
            return
        printUtil.printCaseDevice(f"report.html不存在: {path}")

    def run(self, result=None):
        result = result or self.defaultTestResult()
        if isinstance(result, PocoTestResult):
            self.caseResult = super(WebTestCase, self).run(result)
            # send back case result
            # CmtLogger.logStep("*******Result:"+str(self.caseResult))
            # printUtil.printCaseDevice("*******ResultDetail:"+str(self.caseResult.detail_errors))
            # 增加case错误信息上报
            message = ""
            failType = ""
            if len(self.caseResult.detail_errors) > 0:
                try:
                    if "eventlet.timeout.Timeout" in str(self.caseResult.detail_errors):
                        message = "用例耗时超出{}秒".format(Labconfig.final_timeout)
                        failType = CmtLogger.case_overtime_error
                        try:
                            raise RuntimeError(self.caseResult.detail_errors)
                        except RuntimeError as e:
                            log(e, desc="Run Fail", snapshot=True)
                    else:
                        detail_error = self.caseResult.detail_errors[0][2]
                        # 页面未进入场景错误信息记录
                        if "crash" in str(detail_error):
                            message = CmtLogger.app_crash_message_error
                            failType = CmtLogger.ui_frame_fail_type_error
                        elif "红屏,无需重试" in str(detail_error):
                            message = CmtLogger.page_message_error
                            failType = CmtLogger.code_bug_fail_type_error
                        elif "黑屏,无需重试" in str(detail_error):
                            message = CmtLogger.page_message_error2
                            failType = CmtLogger.code_bug_fail_type_error
                        elif CmtLogger.net_message_error in str(detail_error):
                            message = CmtLogger.net_message_error
                            failType = CmtLogger.net_fail_type_error
                        elif CmtLogger.mock_message_error in str(detail_error):
                            message = CmtLogger.mock_message_error
                            failType = CmtLogger.mock_type_business_change_error
                        # 页面超时未进入增加一种错误类型
                        elif CmtLogger.page_jump_message_error in str(detail_error):
                            message = CmtLogger.page_jump_message_error
                            failType = CmtLogger.ui_case_fail_type_error
                        elif "是否进入" in str(detail_error) or '页面进入' in str(detail_error):
                            printUtil.printCaseDevice('AirTest_FailReason--页面未进入')
                            message = CmtLogger.page_check_message_error
                            failType = CmtLogger.ui_case_fail_type_error
                        # 断言失败场景错误信息记录
                        elif ("False is not true" in str(detail_error)) or ("True is not false" in str(detail_error)):
                            message = str(detail_error)[str(detail_error).find(':') + 1:] + "-验证失败"
                            failType = CmtLogger.ui_case_fail_type_error
                        # 元素未找到场景错误信息记录
                        elif "Cannot find any visible node" in str(detail_error):
                            message = str(detail_error)[str(detail_error).find('"'):] + "-元素未找到"
                            failType = CmtLogger.UIcase_error_type_error
                        # 数组越界
                        elif "list index out of range" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # name 'xx' is not defined
                        elif "is not defined" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # MAXTOUCH does not support to_json
                        elif "MAXTOUCH does not support to_json method" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # 'NoneType' object has no attribute 'exists'
                        elif ("object has no attribute" in str(detail_error)) or ("has no attribute" in str(detail_error)):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # missing 1 required positional argument:
                        elif ("missing" in str(detail_error)) & ("required positional argument" in str(detail_error)):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # takes 1 positional argument but 2 were given
                        elif (("takes" in str(detail_error)) & ("positional argument but" in str(detail_error)) & (
                                "were given" in str(detail_error))):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # File not exist
                        elif "File not exist" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        else:
                            printUtil.printCaseDevice('AirTest_FailReason--case执行中失败')
                            message = str(detail_error)
                            failType = CmtLogger.ui_case_fail_type_error
                        # log(detail_error,desc="Run Fail", snapshot=True)
                        try:
                            raise RuntimeError(detail_error)
                        except RuntimeError as e:
                            log(e, desc="Run Fail", snapshot=True)
                except:
                    printUtil.printCaseDevice("未处理的错误信息：{}".format(str(self.caseResult.detail_errors)))
            self.end_time = int(round(time.time() * 1000))

            excute_time = int((self.end_time - self.start_time) / 1000)
            Labconfig.setTimeSplitToCase(
                excute_time - Labconfig.getTimeSplitToFramework() - Labconfig.getTimeSplitToHotelPage() - Labconfig.getTimeSplitToLoadPage())

            log_url = ""
            try:
                CmtLogger.writeBackCaseResult(self.caseResult, self.start_time, self.end_time, message, log_url, failType,
                                              HandleInfo.getCurrentClientid(), HandleInfo.getCurrentMockKey())
            except Exception as e:
                printUtil.printCaseDevice(e)
            return self.caseResult
        else:
            return result

    def assertTrue(self, expr, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        if isinstance(expr, (str)):
            message = msg + "  期望结果：[True]" + " 实际结果：[" + str(expr) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, "True", False, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            printUtil.printCaseDevice('断言:' + str(message))
            super(WebTestCase, self).assertTrue(False, message)
        else:
            expr = bool(expr)
            message = msg + "  期望结果：[True]" + " 实际结果：[" + str(expr) + "]"
            if not expr:
                # log_result_detail(False,"True",expr,img,url,msg)
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", expr, message)
                log(message, desc=msg, snapshot=True)
                if Labconfig.getIsLogJavis() == 'True':
                    self.mySqlConnect.setCoreCase(0, caseName)
            else:
                # log_result_detail(True, "True", expr, img, url, msg)
                CmtLogger.writeToDatabase(LogCategory.assert_pass, "True", expr, message)
                log(message, snapshot=True)
                if Labconfig.getIsLogJavis() == 'True':
                    self.mySqlConnect.setCoreCase(1, caseName)
            # os.system('echo 断言:' + str(message))
            printUtil.printCaseDevice('断言:' + str(message))
            super(WebTestCase, self).assertTrue(expr, msg)

    def assertFalse(self, expr, msg='', caseName=None):
        expr = bool(expr)
        if caseName == None:
            caseName = self.casename
        message = msg + "  期望结果：[False]" + " 实际结果：[" + str(expr) + "]"
        if expr:
            CmtLogger.writeToDatabase(LogCategory.assert_error, "False", expr, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
        else:
            CmtLogger.writeToDatabase(LogCategory.assert_pass, "False", expr, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        printUtil.printCaseDevice('断言:' + str(message))
        super(WebTestCase, self).assertFalse(expr, msg)

    def getJsPath(self, req, jPath):
        try:
            req = json.loads(req)
            printUtil.printCaseDevice("jsonpath", str(jsonpath.jsonpath(req, jPath)))
            res = jsonpath.jsonpath(req, jPath)
            if not res:
                return ""
            if len(res):
                return res[0]
            else:
                return ""
        except Exception as e:
            printUtil.printCaseDevice('jsonpath-str(e):\t\t', str(e))
            printUtil.printCaseDevice('jsonpath-repr(e):\t', repr(e))
            return ""