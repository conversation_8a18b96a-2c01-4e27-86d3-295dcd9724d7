class Switch:
    def __init__(self, case_func):
        self.case_func = case_func
        self.cases = {}
        self.default_case = None

    def case(self, case_value):
        def decorator(func):
            self.cases[case_value] = func
            return func

        return decorator

    def default(self, func):
        self.default_case = func
        return func

    def __call__(self, *args, **kwargs):
        case = self.case_func(*args, **kwargs)
        if case in self.cases:
            return self.cases[case](*args, **kwargs)
        elif self.default_case is not None:
            return self.default_case(*args, **kwargs)
        else:
            return None