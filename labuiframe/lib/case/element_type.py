import dataclasses
from typing import Union

@dataclasses.dataclass
class SharkText:
    sharkKey: str
    locale: str = 'zh_HK'
    appId: str = '37007'  #不赋值代表使用默认的，代表前端的shark
    value: str = ""


@dataclasses.dataclass
class JsonPath:
    service: str
    path: str


@dataclasses.dataclass
class ViewId:
    view_id: str
    need_escape: bool = False  # 是否需要进行转义






@dataclasses.dataclass
class NodePath:
    nodes: list[Union[SharkText,JsonPath,ViewId,str,int]]


