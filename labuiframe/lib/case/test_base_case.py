__all__ = ['TestBaseCase']

import json
import zlib

import jsonpath
from labuiframe.lib.case.appCase import *
from labuiframe.lib.case.element_type import JsonPath, SharkText, ViewId, NodePath
from labuiframe.lib.utils.commonAction import CommonAction
from labuiframe.lib.utils.logsDecorator import fun_run_log, coast_time
from labuiframe.lib.utils.text_type_check import assert_text_type
from labuiframe.lib.utils.device import DeviceInfo
from poco.exceptions import PocoNoSuchNodeException

from .simple_proxy import SimpleProxy
from labuiframe.lib.utils.ElementItem import ElementItem
from ..utils.findSpecialElements import find_special_elements
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os
import time
from airtest.core.api import *
import base64
from io import BytesIO
import random
import colorsys
import re


class TestBaseCase(AppCase):
    def scroll_page(self, direction, scroll_height=0.3, wait_time=5):
        """
        滚动页面
        :param direction: 滚动方向
        :param scroll_height: 滚动高度
        :param wait_time: 滑动后等待时间
        :return:
        """
        if direction == "up":
            self.poco.scroll("vertical", percent=-scroll_height, duration=1)
            time.sleep(wait_time)
            print("向上滑动成功")
            self.assertTrue(True, "向上滑动成功")
        elif direction == "down":
            self.poco.scroll("vertical", percent=scroll_height, duration=1)
            time.sleep(wait_time)
            print("向下滑动成功")
            self.assertTrue(True, "向下滑动成功")
        else:
            self.assertTrue(False, "滑动方向错误")
            
    #直接通过坐标点击，支持相对坐标和绝对坐标
    #x,y--坐标点 is_relative--是否为相对坐标(0-1范围内的比例值)，默认为True wait_time--点击后等待时间
    def clickByCoordinate(self, seq_index, is_relative=False, wait_time=5, is_print=True):
        '''
        直接通过seq_index点击屏幕，支持相对坐标和绝对坐标
        
        示例:
        1. 点击屏幕中心: Action.clickByCoordinate(0)
        2. 点击绝对坐标点(100, 200): Action.clickByCoordinate(100, 200, is_relative=False)
        '''
        if is_print:
            print("对该控件执行点击操作")
        try:
            pos = self.find_center_from_dom_tree_by_seqIndex(seq_index)
            x = pos[0]
            y = pos[1]
            # touch((x, y)) # 有的页面使用touch点击没有反应
            self.poco.click((x, y))
            time.sleep(wait_time)
            if is_print:
                print("点击成功")
            self.assertTrue(True, "点击成功")
        except Exception as e:
            print("点击失败:{}".format(str(e)))
            self.assertTrue(False, "点击失败")
    
    def input_text_by_coordinate(self, seq_index, text_input, enter=False, is_relative=False, wait_time=5):
        '''
        通过坐标输入文本
        '''
        print("对该控件执行输入文本操作")
        try:
            self.clickByCoordinate(seq_index, is_relative, wait_time, is_print=False)
            text(text_input, enter=enter)
            time.sleep(wait_time)
            print("输入文本成功")
            self.assertTrue(True, "输入文本成功")
        except Exception as e:
            print("输入文本失败:{}".format(str(e)))
            self.assertTrue(False, "输入文本失败")


    @classmethod
    def wrapConfigParam(cls, basic_config, new_switch_map):
        config = json.loads(basic_config)
        config["newSwitchMap"] = new_switch_map
        return json.dumps(config)

    # 滑动到某一元素出现位置
    def exist(self, element):
        if isinstance(element, JsonPath):
            message = self.getRequestBodyWithClear(element.service)
            data = self.getJsPath(message, element.path)
            self._assertElementExist(data)
        elif isinstance(element, SharkText):
            value = CommonAction.getTripAppShark(element.sharkKey, locale='zh_HK', appId=element.appId)
            self._assertElementExist(value)
        elif isinstance(element, ViewId):
            self._assertElementExist(element.view_id, type='id')
        elif isinstance(element, str):
            return self._assertElementExist(element)
        else:
            return False

    def _assertElementExist(self, text, type='text'):
        node = CommonAction.searchElementwithType(self.poco, text, type=type)
        self.assertTrue(node, text + ',元素存在')

    def _parse_element(self, element):
        if isinstance(element, (str, int, list)):
            return element
        elif isinstance(element, JsonPath):
            message = self.getRequestBody(element.service)
            data = self.getJsPath(message, element.path)
            return re.escape(str(data))
        elif isinstance(element, SharkText):
            value = CommonAction.getTripAppShark(element.sharkKey, locale='zh_HK', appId=element.appId)
            return re.escape(value)
        elif isinstance(element, ViewId):
            return re.escape(element.view_id) if element.need_escape else element.view_id
        elif isinstance(element, NodePath):
            return list(map(lambda x: self._parse_element(x), element.nodes))
        else:
            raise TypeError(f'element type of {type(element)} not supported')

    # 找到targets元素
    def findAnyElement(self, target, needPrintMsg=True, needScroll=True):
        if target:
            target_proxy = SimpleProxy(self.poco, self._parse_element(target), isinstance(target, ViewId))
            if DeviceInfo.is_android_run() and DeviceInfo.get_is_simulator() == 1:
                # 模拟器场景，兼容短屏幕，找不到的话就滑动，但最多滑动一次
                target_proxy = self.look_for_element(target, "", 1)
                return target_proxy
            else:
                if target_proxy.wait(timeout=10).exists():
                    # 截图+上传
                    CommonAction.getPicUpload(self.poco)
                    # RunElement.insertElementPosition(target_proxy, targets)
                    print("成功找到该控件对象")
                    # 规避控件在最底部的问题
                    self.scroll_from_bottom(target_proxy, needScroll)
                    return target_proxy
                else:
                    print(self.get_element_find_result_log(target) if needPrintMsg else "")
                    return None
        else:
            print(self.get_element_find_result_log(target) if needPrintMsg else "")
            return None

    # 查找指定模块下的target元素
    def findElementChildByStr(self, parentStr, childStr, needPrintMsg=True):
        if parentStr and childStr:
            parent_proxy = SimpleProxy(self.poco, self._parse_element(parentStr), isinstance(parentStr, ViewId))
            final_proxy = SimpleProxy(self.poco, self._parse_element([parentStr, childStr]), False)
            #优先查找父级节点下子级节点是否存在
            if final_proxy.wait(timeout=3).exists():
                print("成功找到该控件对象")
                return final_proxy
            # 子级节点兜底
            child_proxy = SimpleProxy(self.poco, self._parse_element(childStr), isinstance(childStr, ViewId))
            if child_proxy.exists():
                print("成功找到该控件对象")
                return child_proxy
            print(self.get_element_find_result_log(childStr) if needPrintMsg else "")
            return None
            # if parent_proxy.wait(timeout=10).exists():
            #     final_proxy = SimpleProxy(self.poco, self._parse_element([parentStr,childStr]), False)
            #     if final_proxy.exists():
            #         print("成功找到该控件对象")
            #         return final_proxy
            #     else:
            #         print(self.get_element_find_result_log(childStr) if needPrintMsg else "")
            #         return None
            # else:
            #     #父级节点找不到场景，只用子级兜底
            #     child_proxy = SimpleProxy(self.poco, self._parse_element(childStr), isinstance(childStr, ViewId))
            #     if child_proxy.exists():
            #         print("成功找到该控件对象")
            #         return child_proxy
            #     else:
            #         print(self.get_element_find_result_log(childStr) if needPrintMsg else "")
            #         return None
        else:
            print(self.get_element_find_result_log(childStr) if needPrintMsg else "")
            return None


    # 查找target元素，如果endpoint存在，则一直滑动查找直到endpoint出现为止
    def look_for_element(self, target, endpoint="", limit=10):
        if (target and endpoint) is not None:
            target_proxy = SimpleProxy(self.poco, self._parse_element(target), isinstance(target, ViewId))
            endpoint_proxy = SimpleProxy(self.poco, self._parse_element(endpoint), isinstance(endpoint, ViewId))
            i = 0
            while i < limit:
                if target_proxy.exists():
                    # 截图+上传
                    CommonAction.getPicUpload(self.poco)
                    # RunElement.insertElementPosition(target_proxy, targets)
                    self.scroll_from_bottom(target_proxy)
                    print("成功找到该控件对象")
                    return target_proxy
                elif endpoint_proxy.exists() and endpoint != "":
                    print(self.get_element_find_result_log(target))
                    raise PocoNoSuchNodeException(target_proxy)
                else:
                    target_proxy.invalidate()
                    endpoint_proxy.invalidate()
                    self.poco.scroll("vertical", 0.4, duration=1)
                    i = i + 1
            # 达到最大滑动次数
            if target_proxy.exists() is False:
                print(self.get_element_find_result_log(target))
                return None
            else:
                print("成功找到该控件对象")
                return target_proxy
        elif target:
            t = SimpleProxy(self.poco, self._parse_element(target))
            if t is None or not t.exists():
                print(self.get_element_find_result_log(target))
                return None
            print("成功找到该控件对象")
            return t
        else:
            print(self.get_element_find_result_log(target))
            return None

    # 左滑右滑至边界，direction=0代表左滑，1代表右滑
    def scroll_left_right_to_edge(self, base_element, direction=0,limit=10):
        if base_element is not None and len(base_element) > 0:
            target_proxy = SimpleProxy(self.poco, self._parse_element(base_element), isinstance(base_element, ViewId))
            if not target_proxy.exists():
                print(self.get_element_find_result_log(base_element, "滑动对象不存在，无法定位滑动对象，不能确定纵轴位置"))
                return None
            target_position = target_proxy.get_position()  # 控件中心点 [x, y]
            # 从控件中心点往左滑，滑动半个屏幕宽度
            start_point = target_position
            x_position = 0 if direction == 0 else 1
            end_point = [x_position, target_position[1]]
            while limit > 0:
                before_tree = self.poco.agent.hierarchy.dump()
                before_text= CommonAction.getAllTextInModel(self.poco, base_element)
                self.poco.swipe(start_point, end_point, duration=1)
                # 滑动后判断页面前后是否一致，一致则说明已经滑动到左边界

                after_tree = self.poco.agent.hierarchy.dump()
                after_text= CommonAction.getAllTextInModel(self.poco, base_element)
                if self.compare_trees(before_tree, after_tree) and before_text == after_text:
                    print("已经滑动到{}边界".format("右" if direction == 0 else "左"))
                    del before_tree, after_tree
                    break
                del before_tree,after_tree
        else:
            print(self.get_element_find_result_log(base_element, "target 对象为空"))
            return None

    def scroll_to_top(self,percent=-0.5):
        for i in range(10):
            self.poco.scroll("vertical", percent=percent, duration=0.5)
        print("已经滑动到顶部")

    # 左滑/右滑 --不书写参数&书写滑动参数,percent代表滑动比例，direction=0代表左滑，1代表右滑
    # 以控件中心点,按照屏幕尺寸进行滑动,如果超出0或1,则取0或1
    def scroll_left_right_by_percent(self, target, percent=0.5, direction=0, duration=1):
        if target is not None and len(target) > 0:
            target_proxy = SimpleProxy(self.poco, self._parse_element(target), isinstance(target, ViewId))
            if not target_proxy.exists():
                print(self.get_element_find_result_log(target, "滑动对象不存在，无法定位滑动对象，不能确定纵轴位置"))
                return None
            target_position = target_proxy.get_position()  # 控件中心点 [x, y]
            start_x = target_position[0]
            left_end_x = 0 if target_position[0] - percent < 0 else target_position[0] - percent
            right_end_x = 1 if target_position[0] + percent > 1 else target_position[0] + percent
            end_x = right_end_x if direction == 1 else left_end_x
            self.poco.swipe(target_position, [end_x, target_position[1]], duration=duration)
            print("已经向{}滑动完成".format("左" if direction == 0 else "右"))
        else:
            print(self.get_element_find_result_log(target, "target 对象为空"))
            return None

    # 左滑右滑查找目标元素
    def scroll_left_right_look_for_element(self, base_element, target, endpoint="", direction=0, limit=10):
        if (target and endpoint and base_element) is not None:
            base_element_proxy = SimpleProxy(self.poco, self._parse_element(base_element), isinstance(base_element, ViewId))
            target_proxy = SimpleProxy(self.poco, self._parse_element(target), isinstance(target, ViewId))
            endpoint_proxy = SimpleProxy(self.poco, self._parse_element(endpoint), isinstance(endpoint, ViewId))
            i = 0
            while i < limit:
                if target_proxy.exists():
                    # 将找到的控件对象滑动到屏幕中间
                    # self.scroll_from_edge(target_proxy)
                    print("成功找到该控件对象")
                    return target_proxy
                elif endpoint_proxy.exists() and endpoint != "":
                    print(self.get_element_find_result_log(target))
                    raise PocoNoSuchNodeException(target_proxy)
                elif base_element_proxy.exists():
                    base_element_position = base_element_proxy.get_position()  # 控件中心点 [x, y]
                    # 从控件中心点往左滑，滑动半个屏幕宽度
                    start_point = base_element_position
                    # 0.05/0.95兼容边界问题
                    left_end_x = 0.05 if base_element_position[0] - 0.5 <= 0 else base_element_position[0] - 0.5
                    right_end_x = 0.95 if base_element_position[0] + 0.5 >= 1 else base_element_position[0] + 0.5
                    end_x = right_end_x if direction == 1 else left_end_x
                    end_point = [end_x, base_element_position[1]]
                    before_tree = self.poco.agent.hierarchy.dump()
                    before_text = CommonAction.getAllTextInModel(self.poco, base_element)
                    self.poco.swipe(start_point, end_point, duration=1)
                    # 滑动后判断页面前后是否一致，一致则说明已经滑动到左边界
                    after_tree = self.poco.agent.hierarchy.dump()
                    after_text = CommonAction.getAllTextInModel(self.poco, base_element)
                    if self.compare_trees(before_tree, after_tree) and before_text == after_text:
                        print("已经滑动到{}边界".format("右" if direction == 0 else "左"))
                        del before_tree, after_tree
                        break
                    i = i + 1
                    del before_tree, after_tree
                else:
                    print("已无法找到{}".format(base_element))
                    break
            # 达到最大滑动次数
            if target_proxy.exists() is False:
                print(self.get_element_find_result_log(target))
                return None
            else:
                # self.scroll_from_edge(target_proxy)
                print("成功找到该控件对象")
                return target_proxy
        elif target:
            t = SimpleProxy(self.poco, self._parse_element(target))
            if t is None or not t.exists():
                print(self.get_element_find_result_log(target))
                return None
            # self.scroll_from_edge(t)
            print("成功找到该控件对象")
            return t
        else:
            print(self.get_element_find_result_log(target))
            return None


    def scroll_to_down(self, percent=0.5):
        self.poco.scroll("vertical", percent=percent, duration=1)

    def get_element_find_result_log(self, target, append_msg: str = "") -> str:
        msg = "无法找到该控件对象:{}".format(target)
        if append_msg is not None and append_msg != "":
            msg = msg + ", desc: {}".format(append_msg)
        return msg

    def assert_exist(self, target, description=''):
        """
        find element in screen
        """
        if not isinstance(target, SimpleProxy):
            if DeviceInfo.is_android_run() and DeviceInfo.get_is_simulator() == 1:
                # 模拟器场景，兼容短屏幕，找不到的话就滑动，但最多滑动两次
                target = self.look_for_element(target, "", 2)
            else:
                target = self.findAnyElement(target)
        if target is None:
            print("屏幕上找不到该对象: {}".format(target))
            self.assertTrue(False, description)
            return
        if target.exists() is False:
            print("屏幕上找不到该对象: {}".format(target))
        self.assertTrue(target.exists(), description)

    # 不再推荐使用，建议使用assert_exist--ai生成统一使用
    def assert_exists(self, target, endpoint=None, description=''):
        """
        scroll to endpoint to look for element
        """
        if not isinstance(target, SimpleProxy):
            target = self.look_for_element(target, endpoint)
        if target is None:
            print("屏幕上找不到该对象: {}".format(target))
            self.assertTrue(False, description)
            return
        if target.exists() is False:
            print("屏幕上找不到该对象: {}".format(target))
        self.assertTrue(target.exists(), description)

    def wait_exists(self, target, timeout=10, description=''):
        """
        wait for element existing
        """
        target_proxy = SimpleProxy(self.poco, self._parse_element(target), isinstance(target, ViewId))
        target_proxy.wait(timeout)

    def assert_all_exists(self, *targets, endpoint=None, description=''):
        for target in targets:
            self.assert_exists(target, endpoint, description)

    def assert_not_exist(self, target, description=''):
        if target is None:
            self.assertTrue(True, description)
            return
        if not isinstance(target, SimpleProxy):
            target = self.findAnyElement(target, needPrintMsg=False)
        # target是None的话
        if target == None:
            self.assertTrue(True, description)
            return
        self.assertFalse(target.exists(), description)

    def assert_not_exists(self, target, endpoint=None, description=''):
        if not isinstance(target, SimpleProxy):
            target = self.look_for_element(target, endpoint)
        # target是None的话
        if target == None:
            self.assertTrue(True, description)
            return
        self.assertFalse(target.exists(), description)

    def assert_toast_exists(self, target, description=''):
        text = self._parse_element(target)
        toast_content = "".join(jsonpath.jsonpath(json.loads(self.getRequestBody("toast", reGet=1)), target.path))
        self.assertTrue(re.match(text, toast_content), description)

    def compare_trees(self, node1, node2, write_log: bool = False) -> bool:
        def console_log(message):
            if write_log:
                print(message)

        # 忽略以 com.android.systemui 作为前缀的节点
        if node1.get('name', '').startswith('com.android.systemui') and node2.get('name', '').startswith('com.android.systemui'):
            if node1.get('name') == node2.get('name'):
                console_log(f"忽略 {node1.get('name')} 和 {node2.get('name')} 的比较")
                return True

        # 检查两个节点是否都是 None 或都不是 None
        if node1 is None and node2 is None:
            console_log("两个节点都是 None，比较结果为 True")
            return True
        if node1 is None or node2 is None:
            console_log("一个节点是 None 而另一个不是，比较结果为 False")
            return False

        # 打印当前比较的节点名和text信息
        console_log(f"比较节点 {node1.get('name')} 和 {node2.get('name')}，文本分别为 {node1.get('text')} 和 {node2.get('text')}")

        node1_final_text = node1.get('name') if len(node1.get('name', '')) > 0 else node1.get('text', '')
        node2_final_text = node2.get('name') if len(node2.get('name', '')) > 0 else node2.get('text', '')

        # 检查两个节点的文本是否相同
        if node1_final_text != node2_final_text:
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的文本不同，分别为 {node1_final_text} 和 {node2_final_text}，比较结果为 False")
            return False
        else:
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的文本相同，均为 {node1_final_text}")

        # 检查子节点的数量是否相同
        children1 = node1.get("children", [])
        children2 = node2.get("children", [])
        if len(children1) != len(children2):
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点数量不同，分别为 {len(children1)} 和 {len(children2)}，比较结果为 False")
            return False
        else:
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点数量相同，均为 {len(children1)}")

        # 递归地比较每一对子节点
        for child1, child2 in zip(children1, children2):
            if not self.compare_trees(child1, child2, write_log):
                console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点在递归比较中不相同，比较结果为 False")
                return False
        return True

    def click(self, target, index=0, endpoint=None):
        if not isinstance(target, SimpleProxy):
            print("传入的是文本，需要先查找到该控件对象")
            target = self.look_for_element(target, endpoint)
        if target is None:
            print("点击失败：屏幕上找不到该对象，无法点击")
            return
        tree1 = self.poco.agent.hierarchy.dump()
        # 截图比较
        ori_url = Capture.screenCropImageUrlByNode(target)
        print("对该控件执行点击操作")
        target[index].click()
        # 避免点击后页面还未渲染出来场景
        sleep(5)
        tree2 = self.poco.agent.hierarchy.dump()
        if not self.compare_trees(tree1, tree2):
            print("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(json.dumps(tree2, default=ElementItem.custom_serializer)))
        else:
            new_url = Capture.screenCropImageUrlByNode(target)
            if AiAgentGenerate.compareImage(ori_url, new_url):
                print("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(json.dumps(tree2, default=ElementItem.custom_serializer)))
            else:
                print("点击失败：点击后页面没有变化，可能是你查找的控件对象不对")
        del tree1, tree2

    def click_these(self, target, *, endpoint=None, indices=None):
        target = self.look_for_element(target, endpoint)
        if not indices:
            for item in target:
                item.click()
        else:
            for index in indices:
                target[index].click()

    # 判断target和source是否相等
    def assert_element_equal(self, target, source, description):
        self.assertEqual(target, source, description)

    # 判断target和source是否不相等
    def assert_element_not_equal(self, target, source, description):
        self.assertNotEqual(target, source, description)

    # 判断服务器请求的XXX字段和指定的值相等 可定检查pageinfo='bookCheck'， 床单请求pageinfo='book' ,description为then的描述，target为服务器请求的字段，source为指定的值
    def assert_req_equals(self, target, source, description, pageinfo='bookCheck'):
        self.assertReqEqual(target, source, description, pageinfo)

    # 校验图片是否存在
    @fun_run_log
    def assertImgExists(self, target, description='', threshold=None, platform='android', category='hotellist'):
        result = exists(Template(Capture.fullPicturePath(self.get_capture_path(target, platform, category)), threshold=threshold))
        self.assertTrue(result, description)

    @fun_run_log
    def assertImgNotExists(self, target, description='', threshold=None, platform='android', category='hotellist'):
        result = exists(Template(Capture.fullPicturePath(self.get_capture_path(target, platform, category)), threshold=threshold))
        self.assertFalse(result, description)

    # 根据target所对应的图片进行点击
    @fun_run_log
    def clickElementByImg(self, target, platform='android', category='hotellist'):
        # self.printLog(self.TAG + 'clickElementByImg ' + '进入')
        click(Template(Capture.fullPicturePath(self.get_capture_path(target, platform, category))))
        # self.printLog(self.TAG + 'clickElementByImg ' + '执行完毕')

    def get_capture_path(self, filename, platform='android', category='hotellist'):
        """
        获得截图存储相对路径
        :param filename: 文件名
        :param platform: 平台
        :param category: 页面
        :return:
        """
        return "{}{}{}{}{}".format(platform, os.sep, category, os.sep, filename)

    # 点击返回按钮
    def clickBack(self):
        platform = DeviceInfo.getPlatform()
        if platform.lower() == 'android':
            keyevent('BACK')
            sleep(2)
        elif platform.lower() == 'ios':
            self.poco.click([0.055, 0.059])
            sleep(2)
        print("已完成操作")
        tree = self.poco.agent.hierarchy.dump()
        print("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(json.dumps(tree, default=ElementItem.custom_serializer)))
        del tree

    # 页面向下滑动一点点
    def swipeALittle(self):
        self.poco.swipe([0.5, 0.75], [0.55, 0.5])

    # 获取控件的文本,并组装入库
    def getElementTextAndDownBase(self, placeHolder, advice=None):
        allTextInPage = CommonAction.getAllTextInModel(self.poco, "<Root>")
        allTextInPageList = allTextInPage.split(CommonAction.GAP)
        print("页面上的文案列表如下，你需要在下面的列表中选择一个或多个意义接近" + placeHolder + "的文案作为断言的预期文本" + str(allTextInPageList))
        return allTextInPageList

    # 重写getElementTextAndDownBase，用于V3版本ai服务，仅输出allTextInPageList
    def getElementTextAndDownBaseNew(self, advice=None):
        allTextInPage = CommonAction.getAllTextInModel(self.poco, "<Root>")
        allTextInPageList = allTextInPage.split(CommonAction.GAP)
        print(json.dumps(allTextInPageList))

    # 根据模块Id以及占位符信息获取控件文本，并组装入库
    def getElementInfoWithModuleId(self, moduleId, advice=None):
        allTextInModule = CommonAction.getAllTextInModel(self.poco, moduleId)
        allTextInModuleList = allTextInModule.split(CommonAction.GAP)
        print(json.dumps(allTextInModuleList))

    # 根据模块 ID 获取这个模块下所有控件 ID 对应的文本控件的文本, {key: [text1, text2]}
    def getModuleTextControl(self, moduleId):
        element_id_text_list_mapping, unnamed_text_list = CommonAction.extractTextControls(self.poco, moduleId)
        print(json.dumps({"id_mapping": element_id_text_list_mapping, "unnamed_text_list": unnamed_text_list}, ensure_ascii=False))

    def get_model_text(self, rootElement):
        nodeName = ''
        if isinstance(rootElement, SimpleProxy):
            nodeName = rootElement.get_name()
        if isinstance(rootElement, str):
            nodeName = rootElement
        elif isinstance(rootElement, ViewId):
            nodeName = re.escape(rootElement.view_id) if rootElement.need_escape else rootElement.view_id
        if nodeName == '' or nodeName is None:
            print("未找到对应的模块")
            self.assertTrue(False, "未找到对应的模块")
        return CommonAction.getAllTextInModel(self.poco, nodeName)

    def assertTextInModel(self, text, rootElement,desc = ''):
        if rootElement is None:
            self.assertTrue(False, "未找到目标模块")
        model_string = self.get_model_text(rootElement)
        if text not in model_string:
            if DeviceInfo.is_android_run() and DeviceInfo.get_is_simulator() == 1:
                # 模拟器场景，兼容短屏幕，找不到的话就滑动一次（由于模块宽度不会太宽，因此暂时定义一次即可）
                self.poco.scroll("vertical", 0.3, duration=1)
                model_string = self.get_model_text(rootElement)
                if text not in model_string:
                    print("根模块中未找到 {} 字符串".format(text))
                    self.assertTrue(False, "根模块中未找到 {} 字符串,bdd为 {} ".format(text,desc))
            else:
                print("根模块中未找到 {} 字符串".format(text))
                self.assertTrue(False, "根模块中未找到 {} 字符串,bdd为 {} ".format(text,desc))
        self.assertTrue(True, "根模块中找到 {} 字符串".format(text))

    def assertTextNotInModel(self, text, rootElement,desc = ''):
        model_string = self.get_model_text(rootElement)
        if text not in model_string:
            print("根模块中未找到 {} 字符串".format(text))
            self.assertTrue(True, "根模块中未找到 {} 字符串".format(text))
        self.assertTrue(False, "根模块中找到 {} 字符串,bdd为 {} ".format(text,desc))

    def assertTextTypeInModel(self, textType, rootElement):
        """
        Assert the text type of the input text in the root element.
        :param text_type: The text type to be asserted. Supported text types: "number", "english", "chinese", "special_characters".
        :param rootElement: The root element.
        :return: None
        :throws: AssertionError if the text type of the input text is not contained in the root element.
        """
        model_string = self.get_model_text(rootElement)
        assert_result = assert_text_type(model_string, textType)
        if not assert_result:
            print("根模块中未找到 {} 类型的文本".format(textType))
            self.assertTrue(False, "根模块中未找到 {} 类型的文本".format(textType))
        self.assertTrue(True, "根模块中找到 {} 类型的文本".format(textType))

    def assertTextTypeNotInModel(self, textType, rootElement):
        """
        Assert the text type of the input text in the root element.
        :param text_type: The text type to be asserted. Supported text types: "number", "english", "chinese", "special_characters".
        :param rootElement: The root element.
        :return: None
        :throws: AssertionError if the text type of the input text is contained in the root element.
        """
        model_string = self.get_model_text(rootElement)
        assert_result = assert_text_type(model_string, textType)
        if assert_result:
            print("根模块中找到了 {} 类型的文本".format(textType))
            self.assertTrue(False, "根模块中找到了 {} 类型的文本".format(textType))
        self.assertTrue(True, "根模块未找到 {} 类型的文本".format(textType))

    def formatBDDbyLabel(self):
        url = "http://collects.fat9.qa.nt.ctripcorp.com/api/chat/formatBddByLabelId/" + HandleInfo.getMetaInfo()['label']
        limit = 0
        while limit < 3:
            try:
                response = requests.get(url, headers={"Content-Type": "application/json"}).json()
                print(response)
                self.assertTrue(False, "占位符脚本最后不允许执行成功,无需重试")
            except json.decoder.JSONDecodeError as e:
                print("formatBDDbyLabel失败" + str(e))
                limit += 1
                if limit == 3:
                    self.assertTrue(False, "formatBDDbyLabel失败" + str(e))
                else:
                    continue

    # targetUrl:基准图片url element:目标控件
    def assert_image_equal(self, baseUrl, element, description="图片比对"):
        targetUrl = Capture.screenCropImageUrlByNode(element)
        printUtil.printCaseDevice(f"assert_image_equal log，baseUrl：{ baseUrl }，targetUrl：{ targetUrl }")
        res = AiAgentGenerate.compareImage(baseUrl, targetUrl)
        self.assertTrue(res, description)

    # 将目标对象从最底部向上滑动
    def scroll_from_bottom_(self, target_proxy, needScroll=True):
        print("滑动类型：默认滑动")
        position = target_proxy.get_position()
        size = target_proxy.get_size()
        top_y = position[1] - size[1] / 2
        # 计算控件上边界距离屏幕顶部的距离，top_y的阈值不可以随便改动，否则影响历史执行！！！！！！！
        if needScroll and top_y > 0.7:
            # 使用swipe方法，定义滑动起点为当前控件中心点，避免产生半浮层滑动为点击问题
            if position[1] > 0.97:
                #如果滑动的起点Y坐标超出0.972，则会触发将app隐藏
                self.poco.swipe([position[0], position[1] - 0.05], [position[0], top_y - 0.3], duration=1)
            else:
                self.poco.swipe(position, [position[0], top_y - 0.3], duration=1)
            # position_new = target_proxy.get_position()
            # if position_new == position:
            #     # 未滑动成功，需要再次滑动--兼容目标控件被上层控件遮挡，导致无法滑动到屏幕中间（eg.trip酒店填写页，底部bar较宽，遮挡问题）
            #     self.poco.scroll("vertical", 0.6, duration=1)

    @coast_time
    def special_element_init(self):
        """
        特殊元素初始化
        """
        all_elements = self.poco.agent.hierarchy.dump()
        # 用于存储顶部元素和底部元素
        top_element = []
        bottom_element = []

        find_special_elements(all_elements, top_element, bottom_element)

        if len(top_element) == 0 or len(bottom_element) == 0:
            print("未找到底部或顶部元素")
            return

        # 在底部元素bottom_element找一个高度最小的元素，找一个高度最大的元素，记录他们的pos，size(bottom_max_element, bottom_min_element)
        # 根据列表中每个对象的pos[1]进行排序，找到最小和最大的值
        bottom_element.sort(key=lambda x: x['pos'][1])
        self.specialElement['bottom_min_element'] = bottom_element[-1]
        self.specialElement['bottom_max_element'] = bottom_element[0]

        # 在顶部元素top_element找一个高度最小的元素，找一个高度最大的元素，记录他们的pos，size(top_max_element, top_min_element)
        # 根据列表中每个对象的pos[1]进行排序，找到最小和最大的值
        top_element.sort(key=lambda x: x['pos'][1])
        self.specialElement['top_min_element'] = top_element[0]
        self.specialElement['top_max_element'] = top_element[-1]

        self.specialElement['is_init'] = False

        print("specialElement：", self.specialElement)

    @coast_time
    def scroll_from_bottom(self, target_proxy, needScroll=True):
        if self.specialElement['is_init']:
            self.special_element_init()

        if self.specialElement['bottom_max_element'] == {}:
            # 未找到底部最大元素，直接滑动
            self.scroll_from_bottom_(target_proxy, needScroll)
            return

        pos = target_proxy.get_position()
        size = target_proxy.get_size()
        top_y = pos[1] - size[1] / 2
        if top_y < 0.7:
            return
        print("滑动类型：特殊滑动")

        bottom_max_element_pos = self.specialElement['bottom_max_element']['pos']
        bottom_max_element_size = self.specialElement['bottom_max_element']['size']
        bottom_max_element_top_y = bottom_max_element_pos[1] - bottom_max_element_size[1] / 2

        bottom_min_element_pos = self.specialElement['bottom_min_element']['pos']
        bottom_min_element_size = self.specialElement['bottom_min_element']['size']
        bottom_min_element_top_y = bottom_min_element_pos[1] - bottom_min_element_size[1] / 2

        attr = {
            "pos": pos,
            "size": size,
            "type": target_proxy.attr('type'),
            "name": target_proxy.attr('name'),
            "package": target_proxy.attr('package'),
            "resourceId": target_proxy.attr('resourceId')
        }

        if attr == self.specialElement['bottom_max_element']:
            print("目标元素就是底部最大元素")
            # 判断默认滑动选择的中心点是否在底部系统横线界内
            if pos[1] >= bottom_min_element_top_y:
                print("目标元素中心点在底部系统横线下方")
                self.poco.swipe([pos[0], top_y], [pos[0], top_y - 0.3], duration=1)
            else:
                print("目标元素中心点在底部系统横线上方")
                self.poco.swipe([pos[0], pos[1]], [pos[0], pos[1] - 0.3], duration=1)
            return

        print("目标元素不是底部最大元素")
        # 判断滑动元素矩形上边界坐标小于bottom_max_element
        # [0.5, 0.9885416666666667] [1, 0.022916666666666665] 0.9770833333333333
        # 底部隐形横线坐标：
        # size :  [1.0, 0.021875]
        # pos :  [0.5, 0.9661458333333334]
        # bottom_max_element_top_y如果是底部横线，那么就要判断目标元素的上边界是否在底部横线上方top_y < (0.9661458333333334 - 0.021875 / 2)
        if top_y < min(bottom_max_element_top_y, 0.9661458333333334 - 0.021875 / 2):
            print("目标元素上边界在底部最大元素上边界上方")
            # 选择超出边界的一个点进行滑动
            self.poco.swipe([pos[0], top_y], [pos[0], top_y - 0.3], duration=1)
        else:
            print("目标元素上边界在底部最大元素上边界下方")
            # 选择bottom_max_element上边界外的点进行滑动
            self.poco.swipe([pos[0], bottom_max_element_top_y - 0.1], [pos[0], bottom_max_element_top_y - 0.4],
                            duration=1)

    # 左滑右滑场景，将控件完全滑动至屏幕内,direction=0代表左滑场景，1代表右滑场景
    def scroll_from_edge(self, target_proxy, direction=0):
        position = target_proxy.get_position()
        if position[0] < 0.2 or position[0] > 0.8:
            end_x = position[0] + 0.2 if position[0] < 0.2 else position[0] - 0.2
            # 使用swipe方法，定义滑动起点为当前控件中心点，避免产生半浮层滑动为点击问题
            self.poco.swipe(position, [end_x, position[1]], duration=1)

    # 输入框输入文案
    def inputText(self, target, input_text, index=0):
        input_target = self.findAnyElement(target)
        if input_target is None:
            return
        input_target[index].click()
        text(input_text, enter=False)
        if self.findAnyElement("com.android.systemui:id/back",needPrintMsg=False):
            print("输入法遮挡底部点击按钮了")
            self.click("com.android.systemui:id/back")

    def clickSearch(self):
        platform = DeviceInfo.getPlatform()
        if platform.lower() == 'android':
            keyevent('Enter')
            sleep(2)
        # IOS后续待支持
        # elif platform.lower() == 'ios':
        #     self.poco.click([0.055, 0.059])
        #     sleep(2)
        print("已完成操作")
        tree = self.poco.agent.hierarchy.dump()
        print("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(json.dumps(tree, default=ElementItem.custom_serializer)))
        del tree
        
    def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def _add_index_to_dom_tree(self, node, parent_index="", current_index=0):
        """递归地为DOM树添加索引"""
        # 为当前节点添加索引
        index = f"{parent_index}{current_index}" if parent_index else f"{current_index}"
        node["index"] = index
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for i, child in enumerate(node["children"]):
                self._add_index_to_dom_tree(child, f"{index}_", i)
    
    def _add_sequential_index(self, node, index_map=None, current_index=0):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
            
        # 为当前节点添加顺序索引
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_dom_tree(self):
        """获取当前页面的DOM树"""
        # 获取DOM树
        hierarchy = self.poco.agent.hierarchy.dump()
        
        # 添加索引到DOM树
        self._add_index_to_dom_tree(hierarchy)
        
        # 添加全局顺序索引
        self._add_sequential_index(hierarchy)
        
        return hierarchy

    def take_screenshot(self):
        """获取当前屏幕截图"""
        return G.DEVICE.snapshot()
    
    def get_screen_resolution(self):
        """获取屏幕分辨率"""
        display_info = G.DEVICE.display_info
        width = display_info["width"]
        height = display_info["height"]
        orientation = display_info["orientation"]

        # 根据屏幕方向调整宽高
        if orientation in [1, 3]:  # 横屏
            return height, width
        else:  # 竖屏
            return width, height

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)
    
    def _draw_node_bounding_box(self, draw, node, screen_width, screen_height, font):
        """递归地为每个节点绘制边界框"""
        # 检查节点是否有位置和大小信息
        if "payload" in node and "pos" in node["payload"] and "size" in node["payload"]:
            # 获取节点的位置和大小
            pos = node["payload"]["pos"]
            size = node["payload"]["size"]

            # 计算边界框坐标
            x1 = int((pos[0] - size[0]/2) * screen_width)
            y1 = int((pos[1] - size[1]/2) * screen_height)
            x2 = int((pos[0] + size[0]/2) * screen_width)
            y2 = int((pos[1] + size[1]/2) * screen_height)

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")
            
            # 构建标签文本
            label_text = f"{seq_index}"

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)
            
            
            
            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > screen_width:
                        label_x = screen_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > screen_height:
                            label_y = screen_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, screen_width, screen_height, font)
    
    
    def draw_bounding_boxes(self, screenshot, dom_tree):
        """在截图上绘制边界框"""
        # 直接使用PIL处理图像，不需要从OpenCV转换
        img = Image.fromarray(screenshot)
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度

        # 获取屏幕分辨率
        width, height = self.get_screen_resolution()

        # 尝试加载字体，优先使用系统字体以获得更好的显示效果
        font = None
        font_paths = [
            # macOS 系统字体
            "/System/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/Helvetica.ttc",
            # Windows 系统字体
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            # Linux 系统字体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
        ]

        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, 14)  # 稍微增大字体
                    break
            except:
                continue

        # 如果没有找到合适的字体，使用默认字体
        if font is None:
            try:
                font = ImageFont.truetype("Arial", 14)
            except:
                font = ImageFont.load_default()

        # 重置随机种子，确保颜色生成的一致性
        random.seed(42)

        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)

        # 返回PIL图像的numpy数组形式
        return np.array(img)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        # 判断是否有output目录，没有的话则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        filepath = os.path.join("output", filename)
        
        # 将numpy数组转为PIL图像并保存
        img = Image.fromarray(image)
        img.save(filepath)
        return filepath
    
    def is_page_scrollable(self):
        """检测当前页面是否可以滚动
        
        Returns:
            bool: 页面是否可滚动
        """
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = self.get_screen_resolution()
            
            # 获取当前页面截图
            before_scroll = self.take_screenshot()
            
            # 尝试滚动页面（小幅度滚动）
            start_x = screen_width / 2
            start_y = screen_height * 0.6
            end_x = screen_width / 2
            end_y = screen_height * 0.5
            
            # 执行滑动操作
            swipe((start_x, start_y), (end_x, end_y))
            
            # 等待页面响应
            time.sleep(0.5)
            
            # 获取滚动后的截图
            after_scroll = self.take_screenshot()
            
            # 比较两张截图是否有差异
            # 转换为灰度图像
            before_img = Image.fromarray(before_scroll).convert('L')
            after_img = Image.fromarray(after_scroll).convert('L')
            
            # 转换为numpy数组以便计算差异
            before_array = np.array(before_img)
            after_array = np.array(after_img)
            
            # 计算图像差异
            diff = np.abs(before_array - after_array)
            
            # 应用阈值
            thresh = np.where(diff > 30, 255, 0).astype(np.uint8)
            
            # 计算不同像素的百分比
            diff_percentage = (np.count_nonzero(thresh) / (screen_width * screen_height)) * 100
            
            # 如果差异大于阈值，则认为页面可以滚动
            is_scrollable = diff_percentage > 1.0  # 1%的差异阈值
            
            # 如果检测到页面可滚动，尝试滚动回原位置
            if is_scrollable:
                swipe((end_x, end_y), (start_x, start_y))
                time.sleep(0.5)
            
            return is_scrollable
        except Exception as e:
            return False
            
        
    def scroll_page_(self):
        """通过滚动页面查找元素"""
        # 尝试滚动页面
        try:
            # 获取屏幕尺寸
            screen_width, screen_height = self.get_screen_resolution()
            
            # 从屏幕中心向上滑动（滚动页面）
            start_x = screen_width / 2
            start_y = screen_height * 0.8
            end_x = screen_width / 2
            end_y = screen_height * 0.1
            
            # 执行滑动操作
            swipe((start_x, start_y), (end_x, end_y))
            print(f"已滚动页面，从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
            
            # 等待页面加载
            time.sleep(1.5)
        except Exception as e:
            print(f"滚动页面失败: {str(e)}")
            
    def clear_invalid_dom_tree(self, dom_tree):
        """递归清除无效的DOM树属性"""
        # 去掉payload中的visible、zOrders、package、anchorPoint、dismissable、checkable、scale、boundsInParent、focusable、touchable、enabled、longClickable、size、pos、focused、checked、editable、selected、scrollable
        if "payload" in dom_tree:
            payload = dom_tree["payload"]
            payload.pop("visible", None)
            payload.pop("zOrders", None)
            payload.pop("package", None)
            payload.pop("anchorPoint", None)
            payload.pop("dismissable", None)
            payload.pop("checkable", None)
            payload.pop("scale", None)
            payload.pop("boundsInParent", None)
            payload.pop("focusable", None)
            payload.pop("touchable", None)
            payload.pop("enabled", None)
            payload.pop("longClickable", None)
            payload.pop("size", None)
            payload.pop("pos", None)
            payload.pop("focused", None)
            payload.pop("checked", None)
            payload.pop("editalbe", None)
            payload.pop("selected", None)
            payload.pop("scrollable", None)
            
        if "children" in dom_tree:
            for child in dom_tree["children"]:
                self.clear_invalid_dom_tree(child)
        return dom_tree
    
    def find_center_from_dom_tree_by_seqIndex(self, seq_index):
        """根据seq_index递归查找对应的pos"""
        # 检查当前节点是否匹配目标seq_index
        if seq_index == self.dom_tree.get("seq_index"):
            return self.dom_tree.get("payload").get("pos")
        
        # 递归遍历子节点
        return self._find_pos_in_children(seq_index, self.dom_tree.get("children", []))
    
    def _find_pos_in_children(self, seq_index, children):
        """辅助方法：在子节点列表中递归查找特定seq_index的节点位置"""
        if not children:
            return None
            
        for node in children:
            # 检查当前子节点是否匹配
            if node.get("seq_index") == seq_index:
                return node.get("payload", {}).get("pos")
                
            # 递归检查当前子节点的子节点
            pos = self._find_pos_in_children(seq_index, node.get("children", []))
            if pos:
                return pos
                
        # 如果在所有子节点中都找不到匹配的seq_index，则返回None
        return None
    
    def ai_exec_get_dom_tree_and_page_screenshot(self):
        """ai执行，获取DOM树和页面截图"""
        try:
            # 1. 获取屏幕分辨率
            screen_width, screen_height = self.get_screen_resolution()
            viewport = {"width": screen_width, "height": screen_height}
            # 2. 判断页面是否可以滚动
            is_scrollable = True
            self.dom_tree = self.get_dom_tree()
            # 2. 获取屏幕截图
            screenshot = self.take_screenshot()
            # 3. 在截图上绘制边界框
            annotated_image = self.draw_bounding_boxes(screenshot, self.dom_tree)
            # 4. 保存标注后的图像
            timestamp = int(time.time())
            annotated_image_path = self.save_image(annotated_image, f"annotated_{timestamp}.png")
            # 将图像上传到服务器，获取图片url
            image_url = Capture.getImgOnlineUrl(self, annotated_image_path, env="fws")
            json_data = {"base64_image": image_url, "viewport": viewport, "is_scrollable": is_scrollable}
            json_str = json.dumps(json_data, ensure_ascii=False)
            # 使用base64编码确保可以安全传输
            compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
            # 调用getBase64OnlineUrl方法上传这个额外数据
            image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
            print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
    
    def get_dom_tree_and_page_screenshot(self, scroll_page=False, ai_exec=False):
        """获取当前页面的DOM树结构和页面截图"""
        try:
            # ai执行，获取DOM树和页面截图
            if ai_exec: 
                self.ai_exec_get_dom_tree_and_page_screenshot()
            # 非ai执行，获取DOM树和页面截图
            else:  
                if scroll_page:
                    self.scroll_page_()
                # 1. 获取DOM树
                dom_tree = self.get_dom_tree()
                # 2. 获取屏幕截图
                screenshot = self.take_screenshot()
                # 3. 在截图上绘制边界框
                annotated_image = self.draw_bounding_boxes(screenshot, dom_tree)
                # 4. 保存标注后的图像
                timestamp = int(time.time())
                annotated_image_path = self.save_image(annotated_image, f"annotated_{timestamp}.png")
                # 将图像上传到服务器，获取图片url
                image_url = Capture.getImgOnlineUrl(self, annotated_image_path, env="fws")
                # 将图像编码为base64
                # base64_image = self.encode_image_to_base64(annotated_image_path)
                # 清除无效的DOM树属性
                dom_tree = self.clear_invalid_dom_tree(dom_tree)
                json_data = {"dom_tree": dom_tree, "base64_image": image_url}
                json_str = json.dumps(json_data, ensure_ascii=False)
                
                # 使用base64编码确保可以安全传输
                compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
                
                # 调用getBase64OnlineUrl方法上传这个额外数据
                image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
                
                print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
            