# -*- coding: utf-8 -*-

__author__ = "tingxiao"

from pocounit.addons.poco.action_tracking import ActionTracker
from pocounit.result import PocoTestResult

from labuiframe.lib.case.suite import *
from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.mcd_util import Mcd_Util
from CtestCommon.StartStage.RNConfig import *


class AppCase(AppTestCase):
    caseResult = PocoTestResult()
    mySqlConnect = MySqlConnect()
    casename = ""
    caseConfig = None
    activityName = None

    def __init__(self, methodName='runTest'):
        self.printCaseDevice("开始执行case")
        self.dom_tree = None
        AppTestCase.__init__(self, methodName)

    @classmethod
    def setUpClass(cls):
        super(AppCase, cls).setUpClass()
        business_switch = {
            "android": cls.androidSetupHandle,
            "ios": cls.iosSetHandle,
        }
        # 使用字典模拟switch
        business_switch.get(Labconfig.suitPlatform.lower(), cls.defaultHandle)()

    @classmethod
    def androidSetupHandle(cls):
        auto_setup(logdir=True, project_root=cls._resule_collector.project_root, basedir=cls._resule_collector.root)
        cls.printCaseDevice("********初始化poco**************")
        cls.poco = Suite.poco
        # cls.page仅用于兼容app和H5共用的case
        cls.page = cls.poco
        if not current_device():
            DeviceInfo.connect_android_device()
        else:
            DeviceInfo.device = current_device()
        dev = current_device()
        cls.printCaseDevice("********device wake up**************")
        wake()
        # log cmt
        # 关闭系统弹窗
        ADB(serialno=dev.serialno).cmd("shell input keyevent KEYCODE_BACK")
        # 收缩下拉状态栏，避免上个case滑动导致状态栏展示
        try:
            name = DeviceInfo.getModelBySystem(DeviceInfo.deviceName)
            if 'MI' in name.upper() or 'Redmi' in name:
                ADB(serialno=dev.serialno).cmd("shell service call statusbar 3")
            else:
                ADB(serialno=dev.serialno).cmd("shell service call statusbar 2")
        except:
            cls.printCaseDevice("************收缩下拉状态栏***********")
        #############这里 有问题，应该放到模拟器专门处理里面  start
        # 兼容模拟器出现pixel异常弹窗
        if cls.poco(text="Close app").exists():
            cls.poco(text="Close app").click()
        #############这里 有问题，应该放到模拟器专门处理里面  start

        # if Labconfig.getAppid() == "37":
        #     if (Labconfig.get_breakSoaHookLogin() == 1):
        #         CommonAction.getAccountInfo("_TSHK58mtk96ypml", "123456asd", "IBUAPPTESTAUTHENTICATE")
        #     else:
        #         CommonAction.getAccountInfo("_TIHK105r80r5y5bd", "123456asd", "IBUAPPTESTAUTHENTICATE")
        #

        # metaInfo = cls.getMetaInfo()
        # CmtLogger.createCaseInfo(metaInfo, class_name=cls.__module__)

        cls.comLabConfigSet(cls)
        CmtLogger.createCaseInfo(HandleInfo.getMetaInfo(), class_name=cls.__module__)
        metaInfo = cls.getMetaInfo()
        if Labconfig.getAppid() == "37":
            cls.activityName = "com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity"
        elif Labconfig.getAppid() == "********":
            cls.activityName = "ctrip.business.splash.CtripSplashActivity"
        elif Labconfig.getAppid() == "5125": # 去哪儿APP
            if metaInfo.get('activity', Labconfig.get_activity()) != 'ctrip.business.splash.CtripSplashActivity': # 读取case配置
                cls.activityName = metaInfo.get('activity', Labconfig.get_activity())
                Labconfig.set_activity(cls.activityName)
            else: # 默认值
                cls.activityName = "com.mqunar.splash.SplashActivity"
            cls.printCaseDevice(
                f"***********设置active config的值为{cls.activityName},默认使用Labconfig为{Labconfig.get_activity()}：***********")
        else:
            cls.activityName = metaInfo.get('activity', Labconfig.get_activity())
            Labconfig.set_activity(cls.activityName)
            cls.printCaseDevice(
                f"***********设置active config的值为{cls.activityName},默认使用Labconfig为{Labconfig.get_activity()}：***********")

        # 日志用于自动化分析case，请勿变更
        cls.package_name = Labconfig.getPackageName()

        action_tracker = ActionTracker(cls.poco)
        cls.register_addon(action_tracker)

        if 'tracer' in cls._result_emitters:
            cls._result_emitters.pop('tracer')

        cls.printCaseDevice("********启动app**************")
        timeTempToFramework = int(round(time.time() * 1000))
        Labconfig.setTimeSplitToFramework(int(round((timeTempToFramework - cls.start_time) / 1000, 0)))
        if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            Labconfig.set_config_data(Labconfig.get_config_file_path())

            if Labconfig.get_configEnv() is not None and cls.activityName is not None:
                if cls.caseConfig is not None and len(cls.caseConfig) > 0:
                    env_config_json = json.loads(Labconfig.get_configEnv())
                    case_config_json = json.loads(cls.caseConfig)
                    config_json = dict(env_config_json, **case_config_json)
                    config_str = str(base64.b64encode(json.dumps(config_json).encode('utf-8')), 'utf-8')
                else:
                    config_str = Labconfig.get_encode_configEnv()
                    cls.printCaseDevice("获取到的configEnv值为：{}".format(config_str))

                cls.androidAdbSetup(cls, dev, config_str)
            elif cls.caseConfig is not None and len(cls.caseConfig) > 0 and cls.activityName is not None:

                cls.androidAdbSetup(cls, dev)

            else:
                start_app(cls.package_name)
        elif cls.caseConfig is not None and len(cls.caseConfig) > 0 and cls.activityName is not None and len(
                cls.activityName) > 0:

            cls.androidAdbSetup(cls, dev)
        else:
            start_app(cls.package_name)

        if cls.poco(text="OK").exists():
            cls.poco(text="OK").click()
        cls.printCaseDevice("********成功启动app2**************")
        timeTempToWakeupAPPSecondEnd = int(round(time.time() * 1000))
        Labconfig.setTimeSplitToHotelPage(int(round((timeTempToWakeupAPPSecondEnd - timeTempToFramework) / 1000, 0)))
        # cls.printLog()
        HandleInfo.clearData()

    @classmethod
    def iosSetHandle(cls):
        metaInfo = cls.getMetaInfo()
        cls.casename = metaInfo.get('casename', '')
        cls.casePlatform = metaInfo.get('platform', '')

        # log cmt
        CmtLogger.createCaseInfo(metaInfo, class_name=cls.__module__)

        # if not current_device():
        #     DeviceInfo.connect_android_device()

        cls.package_name = CommonAction.getIosPackageName(Labconfig.getAppid(), DeviceInfo.get_is_simulator())

        ori_case_config_json = json.loads(metaInfo.get('config'))
        cls.caseConfig = json.dumps(ori_case_config_json)
        #在启动前获取configenv中的listenport确保传给客户端与建立socket连接的时候端口号一支
        try:
            port = ori_case_config_json.get("listenPort")
        except:
            port = ''
        Labconfig.set_listen_port(port)
        config_str = str(base64.b64encode(cls.caseConfig.encode('utf-8')), 'utf-8')

        driver = wda.Client(HandleInfo.currentIp_port)
        cls.printCaseDevice("*********获取到的configEnv值为：{}".format(config_str))
        # 处理系统弹窗
        cls.location_allow_when_use_app(driver)
        HandleInfo.IOSSession = driver.session(cls.package_name, environment={"configEnv": config_str})
        # meta_info_emitter = cls.get_result_emitter('metaInfo')
        cls.poco = iosPoco()
        if cls.poco("open").exists():
            cls.poco("open").click()
        auto_setup(logdir=True, project_root=cls._resule_collector.project_root, basedir=cls._resule_collector.root)
        action_tracker = ActionTracker(cls.poco)
        cls.register_addon(action_tracker)
        # cls.site_capturer = SiteCaptor(cls.poco)
        # cls.register_addon(cls.site_capturer)

        if 'tracer' in cls._result_emitters:
            cls._result_emitters.pop('tracer')
    
    def location_allow_when_use_app(driver: wda.Client, time_counter = 2):
        """
        位置权限: 使用App时允许
        位置权限：['精确位置：打开', '允许一次', '使用App时允许', '不允许', 'Allow']
        :param poco:
        :return:
        """
        try:
            button_name = ''
            buttons = ['Allow']
            if driver.alert.wait(time_counter) and driver.alert.exists:
                button_name = driver.alert.click(buttons)
                printUtil.printCaseDevice("系统弹窗点击成功")
        except Exception as e:
            printUtil.printCaseDevice("系统弹窗点击失败")
        return button_name

    def defaultHandle(self):
        self.printCaseDevice("********参数错误哦，没有执行对应的switch**************")
        pass

    @classmethod
    def tripConfigHandle(cls):
        pass

    def androidAdbSetup(self, dev, confit_str=None):
        if confit_str == None:
            config_str = str(base64.b64encode(self.caseConfig.encode('utf-8')), 'utf-8')
        self.printCaseDevice("获取到的configEnv值为：{}".format(config_str))
        start_app_configenv(dev, self.package_name, "1", config_str)

        # if Labconfig.getAppid() == "37":
        #     # trip
        #     ADB(serialno=dev.serialno).shell(
        #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName), '--es', 'hideDebug 1',
        #          '--es',
        #          'configEnv %s' % (config_str)])
        #     ADB(serialno=dev.serialno).shell(
        #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName + '.alias'), '--es',
        #          'hideDebug 1', '--es',
        #          'configEnv %s' % (config_str)])
        #     ADB(serialno=dev.serialno).shell(
        #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName + '.alias2'), '--es',
        #          'hideDebug 1', '--es',
        #          'configEnv %s' % (config_str)])
        # else:
        #     # ctrip
        #     ADB(serialno=dev.serialno).shell(
        #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName), '--es', 'hideDebug 1',
        #          '--es',
        #          'configEnv %s' % (config_str)])

    # 通用的labconfig配置更改，如果是安卓或者iOS独有的，放到对应的方法里面处理
    def comLabConfigSet(self):
        metaInfo = self.getMetaInfo()

        CommonAction.setSharkListDefault()

        CommonAction.setTestCaseName(metaInfo.get('casename', ''))

        try:
            # ---------- 自助下单场景专用的，后面通过配置，控制单独工程走的case
            tags = metaInfo['tags']
            # 初始化第一个tag
            if Labconfig.getIsHotelWirelessAppGroup() == "True":
                CommonAction.getPrimaryCaseId()
                taglist = tags.split("|")
                HandleInfo.setCirculateInfos(HandleInfo.currentCirculate, len(taglist), taglist, self.casename)
                metaInfo = self.getMetaInfo()
        except Exception as e:
            self.printCaseDevice(str(e))
            self.printCaseDevice("************case没有关联的tag信息***********")
        # 这里会获取一次metaInfo的值，然后设置到全局变量中
        HandleInfo.setMetaInfo(metaInfo)
        Labconfig.setPackage(metaInfo.get('package', ""))

        try:
            extention = Labconfig.get_extention()
            self.printCaseDevice("************extention:{}s***********".format(str(extention)))
            if extention is not None and extention.get('isDebug', 0) == 1:
                # aiagent超时时间设置为1200s
                Labconfig.set_finalTimeOut(1200)
                Labconfig.set_ai_agent_debug(True)
                self.printCaseDevice(
                    "************AiAgent调试场景超时时间为{}***********".format(str(Labconfig.get_finalTimeOut())))
            else:
                timeout = metaInfo.get('timeout', Labconfig.get_caseTimeOut())
                Labconfig.set_finalTimeOut(int(timeout))
                self.printCaseDevice("************用例超时时间为" + str(timeout) + "s***********")
        except Exception:
            self.printCaseDevice("************   超时逻辑  异常地方未处理¥¥¥¥   s***********")

        # 如果是平台独有的，放入对应的setup中处理
        if Labconfig.suitPlatform.lower() == "android":
            self.androidLabConfigReset(self)
        if Labconfig.suitPlatform.lower() == "ios":
            self.iosLabConfig(self)

    # ios 的labconfig的配置更改值
    def iosLabConfig(self, metaInfo):
        ##todo 暂时没有单独处理的
        pass

    # 这里处理metainfo的数据，取出来处理后，set到labconfig的全局变量中--
    # --- 这里的主要做android独有的，ios的在ios的setup中处理
    def androidLabConfigReset(self, dev=None):
        # metaInfo = self.getMetaInfo()
        metaInfo = HandleInfo.getMetaInfo()

        # 这里处理不知道干啥的，先只在安卓中处理，后面必要的情况下，iOS需要放入通用的里面
        requestCount = metaInfo.get('requestCount', 200)
        Labconfig.setRequestCount(requestCount)
        self.printCaseDevice("************case默认requestCount数量为" + str(requestCount))

        # 这里清理缓存的，目前只有安卓试验是可以的，iOS的应该是另外的逻辑
        try:
            if metaInfo.get('cacheClear', 'false') == "false" and not Labconfig.cache_clear:
                self.printCaseDevice("************不需要清理缓存***********")
            else:
                self.printCaseDevice("************需要清理缓存***********")
                CommonAction.clearCache()
                # 清理缓存后需要重新启动一下获取AB实验
                start_app(Labconfig.getPackageName())
                # 重新获取crn增量--去除增量下载逻辑
                stop_app(Labconfig.getPackageName())
        except Exception:
            self.printCaseDevice("************默认不清理缓存***********")

        # 处理本地mock
        self.lockMockProcess(self, metaInfo)

    def lockMockProcess(self, metaInfo):
        # 默认true,不影响整个项目的配置
        singleLocalMock = "false"
        singleLocalMock = metaInfo.get('localMock', singleLocalMock)
        self.printCaseDevice("*****本地mock:" + str(singleLocalMock))
        try:
            mockKeyName = 'flightMockKey' if Labconfig.getAppid() == "37" else 'mockKey'
            ori_case_config_json = json.loads(metaInfo.get('config'))
            # 如果case中设置了cid，就使用case中的；否则使用数据库数据
            self.configCidHandle(ori_case_config_json)
            CmtLogger.createMetaConfig(metaInfo)
            self.printCaseDevice("*****case config:" + self.caseConfig)
            HandleInfo.setCurrentMockKey(ori_case_config_json.get(mockKeyName, ""))
            if Labconfig.getOpenLocalMock() == "True" and singleLocalMock.lower() == "true":
                self.pushMockKeyMessage(HandleInfo.getCurrentMockKey())
            elif singleLocalMock.lower() == "false":
                self.removeLocalMockFile(HandleInfo.getCurrentMockKey())
        except Exception as e:
            self.printCaseDevice('*****lockMockProcess client config fail:\t\t', str(e))

    def configInfo(self, ori_case_config_json):
        metaInfo = self.getMetaInfo()
        if ori_case_config_json.get("SetCid") is None:
            self.caseConfig = json.dumps(ori_case_config_json)
        else:
            self.caseConfig = metaInfo.get('config', '')

    @classmethod
    def configCidHandle(cls, ori_case_config_json):
        dev = current_device()

        # metaInfo = cls.getMetaInfo()
        metaInfo = HandleInfo.getMetaInfo()
        if ori_case_config_json.get("SetCid") is None:
            cls.printCaseDevice("******case中没有设置cid")
            if HandleInfo.getCurrentClientid() == "":
                tableCid = CommonAction.getClientId(Labconfig.getAppid(), dev.serialno)
            else:
                tableCid = HandleInfo.getCurrentClientid()
            if len(tableCid) == 0:
                cls.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())),
                                    "get clientid is empty")
            else:
                cls.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "get clientid:",
                                    tableCid)
                HandleInfo.setCurrentClientid(tableCid)
            if tableCid:
                try:
                    cidDict = int(tableCid)
                    ori_case_config_json["SetCid"] = cidDict
                except Exception as e:
                    printUtil.printCaseDevice("cid转换异常：{}".format(str(e)))
            cls.caseConfig = json.dumps(ori_case_config_json)
        else:
            cidVal = ori_case_config_json.get("SetCid")
            # 日志用于自动分析失败case，请不要变更！！
            cls.printCaseDevice("get clientid为:", str(cidVal))
            cls.caseConfig = metaInfo.get('config', '')
            HandleInfo.setCurrentClientid(str(cidVal))

    # @classmethod
    # def printLog(cls):
    #     # 日志用于自动分析case，请不要变更！
    #     # ctrip-case日志增加登录打印，trip已有
    #     if Labconfig.get_loginInfo() != "":
    #         cls.printCaseDevice("登录信息为**" + Labconfig.get_loginInfo() + "**")
    #     # 增加增量获取日志-去除增量下载结果
    #     '''
    #     if len(Labconfig.get_channelCode()) > 0 and Labconfig.getAppversion() != "":
    #         cls.printCaseDevice("需要下载增量信息**" + str(set(Labconfig.get_channelCode())).replace("\'", "\"") + "**")
    #         # cls.printCaseDevice("增量分支信息**" + Labconfig.get_crnBranchName() + "**")
    #         if Labconfig.get_downloadChannelCode() != "":
    #             cls.printCaseDevice("成功获取的增量信息**" + str(Labconfig.get_downloadChannelCode()) + "**")
    #             cls.printCaseDevice("下载增量时间**" + Labconfig.get_downloadChannelCodeTime() + '**')
    #             # 增加增量下载结果日志信息
    #             if len(Labconfig.get_failLoadChannelCode()) > 0:
    #                 cls.printCaseDevice("下载失败增量信息**" + str(Labconfig.get_failLoadChannelCode()) + "**")
    #             else:
    #                 printUtil.printCaseDevice("**增量下载成功**")
    #         else:
    #             cls.printCaseDevice("**未获取到下载增量信息**")
    #     else:
    #         cls.printCaseDevice("**无需下载增量**")
    #     '''
    #
    #     # 增加clientId获取日志
    #     if HandleInfo.getCurrentClientid() != "":
    #         cls.printCaseDevice("clientId信息**" + HandleInfo.getCurrentClientid() + "**")
    #     # 增加MockId获取日志
    #     if HandleInfo.getCurrentMockKey() != "":
    #         cls.printCaseDevice("mockId信息**" + HandleInfo.getCurrentMockKey() + "**")
    #     # 增加openUrl获取日志
    #     if Labconfig.get_openUrl() != "":
    #         cls.printCaseDevice("openUrl信息**" + Labconfig.get_openUrl() + "**")
    #     # 增加本地mock日志
    #     # if Labconfig.getLocalMockStatus():
    #     #     cls.printCaseDevice("**本地Mock成功**")
    #     # else:
    #     #     cls.printCaseDevice("********")

    @classmethod
    def getConfigEnv(cls, caseConfig):
        try:
            configEnv = json.loads(caseConfig)
            print("转换前的configEnv:{}".format(str(configEnv)))
            if "channelCode" in configEnv and len(configEnv["channelCode"]) > 0 and Labconfig.getAppversion() != "":
                channelList = []
                for i in range(len(configEnv["channelCode"])):
                    dict = {"channelCode": "", "buildId": 0}
                    branchName = cls.transferBranchName(configEnv["channelCode"][i])
                    buildId = Mcd_Util.getCrnBuildId(configEnv["channelCode"][i], branchName, Labconfig.getAppversion(),
                                                     Labconfig.getAppid())
                    if (buildId > 0):
                        dict["channelCode"] = configEnv["channelCode"][i]
                        dict["buildId"] = buildId
                        channelList.append(dict)
                    else:
                        cls.printCaseDevice('*******branch has no buildId!*****')
                        cls.printCaseDevice("channelCode:{},builid:{}".format(configEnv["channelCode"][i], buildId))
                if len(channelList) > 0:
                    channelList = "," + "\"channelList\"" + ":" + str(channelList).replace("\'", "\"")
                    configEnv = caseConfig[:-1] + channelList + "}"
            else:
                cls.printCaseDevice("未获取到channelcode相关信息，无需转换！")
                configEnv = caseConfig
            return str(configEnv)
        except:
            return caseConfig
            cls.printCaseDevice("转换异常！")

    @classmethod
    def transferBranchName(cls, channelCode):
        branchName = ""
        presaleBundleName = ["rn_hotel_packageList", "rn_hotel_packageOrders", "rn_hotel_package", "rn_boom"]
        tripCommentBundleName = ["rn_hotel_comment_submit"]
        ctripCommentBundleName = ["rn_hotelCommentSubmit"]
        if (datetime.datetime.today().weekday()) == 3:
            # 3是周四
            if channelCode in presaleBundleName:
                branchName = "rel/" + Labconfig.getAppversion() + "_prod"
            elif channelCode in ctripCommentBundleName:
                branchName = "release/ctrip/" + Labconfig.getAppversion()
            elif channelCode in tripCommentBundleName:
                branchName = "release/trip/" + Labconfig.getAppversion()
            else:
                branchName = "rel/" + Labconfig.getAppversion()
        else:
            if channelCode in presaleBundleName:
                branchName = "rel/" + Labconfig.getAppversion() + "_dev"
            elif channelCode in tripCommentBundleName or channelCode in ctripCommentBundleName:
                branchName = "develop"
            else:
                branchName = "dev/" + Labconfig.getAppversion()
        del presaleBundleName, ctripCommentBundleName, tripCommentBundleName
        return branchName

    @classmethod
    def pushMockKeyMessage(cls, mockKey):
        if mockKey == "":
            return
        headers = {'content-type': 'application/json;charset=utf8'}
        dev = current_device()
        serial = dev.serialno
        url = "http://mock.fws.qa.nt.ctripcorp.com/SuiteApi/GetCaseConfigInfo"
        body = {
            "CaseID": int(mockKey),
            "UserDomain": "cn1",
            "UserName": "hyang9"
        }
        dataInfo = requests.post(url, data=json.dumps(body), headers=headers).json()
        apiList = dataInfo["MessageBody"]["ApiConfigs"]
        try:
            if (len(apiList) > 0):
                Labconfig.setLocalMockStatus(True)
        except:
            printUtil.printCaseDevice("获取本地Mock信息失败！")
        for item in apiList:
            serviceName = item["InterfaceName"]
            messageList = item["MessageList"]
            if item["IsMocking"] == False:
                continue
            for i in messageList:
                if i["MessageType"] == "1":
                    messageContent = i["MessageContent"]
                    fileName = serial + "_" + serviceName + ".json"
                    HandleInfo.localMockNameList.append(fileName)
                    url = cls._resule_collector.project_root + os.sep + "mockKey" + os.sep
                    if not os.path.exists(url):
                        os.mkdir(url)
                    printUtil.printCaseDevice("开始写入文件")
                    f = open(url + fileName, "w", encoding="UTF-8")
                    f.write(messageContent)
                    f.close()
                    printUtil.printCaseDevice("开始推送文件")
                    if "-" in serviceName:
                        serviceName = str(serviceName).split('-')[1]
                    out = ADB(serialno=serial).cmd(
                        "push " + url + fileName + " /storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey + "/" + serviceName + ".json")
                    printUtil.printCaseDevice("out")
                    os.remove(url + fileName)

    @classmethod
    def removeLocalMockFile(cls, mockKey):
        # 先删除手机里面mockkey的文件夹下的内容
        try:
            url = "/storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey + "/*.*"
            dev = current_device()
            serial = dev.serialno
            ADB(serialno=serial).cmd("shell rm " + url)
            # 再删除文件夹
            url = "/storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey
            ADB(serialno=serial).cmd("shell rm -r " + url)
        except:
            printUtil.printCaseDevice("本地mock文件不存在或无需删除")

    @classmethod
    def watchTemplate(cls, obj):
        if obj is None:
            return None
        if isinstance(obj, Template):
            url = Capture.getOnlineImgPath(obj.filepath)
            if url == '':
                return None
            cur = cls.poco.get_screen_size()
            return Template(url, threshold=obj.threshold, record_pos=obj.record_pos, resolution=cur, rgb=obj.rgb,
                            scale_max=obj.scale_max, scale_step=obj.scale_step)
        else:
            return None
