__all__ = ['WebTestCase']

import base64
import time
from io import BytesIO
import json
import os
import re
import zlib
import math
import random
import colorsys

from playwright.sync_api import Page, Locator
from PIL import Image, ImageDraw, ImageFont
from labuiframe import CommonAction
from labuiframe.lib.case.element_type import ViewId
from labuiframe.lib.case.web_case import WebTestCase
from labuiframe.lib.result.logger import CmtLogger, LogCategory
from labuiframe.lib.utils.AiAgentGenerate import AiAgentGenerate
from labuiframe.lib.utils.ElementItem import ElementItem
from labuiframe.lib.utils.capture import Capture
from labuiframe.lib.utils.text_type_check import assert_text_type
from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.utils.web_element_locator import WebElementLocator


class WebBaseCase(WebTestCase):
    def __init__(self, *args, **kwargs):
        super(WebBaseCase, self).__init__(*args, **kwargs)
    
    def scroll_page(self, direction, scroll_height=100):
        """
        滚动页面
        :param direction: 滚动方向
        :param scroll_height: 滚动高度
        :return:
        """
        if direction == "down":
            self.page.evaluate('window.scrollTo(0, -{})'.format(scroll_height))
            self.page.wait_for_timeout(1000 * 5)
            print("向下滑动成功")
        elif direction == "up":
            self.page.evaluate('window.scrollTo(0, {})'.format(scroll_height))
            self.page.wait_for_timeout(1000 * 5)
            print("向上滑动成功")
        
    def clickByCoordinate(self, seq_index, is_print=True):
        """
        通过坐标点击元素
        :param x: x坐标
        :param y: y坐标
        :param kwargs: 其他参数
        :return:
        """
        if is_print:
            print("对该控件执行点击操作")
        try:
            # 根据seq_index查找pos
            pos = self.find_center_from_dom_tree_by_seqIndex(seq_index)
            self.page.mouse.click(pos["x"], pos["y"])
        except Exception as e:
            print("点击失败:{}".format(str(e)))
            return
        # 避免点击后页面还未渲染出来场景
        self.page.wait_for_timeout(1000 * 5)
        self.page.wait_for_load_state("load", timeout=30000)
        # 点击完成后判断是否有新开页面，如果有则切换到新页面
        if len(self.context.pages) > 1:
            self.switch_new_page()
        if is_print:
            print("点击成功")
            
    def input_text_by_coordinate(self, seq_index, text, **kwargs):
        """
        通过坐标输入文本
        :param x: x坐标
        :param y: y坐标
        :param text: 文本
        :param kwargs: 其他参数
        :return:
        """
        print("对该控件执行输入文本操作")
        try:
            self.clickByCoordinate(seq_index, is_print=False)
            self.page.keyboard.type(text)
            self.page.wait_for_timeout(1000 * 5)
            print("输入文本成功")
        except Exception as e:
            print("输入文本失败:{}".format(str(e)))
    
    def hover_for_ai(self, seq_index, **kwargs):
        """
        通过坐标悬停
        :param x: x坐标
        :param y: y坐标
        :param kwargs: 其他参数
        :return:
        """
        print("对该控件执行悬浮操作")
        try:
            # 根据seq_index查找pos
            pos = self.find_center_from_dom_tree_by_seqIndex(seq_index)
            self.page.mouse.move(pos["x"], pos["y"], **kwargs)
        except Exception as e:
            print("悬浮失败:{}".format(str(e)))
            return
        # 避免点击后页面还未渲染出来场景
        self.page.wait_for_timeout(1000 * 2)
        self.page.wait_for_load_state("load", timeout=30000)
        print("悬浮成功")

    def open_url(self, url):
        """
        打开url
        :param url: url
        :return:
        """
        # 等待页面加载完成
        self.page.goto(url)
        self.page.wait_for_load_state("load", timeout=30000)
        self.page.wait_for_load_state("domcontentloaded", timeout=30000)
        self.page.wait_for_load_state("networkidle", timeout=30000)
        printUtil.printCaseDevice("打开url成功！")
    
    def set_page_viewport_size(self, width, height):
        """
        设置页面视口大小
        :param width: 宽度
        :param height: 高度
        :return:
        """
        self.page.set_viewport_size({"width": width, "height": height})
        printUtil.printCaseDevice("设置页面视口大小成功！{}*{}".format(width, height))

    def look_for_element(self, target, endpoint="", limit=10, needPrintMsg=True, is_blur=False):
        """
        查找元素
        :param target: 目标元素
        :param endpoint: 终点
        :param limit: 限制
        :return:
        """
        try:    
            if is_blur:
                # 退出光标，只影响当前获得焦点的元素（通常是输入框）
                # 仅仅是让元素失去焦点状态，不会触发点击事件，不会改变页面的其他状态
                # 只有look_for_element和findAnyElement都是true外在操作中调用都为false，兼容场景为点评填写光标存在时校验和退出光标后校验
                self.page.evaluate('document.activeElement.blur()')
            target_element = WebElementLocator.elementLocator(self.page, target)
            # 兼容滑动查找的场景，为防止页面滑动导致运行时间增加，只对特定的页面做兼容
            if target_element is None :
                target_element = self.look_for_element_by_scroll(target,limit=limit)
            if target_element is not None:
                printUtil.printCaseDevice(f"找到元素{target}")
                return target_element
            else:
                printUtil.printCaseDevice(self.get_element_find_result_log(target) if needPrintMsg else "")
                return None
        except Exception as e:
            printUtil.printCaseDevice("查找控件异常{}".format(str(e)))
            return None

    def look_for_element_by_scroll(self, target,  limit=10):
        scroll_element = WebElementLocator.elementLocator(self.page, "hotelListDom")
        if scroll_element and scroll_element.element_handle():
            i = 0
            while i < limit:
                element_handle = scroll_element.element_handle()
                element_handle.evaluate('element => element.scrollBy(0, 3000)', element_handle)
                self.page.wait_for_timeout(1000)
                target_element = WebElementLocator.elementLocator(self.page, target)
                if target_element:
                    return target_element
                else:
                    i = i + 1
        scroll_element = WebElementLocator.elementLocator(self.page, "package_info_module_title")
        if scroll_element:
            script = """
                (function() {
                    function scrollElementToContentBottom(element) {
                        // 先滚动到顶部
                        // element.style.transform = 'translate3d(0px, 0px, 0px)';
                        // element.style.transitionDuration = '0ms';
                        
                        // 等待DOM更新
                        setTimeout(() => {
                            simulateNaturalScroll(element);
                        }, 100);
                    }
                    
                    function simulateNaturalScroll(element) {
                        function scroll() {
                            
                            // 触发滚动事件
                            triggerScrollEvents(element);
                            
                        }
                        
                        scroll();
                    }
                    
                    function triggerScrollEvents(element) {
                        // 触发滚动事件
                        const scrollEvent = new Event('scroll', {
                            bubbles: true,
                            cancelable: true
                        });
                        window.dispatchEvent(scrollEvent);
                        element.dispatchEvent(scrollEvent);
                        
                        // 触发 wheel 事件
                        const wheelEvent = new WheelEvent('wheel', {
                            bubbles: true,
                            cancelable: true,
                            deltaY: 100
                        });
                        element.dispatchEvent(wheelEvent);
                    }
                    
                    function findElementWithTransform() {
                        const allElements = document.querySelectorAll('*');
                        for (let i = 0; i < allElements.length; i++) {
                            const element = allElements[i];
                            const computedStyle = window.getComputedStyle(element);
                            const transformValue = computedStyle.getPropertyValue('transform');
                            if (transformValue && transformValue !== 'none') {
                                console.log("找到可能负责滚动的元素:", element, transformValue);
                                return element;
                            }
                        }
                        console.log("没有找到具有 transform 属性的元素");
                        return null;
                    }
                    
                    // 使用示例
                    const scrollingElement = findElementWithTransform();
                    if (scrollingElement) {
                        scrollElementToContentBottom(scrollingElement);
                    }
                })();
                """
            i = 0
            while i < limit:
                self.page.evaluate(script)
                target_element = WebElementLocator.elementLocator(self.page, target)
                if target_element:
                    return target_element
                else:
                    i = i + 1
        return None

    def assert_exist(self, target, description=''):
        """
        校验元素存在
        :param target: 目标元素
        :param description: 描述
        :return:
        """
        text = target
        target = self.look_for_element(target, is_blur=False)
        if target is not None:
            printUtil.printCaseDevice(f"找到元素{target}")
            self.assertTrue(True, description)
        else:
            # 使用assertTextInModel进行兜底文本查找
            self.assertTrue(self.assertTextInRoot(text), description)

    def assert_not_exist(self, target, description=''):
        """
        校验元素不存在
        :param target: 目标元素
        :param description: 描述
        :return:
        """
        if target is None:
            self.assertTrue(True, description)
            return
        target = self.findAnyElement(target, needPrintMsg=False)
        # target是None的话
        if target is None:
            # 使用assertTextInModel进行兜底文本查找（如果findAnyElement不存在，则认为元素不存在，无需兜底）
            self.assertTrue(True, description)
            return
        self.assertFalse(True, description)

    def get_element_find_result_log(self, target, append_msg = ""):
        """
        获取元素查找结果日志
        :param target: 目标元素
        :param append_msg: 附加信息
        :return:
        """
        msg = "无法找到该控件对象:{}".format(target)
        if append_msg is not None and append_msg != "":
            msg = msg + ", desc: {}".format(append_msg)
        return msg

    def findAnyElement(self, target, needPrintMsg=True, is_blur=False)->Locator:
        """
        查找任意元素
        :param target: 目标元素
        :param needPrintMsg: 是否打印信息
        :return:
        """
        return self.look_for_element(target, needPrintMsg=needPrintMsg, is_blur=is_blur)


    def findElementChildByStr(self, parentStr, childStr, needPrintMsg=True):
        """
        查找父节点的下子节点
        :param parentStr:
        :param childStr:
        :param needPrintMsg:
        :return:
        """
        if parentStr and childStr:
            parent = self.findAnyElement(parentStr, needPrintMsg=needPrintMsg)
            if parent:
                try:
                    child = WebElementLocator.elementLocator(parent.first, childStr)
                    if child:
                        return child

                    # 兜底查找
                    child = self.findAnyElement(childStr, needPrintMsg=needPrintMsg)
                    if child:
                        return child
                    
                    printUtil.printCaseDevice(self.get_element_find_result_log(childStr) if needPrintMsg else "")
                    return None
                    
                except Exception as e:
                    printUtil.printCaseDevice(f"查找子元素时发生异常: {str(e)}")
                    # 兜底查找
                    child = self.findAnyElement(childStr, needPrintMsg=needPrintMsg)
                    return child if child else None
            else:
                # 父级节点找不到场景，只用子级兜底
                return self.findAnyElement(childStr, needPrintMsg=needPrintMsg)
        else:
            printUtil.printCaseDevice(self.get_element_find_result_log(childStr) if needPrintMsg else "")
            return None

    def assertTextInModel(self, text, rootElement,desc = ''):
        """
        校验模块中包含文案
        :param text: 文案
        :param rootElement: 根元素
        :return:
        """
        if rootElement is None:
            self.assertTrue(False, "未找到目标模块")
        model_string = self.get_model_text(rootElement)
        if text not in model_string:
            print("根模块中未找到 {} 字符串".format(text))
            self.assertTrue(False, "根模块中未找到 {} 字符串,bdd为 {} ".format(text,desc))
            return
        self.assertTrue(True, "根模块中找到 {} 字符串".format(text))
    
    def assertTextInRoot(self, text):
        """
        校验根节点中包含文案
        :param text: 文案
        :return:
        """
        if isinstance(text, str):   
            model_string = self.get_model_text("<Root>")
            if text not in model_string:
                printUtil.printCaseDevice("(assertTextInRoot)根模块中无法找到 {} 字符串".format(text))
                return False
            printUtil.printCaseDevice("(assertTextInRoot)根模块中找到 {} 字符串".format(text))
            return True
        else:
            printUtil.printCaseDevice("无法找到目标模块")
            return False

    def clickBack(self):
        """
        点击返回
        :return:
        """
        self.page.go_back()
        printUtil.printCaseDevice("web后退页面成功！")
        tree = CommonAction.getWebhierarchy(self.page)
        printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
            json.dumps(tree, default=ElementItem.custom_serializer)))
        del tree

    def clickForward(self):
        """
        点击前进
        :return:
        """
        self.page.go_forward()
        printUtil.printCaseDevice("web前进页面成功！")
        tree = CommonAction.getWebhierarchy(self.page)
        printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
            json.dumps(tree, default=ElementItem.custom_serializer)))
        del tree
        
    def hover(self, target, index=0):
        """
        悬停
        :param target: 目标元素
        :param index: 索引
        :return:
        """
        if isinstance(target, str):
            printUtil.printCaseDevice("传入的是文本，需要先查找到该控件对象")
            target = self.look_for_element(target, is_blur=False)
        if target is None:
            printUtil.printCaseDevice("悬停失败：web页面找不到该对象，无法悬停")
            return
        tree1 = CommonAction.getWebhierarchy(self.page)
        # 截图比较
        ori_url = Capture.screenCropImageUrlByNode(target.nth(index))
        printUtil.printCaseDevice("对该控件执行悬停操作")
        target.nth(index).hover()
        # 避免点击后页面还未渲染出来场景
        self.page.wait_for_timeout(1000 * 2)
        tree2 = CommonAction.getWebhierarchy(self.page)
        printUtil.printCaseDevice("悬停成功")
        # printUtil.printCaseDevice("悬停成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
        #     json.dumps(tree2, default=ElementItem.custom_serializer)))
        if not self.compare_trees(tree1, tree2):
            printUtil.printCaseDevice("悬停成功")
            # printUtil.printCaseDevice("悬停成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
            #     json.dumps(tree2, default=ElementItem.custom_serializer)))
            del tree1, tree2
            return True
        else:
            new_url = Capture.screenCropImageUrlByNode(target.nth(index))
            if AiAgentGenerate.compareImage(ori_url, new_url):
                printUtil.printCaseDevice("悬停成功")
                # printUtil.printCaseDevice("悬停成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
                #     json.dumps(tree2, default=ElementItem.custom_serializer)))
                del tree1, tree2
                return True
            else:
                printUtil.printCaseDevice("悬停失败：悬停后页面没有变化，可能是你查找的控件对象不对")
                del tree1, tree2
                return False
                
        
    
    def click(self, target, index=0):
        """
        点击
        :param target: 目标元素
        :param index: 索引
        :return:
        """
        if isinstance(target, str):
            printUtil.printCaseDevice("传入的是文本，需要先查找到该控件对象")
            target = self.look_for_element(target, is_blur=False)
        if target is None:
            printUtil.printCaseDevice("点击失败：web页面找不到该对象，无法点击")
            return
        tree1 = CommonAction.getWebhierarchy(self.page)
        # 截图比较
        ori_url = Capture.screenCropImageUrlByNode(target.nth(index))
        printUtil.printCaseDevice("对该控件执行点击操作")
        try:
            target.nth(index).click(timeout=10000)
        except Exception as e:
            printUtil.printCaseDevice("点击失败：点击后页面没有变化，可能是你查找的控件对象不对")
            return
        # 避免点击后页面还未渲染出来场景
        self.page.wait_for_timeout(1000 * 5)
        self.page.wait_for_load_state("load", timeout=30000)
        # 点击完成后判断是否有新开页面，如果有则切换到新页面
        if len(self.context.pages) > 1:
            self.switch_new_page()
        tree2 = CommonAction.getWebhierarchy(self.page)
        printUtil.printCaseDevice("点击成功")
        # printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
        #     json.dumps(tree2, default=ElementItem.custom_serializer)))
        if not self.compare_trees(tree1, tree2):
            printUtil.printCaseDevice("点击成功")
            # printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
            #     json.dumps(tree2, default=ElementItem.custom_serializer)))
        else:
            new_url = Capture.screenCropImageUrlByNode(target.nth(index))
            # 如果new_url为空，则认为点击成功，说明点击的对象可能不存在或者不可见了，例如关闭弹窗
            if new_url == "" or AiAgentGenerate.compareImage(ori_url, new_url):
                printUtil.printCaseDevice("点击成功")
                # printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
                #     json.dumps(tree2, default=ElementItem.custom_serializer)))
            else:
                printUtil.printCaseDevice("点击失败：点击后页面没有变化，可能是你查找的控件对象不对")
        del tree1, tree2

    def compare_trees(self, node1, node2, write_log: bool = False) -> bool:
        """
        比较两个节点树
        :param node1: 节点1
        :param node2: 节点2
        :param write_log: 是否写日志
        :return:
        """
        def console_log(message):
            if write_log:
                print(message)

        # 检查两个节点是否都是 None 或都不是 None
        if node1 is None and node2 is None:
            console_log("两个节点都是 None，比较结果为 True")
            return True
        if node1 is None or node2 is None:
            console_log("一个节点是 None 而另一个不是，比较结果为 False")
            return False

        # 打印当前比较的节点名和text信息
        console_log(
            f"比较节点 {node1.get('name')} 和 {node2.get('name')}，文本分别为 {node1.get('text')} 和 {node2.get('text')}")

        node1_final_text = node1.get('name') if len(node1.get('name', '')) > 0 else node1.get('text', '')
        node2_final_text = node2.get('name') if len(node2.get('name', '')) > 0 else node2.get('text', '')

        # 检查两个节点的文本是否相同
        if node1_final_text != node2_final_text:
            console_log(
                f"节点 {node1.get('name')} 和 {node2.get('name')} 的文本不同，分别为 {node1_final_text} 和 {node2_final_text}，比较结果为 False")
            return False
        else:
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的文本相同，均为 {node1_final_text}")

        # 检查子节点的数量是否相同
        children1 = node1.get("children", [])
        children2 = node2.get("children", [])
        if len(children1) != len(children2):
            console_log(
                f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点数量不同，分别为 {len(children1)} 和 {len(children2)}，比较结果为 False")
            return False
        else:
            console_log(f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点数量相同，均为 {len(children1)}")

        # 递归地比较每一对子节点
        for child1, child2 in zip(children1, children2):
            if not self.compare_trees(child1, child2, write_log):
                console_log(
                    f"节点 {node1.get('name')} 和 {node2.get('name')} 的子节点在递归比较中不相同，比较结果为 False")
                return False
        return True

    def switch_new_page(self):
        """
        切换新页面
        :return:
        """
        self.page = self.context.pages[-1]
        self.page.wait_for_load_state("load")
        self.page.wait_for_timeout(1000 * 3)  # 等待秒确保页面加载完成
        printUtil.printCaseDevice("切换新页面成功！")
        # tree = CommonAction.getWebhierarchy(self.page)
        # printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
        #     json.dumps(tree, default=ElementItem.custom_serializer)))
        # del tree

    def switch_old_page(self):
        """
        切换旧页面（上一个页面）
        :return:
        """
        self.page = self.context.pages[-2]
        printUtil.printCaseDevice("切换旧页面成功！")
        # tree = CommonAction.getWebhierarchy(self.page)
        # printUtil.printCaseDevice("点击成功: 输出当前UI树信息，供ai生成服务进行解析，ui_tree_info:{}".format(
        #     json.dumps(tree, default=ElementItem.custom_serializer)))
        # del tree

    def inputText(self, target, input_text, index=0):
        """
        输入文本
        :param target: 目标元素
        :param input_text: 输入文本
        :return:
        """
        if isinstance(target, str):
            printUtil.printCaseDevice("传入的是文本，需要先查找到该控件对象")
            target = self.look_for_element(target, is_blur=False)
        if target is None:
            printUtil.printCaseDevice("输入失败：web页面找不到该对象，无法输入")
            return
        try:
            target.nth(index).fill(input_text, force=True)
        except Exception as e:
            # 遍历 target.first的一级子元素，如果子元素是input、textarea、contenteditable，则直接输入
            target.nth(index).locator("input, textarea, contenteditable").first.fill(input_text, force=True)
        printUtil.printCaseDevice("{} 输入成功：{}".format(target, input_text))

    def get_model_text(self, rootElement):
        """
        获取模块文本
        :param rootElement: 根元素
        :return:
        """
        if isinstance(rootElement, Locator):
            nodeName = rootElement.first.get_attribute("testid") or rootElement.first.get_attribute("page-module") or rootElement.first.get_attribute("data-exposure")
            if nodeName is None:
                self.assertTrue(False, "未找到对应的模块")
            if "viewID" in nodeName:
                # 解析json字符串
                nodeName = json.loads(nodeName).get("viewID")
            if "ubtKey" in nodeName:
                # 解析json字符串
                nodeName = json.loads(nodeName).get("ubtKey")
        if isinstance(rootElement, str):
            nodeName = rootElement
        elif isinstance(rootElement, ViewId):
            nodeName = re.escape(rootElement.view_id) if rootElement.need_escape else rootElement.view_id
        if nodeName == '' or nodeName is None:
            self.assertTrue(False, "未找到对应的模块")
        printUtil.printCaseDevice("获取到的testid为：{}".format(nodeName))
        return CommonAction.getAllTextInModel(self.page, nodeName)


    def assertTextTypeInModel(self, textType, rootElement):
        """
        Assert the text type of the input text in the root element.
        :param text_type: The text type to be asserted. Supported text types: "number", "english", "chinese", "special_characters".
        :param rootElement: The root element.
        :return: None
        :throws: AssertionError if the text type of the input text is not contained in the root element.
        """
        model_string = self.get_model_text(rootElement)
        assert_result = assert_text_type(model_string, textType)
        if not assert_result:
            printUtil.printCaseDevice("根模块中未找到 {} 类型的文本".format(textType))
            self.assertTrue(False, "根模块中未找到 {} 类型的文本".format(textType))
            return
        self.assertTrue(True, "根模块中找到 {} 类型的文本".format(textType))

    def assertTextTypeNotInModel(self, textType, rootElement):
        """
        Assert the text type of the input text in the root element.
        :param text_type: The text type to be asserted. Supported text types: "number", "english", "chinese", "special_characters".
        :param rootElement: The root element.
        :return: None
        :throws: AssertionError if the text type of the input text is contained in the root element.
        """
        model_string = self.get_model_text(rootElement)
        assert_result = assert_text_type(model_string, textType)
        if assert_result:
            printUtil.printCaseDevice("根模块中找到了 {} 类型的文本".format(textType))
            self.assertTrue(False, "根模块中找到了 {} 类型的文本".format(textType))
            return
        self.assertTrue(True, "根模块未找到 {} 类型的文本".format(textType))

    def assertTextNotInModel(self, text, rootElement,desc = ''):
        """
        Assert the input text is not contained in the root element.
        :param text: The text to be asserted.
        :param rootElement: The root element.
        :return: None
        """
        model_string = self.get_model_text(rootElement)
        if text not in model_string:
            printUtil.printCaseDevice("根模块中未找到 {} 字符串".format(text))
            self.assertTrue(True, "根模块中未找到 {} 字符串".format(text))
        self.assertTrue(False, "根模块中找到 {} 字符串,bdd为 {} ".format(text,desc))

    def assert_image_equal(self, baseUrl, element, description="图片比对"):
        """
        图片比对
        :param baseUrl: 基准图片url
        :param element: 目标控件
        :param description: 描述
        :return:
        """
        if element is None:
            printUtil.printCaseDevice("图片比对失败：目标控件为空")
            self.assertTrue(False, description)
            return
        targetUrl = Capture.screenCropImageUrlByNode(element.first)
        res = AiAgentGenerate.compareImage(baseUrl, targetUrl)
        self.assertTrue(res, description)

    def assertReqContains(self, b, name, msg = '', type = 0):
        """
        断言请求包含
        :param b: b
        :param name: 请求名称
        :param msg: 描述
        :param type: 类型
        :return:
        """
        if type == 0:
            printUtil.printCaseDevice(f"{name}请求断言")
            with self.page.expect_request(name) as request_info:
                request = request_info.value
                if request == '':
                    message = '未拿到服务的请求数据'
                    CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                else:
                    if str(b) in request:
                        message = msg + "  期望结果：[" + str(b) + "] 包含在请求参数中"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, b, "", message)
                    else:
                        message = msg + "  期望结果：[" + str(b) + "] 不包含在请求参数中"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, b, "", message)
                    printUtil.printCaseDevice('断言:' + str(message))
                self.assertTrue(str(b) in request, msg)

        else:
            printUtil.printCaseDevice(f"{name}响应断言")
            with self.page.expect_response(name) as response_info:
                response = response_info.value
                pass

    def assertReqNotContains(self, b, name, msg = '', type = 0):
        """
        断言请求不包含
        :param b: b
        :param name: 请求名称
        :param msg: 描述
        :param type: 类型
        :return:
        """
        if type == 0:
            printUtil.printCaseDevice(f"{name}请求断言")
            with self.page.expect_request(name) as request_info:
                request = request_info.value
                if request == '':
                    message = '未拿到服务的请求数据'
                    CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                else:
                    if str(b) not in request:
                        message = msg + "  期望结果：[" + str(b) + "] 不包含在请求参数中"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, b, "", message)
                    else:
                        message = msg + "  期望结果：[" + str(b) + "] 包含在请求参数中"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, b, "", message)
                    printUtil.printCaseDevice('断言:' + str(message))
                self.assertTrue(str(b) not in request, msg)

        else:
            printUtil.printCaseDevice(f"{name}响应断言")
            with self.page.expect_response(name) as response_info:
                response = response_info.value
                pass

    def assertReqEqual(self, jPath, b, name, msg = '', type = 0):
        """
        断言请求相等
        :param jPath: jPath
        :param b: b
        :param name: 请求名称
        :param msg: 描述
        :param type: 类型
        """
        if type == 0:
            printUtil.printCaseDevice(f"{msg}请求断言")
            a = None
            if self.route_list == []:
                message = '未拿到服务的请求数据'
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                return
            for request in self.route_list:
                if msg in request.get('url'):
                    a = self.getJsPath(request.get('request').post_data, jPath)
                    if a == b:
                        message = msg + "  期望结果：[" + str(b) + "] 实际结果：[" + str(a) + "]等于[" + str(b) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
                    else:
                        message = msg + "  期望结果：[" + str(b) + "]  实际结果：[" + str(a) + "]不等于[" + str(b) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
                    printUtil.printCaseDevice('断言:' + str(message))
                    break
            self.assertTrue(a == b, msg)
        else:
            printUtil.printCaseDevice(f"{msg}响应断言")
            self.assertTrue(False, msg)

    def assertReqNotEqual(self, jPath, name, second, msg = '', type = 0):
        """
        断言请求不相等
        :param jPath: jPath
        :param name: 请求名称
        :param second: second
        :param msg: 描述
        :param type: 类型
        """
        if type == 0:
            printUtil.printCaseDevice(f"{msg}请求断言")
            first = None
            if self.route_list == []:
                message = '未拿到服务的请求数据'
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                return
            for request in self.route_list:
                if msg in request.get('url'):
                    first = self.getJsPath(request.get('request').post_data, jPath)
                    if not first != second:
                        message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(
                            first) + "]等于[" + str(second) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, first, second, message)
                    else:
                        message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(
                            first) + "]不等于[" + str(second) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, first, second, message)
                    printUtil.printCaseDevice('断言:' + str(message))
                    break
            self.assertTrue(first != second, msg)
        else:
            printUtil.printCaseDevice(f"{msg}响应断言")
            self.assertTrue(False, msg)

    def assertTraceValueContain(self, valuePath, value, msg='', traceKey='traceKey', caseName=None):
        """
        断言toast
        """
        message = "toast: " + value
        found = False
        for console_item in self.console:
            if value in str(console_item):
                found = True
                break
                
        if found:
            printUtil.printCaseDevice('toast断言:' + str(value))
            self.assertTrue(True, message)
        else:
            printUtil.printCaseDevice('toast断言:' + str(value))
            self.assertTrue(False, message)
        

    # 根据模块 ID 获取这个模块下所有控件 ID 对应的文本控件的文本, {key: [text1, text2]}
    def getModuleTextControl(self, moduleId):
        element_id_text_list_mapping, unnamed_text_list = CommonAction.extractTextControls(self.page, moduleId)
        # 不能使用printUtil.printCaseDevice替换，在ai生成服务中会解析这个字符串
        print(json.dumps({"id_mapping": element_id_text_list_mapping, "unnamed_text_list": unnamed_text_list},
                         ensure_ascii=False))
    
    def process_payload(self, dom_tree):
        """
        1. 如果payload有testid属性，则删除payload除了testid和text的所有属性
        2. 如果payload有data-exposure或者page-module或者data-testid属性并且没有testid属性，则删除payload除了data-exposure、page-module、data-testid、testid、text的所有属性
        3. 如果payload有text属性且部位'且没有testid、data-exposure、page-module、data-testid属性，则删除payload除了data-exposure、page-module、data-testid、testid、text的所有属性
        4. 如果都没有上述内容，则不处理payload
        """
        if not dom_tree or not isinstance(dom_tree, dict) or 'payload' not in dom_tree:
            return dom_tree

        payload = dom_tree['payload']
        payload.pop('rect', None)
        payload.pop('visible', None)
        if not payload:
            return dom_tree

        # 创建一个新的payload字典
        new_payload = {}
        
        # 情况1：有testid属性
        if 'testid' in payload:
            new_payload['testid'] = payload['testid']
            if 'text' in payload:
                new_payload['text'] = payload['text']

        else:
            # 如果没有testid，则删除payload除了data-exposure、page-module、data-testid、testid、text的所有属性
            for key in WebElementLocator.preserved_keys:
                if key in payload:
                    new_payload[key] = payload[key]
        
        # # 情况2：有data-exposure或page-module或data-testid属性，但没有testid
        # elif any(key in payload for key in ['data-exposure', 'page-module', 'data-testid']) and 'testid' not in payload:
        #     preserved_keys = ['data-exposure', 'page-module', 'data-testid', 'testid', 'text']
        #     for key in preserved_keys:
        #         if key in payload:
        #             new_payload[key] = payload[key]
        #
        # # 情况3：有非空text属性，但没有其他特殊属性
        # elif ('text' in payload and payload['text'] != '' and
        #     not any(key in payload for key in ['testid', 'data-exposure', 'page-module', 'data-testid'])):
        #     preserved_keys = ['data-exposure', 'page-module', 'data-testid', 'testid', 'text']
        #     for key in preserved_keys:
        #         if key in payload:
        #             new_payload[key] = payload[key]
        
        # 更新dom_tree的payload
        if new_payload:
            dom_tree['payload'] = new_payload

        # 递归处理子节点
        if 'children' in dom_tree:
            for child in dom_tree['children']:
                self.process_payload(child)

        return dom_tree
    
    def ai_exec_get_dom_tree_and_page_screenshot(self):
        """ai执行，获取DOM树和页面截图"""
        try:
            # 1. 获取viewport
            viewport = self.page.viewport_size
            # 2. 获取当前页面最大高度，并且通过viewport判断是否可以滑动
            max_height = self.page.evaluate("document.body.scrollHeight")
            is_scrollable = max_height > viewport["height"]
            # 4. 截图
            screenshot = self.get_page_screenshot(full_page=False, scale="css")
            # 获取dom树
            self.dom_tree = self.get_dom_tree()
            # 绘制边界框
            img = self.draw_bounding_boxes(screenshot, self.dom_tree)
            timestamp = int(time.time())
            screenshot_path = self.save_image(img, f"screenshot_{timestamp}.png")
            # 将图像上传到服务器，获取图片url
            image_url = Capture.getImgOnlineUrl(self, screenshot_path, env="fws")
            json_data = {"base64_image": image_url, "viewport": viewport, "is_scrollable": is_scrollable}
            json_str = json.dumps(json_data, ensure_ascii=False)
            # 使用base64编码确保可以安全传输
            compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
            # 调用getBase64OnlineUrl方法上传这个额外数据
            image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
            print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
    
    def get_dom_tree_and_page_screenshot(self, scroll_page=False, ai_exec=False):
        """获取当前页面的DOM树结构和页面截图"""
        try:
            # ai执行，获取DOM树和页面截图
            if ai_exec: 
                self.ai_exec_get_dom_tree_and_page_screenshot()
            # 非ai执行，获取DOM树和页面截图
            else:  
                # 1. 获取DOM树
                dom_tree = self.get_dom_tree()
                # 2. 获取页面截图
                screenshot = self.get_page_screenshot(scale="css")
                # 不再绘制边界框，直接使用原始截图
                timestamp = int(time.time())
                # 保存原始截图，并进行压缩
                img = Image.open(BytesIO(screenshot))
                screenshot_path = self.save_compressed_image(img, f"screenshot_{timestamp}.png")
                
                # 将图像上传到服务器，获取图片url
                image_url = Capture.getImgOnlineUrl(self, screenshot_path, env="fws")
                
                # 将图像编码为base64
                # base64_image = self.encode_image_to_base64(screenshot_path)
                
                # 删除dom_tree中的非使用属性
                dom_tree = self.process_payload(dom_tree)
                
                json_data = {"dom_tree": dom_tree, "base64_image": image_url}
                json_str = json.dumps(json_data, ensure_ascii=False)
                # 使用base64编码确保可以安全传输
                compressed_b64 = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
                
                # 调用getBase64OnlineUrl方法上传这个额外数据
                image_url = Capture.getBase64OnlineUrl(compressed_b64, f"compressed_data_{timestamp}.png", 'png')
                
                print(f"image_url:{image_url}")
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"获取DOM树和页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
    
    def get_dom_tree_and_page_screenshot1(self):
        """获取当前页面的DOM树结构和页面截图"""
        try:
            # 1. 获取DOM树
            try:
                printUtil.printCaseDevice("获取DOM树")
                dom_tree = self.get_dom_tree()
                printUtil.printCaseDevice(json.dumps(dom_tree, ensure_ascii=False))
            except Exception as e:
                import traceback
                printUtil.printCaseDevice(f"获取DOM树失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
                return
            
            # 2. 获取页面截图
            try:
                printUtil.printCaseDevice("获取页面截图")
                screenshot = self.get_page_screenshot(scale="css")
                # 打印出图片的base64
                printUtil.printCaseDevice(base64.b64encode(screenshot).decode('utf-8'))
            except Exception as e:
                import traceback
                printUtil.printCaseDevice(f"获取页面截图失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
                return
            
            # 不再绘制边界框，直接使用原始截图
            timestamp = int(time.time())
            
            # 保存原始截图
            try:
                printUtil.printCaseDevice("保存图像")
                screenshot_path = self.save_image(Image.open(BytesIO(screenshot)), f"screenshot_{timestamp}.png")
            except Exception as e:
                import traceback
                printUtil.printCaseDevice(f"保存图像失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
                return
            
            if not screenshot_path:
                printUtil.printCaseDevice("保存图像失败")
                return
            
            # 将图像上传到服务器
            try:
                printUtil.printCaseDevice("上传图像")
                image_url = Capture.getImgOnlineUrl(self, screenshot_path, f"screenshot_{timestamp}.png", 'png')
            except Exception as e:
                import traceback
                printUtil.printCaseDevice(f"上传图像失败: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
                return
            
            if not image_url:
                printUtil.printCaseDevice("上传图像失败")
                return
            
            result = {
                "dom_tree": dom_tree,
                "image_url": image_url
            }
            printUtil.printCaseDevice(json.dumps(result, ensure_ascii=False))
            
        except Exception as e:
            import traceback
            printUtil.printCaseDevice(f"执行过程中发生错误: {str(e)}\n堆栈跟踪:\n{traceback.format_exc()}")
    
    
    def get_dom_tree(self):
        """获取当前页面的DOM树结构"""
        # 使用JavaScript获取DOM树结构
        dom_tree = self.page.evaluate(r"""() => {
            const isVisibleElement = (node) => {
                if (node.nodeType !== 1) return false;
                const style = window.getComputedStyle(node);
                const rect = node.getBoundingClientRect();
                return true;
                return style.display !== 'none' && 
                       style.visibility !== 'hidden' && 
                       style.opacity !== '0' &&
                       rect.width > 0 && 
                       rect.height > 0;
            };

            const isInteractiveElement = (node) => {
                if (node.nodeType !== 1) return false;
                const interactiveTags = ['a', 'button', 'input', 'select', 'textarea'];
                return interactiveTags.includes(node.tagName.toLowerCase()) ||
                       node.onclick != null ||
                       node.getAttribute('role') === 'button';
            };

            const shouldSkipElement = (node) => {
                // 如果不是元素节点，不跳过
                if (node.nodeType !== 1) return false;
                
                const skipTags = ['script', 'style', 'noscript', 'iframe', 'meta', 'link', 'head'];
                return skipTags.includes(node.tagName.toLowerCase());
            };

            const processNode = (node, index = 0) => {
                // 跳过注释节点和无意义节点
                if (node.nodeType === 8 || shouldSkipElement(node)) return null;
                
                // 如果是文本节点且内容为空或只包含空白字符，则跳过
                if (node.nodeType === 3 && !node.textContent.trim()) {
                    return null;
                }

                const result = {
                    index: index,
                    tag: node.nodeType === 1 ? node.tagName.toLowerCase() : '#text',
                    type: node.nodeType === 3 ? 'text' : 'element'
                };

                // 处理文本节点
                if (node.nodeType === 3) {
                    const text = node.textContent.trim();
                    if (text) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload = { text: cleanedText };
                    }
                    return result;
                }

                // 处理元素节点
                if (node.nodeType === 1) {
                    const isVisible = isVisibleElement(node);
                    // 如果元素不可见且不是交互式元素，则跳过
                    // if (!isVisible && !isInteractiveElement(node)) {
                    //     return null;
                    // }

                    result.payload = {};
                    
                    // 只保存长度不超过20个字符的文本内容
                    const text = node.textContent.trim();
                    if (text && text.length <= 20) {
                        // 移除特殊图标字符，包括"󰁮"（Unicode编码为0xF006E）
                        let cleanedText = text.replace(/[\u{E000}-\u{F8FF}]|[\u{1F000}-\u{1FFFF}]|[\u{2000}-\u{2FFF}]|[\u{F0000}-\u{FFFFF}]/gu, '');
                        // 直接替换特定的图标字符"󰁮"
                        cleanedText = cleanedText.replace(/\udbf0\udc6e/g, '');
                        // 只保留可打印的ASCII字符、空格和常见的中文字符
                        cleanedText = cleanedText.replace(/[^\x20-\x7E\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\s]/g, '');
                        result.payload.text = cleanedText;
                    }

                    // 只保存重要属性，且内容长度不超过50个字符
                    const importantAttrs = ['id', 'class', 'name', 'type', 'role', 'aria-label', 'placeholder', 'href', 'src', 'value', 'title', 'for'];
                    for (let attr of node.attributes) {
                        // 优先处理testid、page-module、data-exposure、data-testid等属性
                        // data-exposure属性是一个json字符串，需要提取ubtKey字段，保存到 payload['data-exposure']属性
                        // testid属性如果只是字符串不是json字符串，直接提取，保存到 payload.testid属性
                        // testid属性如果是json字符串，提取referConfig.oid字段，保存到 payload.testid属性
                        // page-module属性如果只是字符串不是json字符串，直接提取，保存到 payload['page-module']属性
                        // data-testid属性如果只是字符串不是json字符串，直接提取，保存到 payload['data-testid']属性
                        if (attr.name === 'data-exposure') {
                            try {
                                result.payload['data-exposure'] = JSON.parse(attr.value).ubtKey;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-exposure'] = attr.value;
                            }
                        }
                        if (attr.name === 'testid') {
                            try {
                                if (attr.value.includes('referConfig') && attr.value.includes('oid')) {
                                    result.payload.testid = JSON.parse(attr.value).referConfig.oid;
                                } else if (attr.value.includes('viewID')) {
                                    result.payload.testid = JSON.parse(attr.value).viewID;
                                } else {
                                    result.payload.testid = attr.value;
                                }
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload.testid = attr.value;
                            }
                        }
                        if (attr.name === 'page-module') {
                            try {
                                result.payload['page-module'] = JSON.parse(attr.value).moduleId;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['page-module'] = attr.value;
                            }
                        }
                        if (attr.name === 'data-testid') {
                            try {
                                result.payload['data-testid'] = JSON.parse(attr.value).referConfig.oid;
                            } catch (e) {
                                // 如果解析失败，直接使用原始值
                                result.payload['data-testid'] = attr.value;
                            }
                        }
                        if (importantAttrs.includes(attr.name) && attr.value) {
                            // 检查属性值长度是否超过50个字符
                            if (attr.value.length <= 50) {
                                // 直接使用原始属性值，不做任何修改
                                result.payload[attr.name] = attr.value;
                            }
                        }
                    }

                    // 获取元素位置和大小（只对可见元素）
                    if (true) {
                        const rect = node.getBoundingClientRect();
                        result.payload.rect = {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        };
                    }

                    result.payload.visible = true;

                    // 处理子节点
                    if (node.childNodes && node.childNodes.length > 0) {
                        const children = [];
                        let childIndex = 0;
                        for (const childNode of node.childNodes) {
                            const childResult = processNode(childNode, childIndex);
                            if (childResult) {
                                children.push(childResult);
                                childIndex++;
                            }
                        }
                        if (children.length > 0) {
                            result.children = children;
                        }
                    }
                }

                return result;
            };

            // 从body开始处理整个DOM树
            return processNode(document.body);
        }""")
        
        # 添加全局顺序索引
        self._add_sequential_index(dom_tree)
        
        return dom_tree
    
    def _add_sequential_index(self, node, index_map=None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}
        
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1
        
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)
    
    def get_page_screenshot(self, full_page=True, scale="css"):
        """获取页面截图"""
        return self.page.screenshot(full_page=full_page, scale=scale)

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)
    
    def draw_bounding_boxes(self, screenshot_bytes, dom_tree):
        """在截图上绘制边界框"""
        # 将截图字节转换为PIL图像
        img = Image.open(BytesIO(screenshot_bytes))
        draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度

        # 获取页面大小
        viewport_size = self.page.viewport_size
        width = viewport_size["width"]
        height = viewport_size["height"]

        # 尝试加载字体
        font = ImageFont.truetype("Arial", 14)

        # 重置随机种子，确保颜色生成的一致性
        random.seed(42)

        # 递归绘制边界框
        self._draw_node_bounding_box(draw, dom_tree, width, height, font)

        return img
    
    def _draw_node_bounding_box(self, draw, node, viewport_width, viewport_height, font):
        """递归地为每个节点绘制边界框"""
        if "payload" in node and "rect" in node["payload"]:
            rect = node["payload"]["rect"]

            # 使用rect中的坐标和大小信息
            x1 = rect["x"]
            y1 = rect["y"]
            x2 = x1 + rect["width"]
            y2 = y1 + rect["height"]

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 构建标签文本 - 包含seq_index和文本内容（如果有且不太长）
            label_text = str(seq_index)

            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > viewport_width:
                        label_x = viewport_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > viewport_height:
                            label_y = viewport_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    print(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, viewport_width, viewport_height, font)
    
    def save_image(self, image, filename):
        """保存图像到文件"""
        # 判断是否有output目录，没有的话则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        filepath = os.path.join("output", filename)
        image.save(filepath)
        return filepath
    
    def encode_image_to_base64(self, image_path):
        """将图像编码为base64字符串"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
        
    def find_center_from_dom_tree_by_seqIndex(self, seq_index):
        """根据seq_index递归查找对应的pos"""
        # 检查当前节点是否匹配目标seq_index
        # 递归遍历子节点
        return self.find_element_by_seq_index(self.dom_tree, seq_index)
    
    def find_element_by_seq_index(self, dom_tree, target_seq_index):
        """根据序列索引在DOM树中查找元素"""
        # 添加空值检查
        if not dom_tree or not isinstance(dom_tree, dict):
            return None
            
        if dom_tree.get("seq_index") == target_seq_index:
            # 添加payload和rect存在性检查
            if "payload" in dom_tree and "rect" in dom_tree["payload"]:
                rect = dom_tree["payload"]["rect"]
                return {
                    "x": rect["x"] + rect["width"] / 2,
                    "y": rect["y"] + rect["height"] / 2
                }
        
        if dom_tree.get("children"):
            for child in dom_tree.get("children"):
                result = self.find_element_by_seq_index(child, target_seq_index)
                if result:
                    # 直接返回结果，不再尝试获取payload和rect
                    return result
        
        return None
    
    def _build_selector(self, element):
        """构建元素选择器"""
        selectors = []
        
        # 尝试使用id
        if element["payload"].get("id"):
            selectors.append(f"#{element['payload']['id']}")
        
        # 尝试使用testid
        if element["payload"].get("testid"):
            selectors.append(f"[data-testid='{element['payload']['testid']}']")
        
        # 尝试使用精确文本
        if element["payload"].get("text"):
            selectors.append(f"text='{element['payload']['text']}'")
        
        # 如果都没有，使用tag和class组合
        if not selectors and element.get("tag"):
            selector = element["tag"]
            if element["payload"].get("class"):
                selector += f".{element['payload']['class'].replace(' ', '.')}"
            selectors.append(selector)
        
        # 返回第一个有效的选择器
        return selectors[0] if selectors else "body"

    def save_compressed_image(self, img, filename, compression_ratio=0.5):
        """
        保存压缩后的图片，压缩到原始大小的指定比例
        
        Args:
            img: PIL Image对象
            filename: 保存的文件名
            compression_ratio: 压缩比例（默认0.5，即压缩到原始大小的50%）
            
        Returns:
            保存的图片路径
        """
        # 获取保存路径，如果output/目录不存在，则创建
        if not os.path.exists("output"):
            os.makedirs("output")
        save_path = os.path.join("output", filename)
        
        # 创建临时内存文件以获取原始大小
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=95)
        original_size = temp_buffer.tell()
        
        # 计算目标大小
        target_size = int(original_size * compression_ratio)
        
        # 初始质量设置
        quality = 85
        
        # 如果只通过降低质量就能达到目标，则使用此方法
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality, optimize=True)
        compressed_size = temp_buffer.tell()
        
        # 如果压缩后大小仍然大于目标大小，则同时缩小尺寸
        if compressed_size > target_size:
            # 计算需要的尺寸缩放比例（假设文件大小与像素数近似成正比）
            # 我们需要额外的缩放因子来达到目标大小
            scale_factor = math.sqrt(target_size / compressed_size)
            width, height = img.size
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            # 缩小图片
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
            resized_img.save(save_path, format='JPEG', quality=quality, optimize=True)
        else:
            # 如果仅降低质量就能达到目标，直接保存
            img.save(save_path, format='JPEG', quality=quality, optimize=True)
            
        return save_path