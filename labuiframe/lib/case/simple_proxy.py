from poco.proxy import UIObjectProxy
from poco.utils.query_util import build_query
import json
import ast
import poco.utils.six as six
import poco.utils.query_util as query_util
from typing import Union
class SimpleProxy(UIObjectProxy):
    def __init__(self, poco, key: Union[str,list[Union[str,int]]], isViewId = False):
        super().__init__(poco)
        self.key = key
        # 看看是否可以解析为json
        try:
            # 如果有{}
            if (key.startswith("{") and key.endswith("}")) or (key.startswith("[") and key.endswith("]")):
                key = key.replace('\\"', '"')
                try:
                    # 尝试使用标准json解析
                    key = json.loads(key)
                except json.JSONDecodeError:
                    try:
                        # 如果包含转义的单引号，尝试使用ast.literal_eval解析
                        # 例如处理 "{\'text\': \'Trip.com\'}" 这样的情况
                        key = ast.literal_eval(key)
                    except (SyntaxError, ValueError):
                        # 如果ast也解析失败，使用最原始的方法作为备选
                        if isinstance(key, str):
                            self.query = self._build_query(key, isViewId)
                        else:
                            self.query = self._build_query_for_key_path(key)
                        return
                if 'name' in key:
                    name_value = key.pop('name')
                    self.query = build_query(name_value, **key)
                else:
                    self.query = build_query(None, **key)
            else:
                if isinstance(key,str):
                    self.query = self._build_query(key, isViewId)
                else:
                    self.query = self._build_query_for_key_path(key)
        except Exception as e:
            if isinstance(key,str):
                self.query = self._build_query(key, isViewId)
            else:
                self.query = self._build_query_for_key_path(key)


    def _build_query(self,key, isViewId = False):
        query = []
        equalOp = 'attr='
        matchOp = 'attr.*='
        descAttr = 'desc'
        nameAttr = 'name'
        textAttr = 'text'
        query.append((equalOp,(nameAttr,key)))
        query.append((equalOp,(textAttr,key)))
        query.append((equalOp,(descAttr,key)))
        # viewid不使用模糊匹配
        # if (not isViewId) and ((not key.isascii()) or len(key) > 2):
        if (not isViewId):
            # 先将key里面的([\s\S]*)转换成别的防止被转义掉
            key = key.replace("([\s\S]*)","tempMatches")
            key = key.replace('*','\*').replace('?','\?').replace('+','\+').replace('.','\.').replace('$','\$').replace('(','\(').replace(')','\)')
            #校验中去除首尾的空格
            key = key.strip().replace("tempMatches",'([\s\S]*)').replace(" ","([ | ])")
            matchKey = f'([\s\S]*){key}([\s\S]*)'
            query.append((matchOp,(nameAttr,matchKey)))
            query.append((matchOp,(textAttr,matchKey)))
            query.append((matchOp,(descAttr, matchKey)))
        return ('or',tuple(query))

    def _build_query_for_key_path(self, key_path):
        if not key_path or not isinstance(key_path[0],str):
            raise TypeError("the first key must be str")
        query = self._build_query(key_path[0])
        for key in key_path[1:]:
            if isinstance(key,str):
                query = ('>', (query, self._build_query(key)))
            elif isinstance(key, int):
                query = ('index', (query, key))
            else:
                raise TypeError("key must be either str or int")
        return query
    
if __name__ == "__main__":
    proxy = SimpleProxy("", '{\"name\": \"android.view.ViewGroup\", \"boundsInParent\": [1.0, 0.0625]}')
   