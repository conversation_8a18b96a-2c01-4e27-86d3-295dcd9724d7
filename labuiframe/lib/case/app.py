# -*- coding: utf-8 -*-
import jsonpath
from airtest.report import report

from labuiframe.lib.utils.HandleInfo import HandleInfo

__author__ = "tingxiao"

from pocounit.result import PocoTestResult

from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.commonAction import CommonAction
from labuiframe.lib.utils.device import DeviceInfo

from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.capture import Capture
from labuiframe.lib.utils.LabuiframeTestCase import LabuiframeTestCase
from labuiframe.lib.utils.mockApi import mockApi
from labuiframe.lib.utils.printUtil import printUtil
from labuiframe.lib.flutter.flutter_driver import FlutterDriver
from airtest.core.api import device as current_device
from airtest.core.android.adb import ADB
import re
import sys
import traceback
import time
import threading
import random
import string
import base64
from CtestCommon.StartStage.RNConfig import *


class AppTestCase(LabuiframeTestCase):
    caseResult = PocoTestResult()
    mySqlConnect = MySqlConnect()
    casename = ""
    package_name = ""
    start_time = 0
    end_time = 0

    def __init__(self, methodName='runTest'):
        LabuiframeTestCase.__init__(self)
        self.caseResult = LabuiframeTestCase()
        Labconfig.config(self.project_root)  # load labconfig.ini
        Labconfig.set_config_data(Labconfig.get_config_file_path())
        LabuiframeTestCase.initScreenRecorder(self)  # init screenRecorder
        self.cmt_capture = Capture()  # 初始化截图类
        self.cmt_capture.setCase(self)
        # 记录特殊元素position和size信息
        self.specialElement = {
            "bottom_min_element": {},  # 底部最小浮层元素（系统导航栏）
            "bottom_max_element": {},  # 底部最大浮层元素
            "top_min_element": {},  # 顶部最小浮层元素
            "top_max_element": {},  # 顶部最大浮层元素
            "is_init": True  # 是否需要初始化
        }


    @classmethod
    def setUpClass(cls):
        """
        改写此方法来自定义appCase初始化

        :return:
        """
        cls.start_time = int(round(time.time() * 1000))
        super(AppTestCase, cls).setUpClass()
        cls.delReportDirs(cls)
        cls.delCaseVideo(cls)
        try:
            cls.clear_mthemis_log(cls)
            cls.clear_firewall_log(cls)
        except Exception as e:
            print(e)

    @classmethod
    def printCaseDevice(self, *printMessage):
        printUtil.printCaseDevice(" ".join(printMessage))

    def delReportDirs(self):
        self.printCaseDevice("********del reportDirs**************")
        log_path = self._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + self.__name__
        try:
            if os.path.exists(log_path):
                shutil.rmtree(log_path)
        except Exception as e:
            print("Unable to delete report dirs. %s" % e)

    def delCaseVideo(self):
        if DeviceInfo.getPlatform().lower() == "android":
            print("[{}]********del Vedio**************".format(time.strftime("%H:%M:%S"), time.localtime(time.time())))
            print("******device:" + DeviceInfo.getDeviceName())
            file_path = os.path.join(self._resule_collector.root, 'screen-{}.mp4'.format(device().uuid))
            print(file_path)
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print("Unable to delete vedio. %s" % e)

    def clear_firewall_log(self):
        dev = current_device()
        adb = ADB(serialno=dev.serialno)
        try:
            print("删除防火墙日志，执行命令: rm {}".format(Labconfig.firewall_log_path))
            adb.shell(['rm', Labconfig.firewall_log_path])
        except Exception as e:
            print(e)
            pass

    def clear_mthemis_log(self):
        dev = current_device()
        adb = ADB(serialno=dev.serialno)
        try:
            print("执行命令：cat /dev/null > {}".format(Labconfig.mthemis_log_path))
            adb.shell(
                ['cat', '/dev/null', '>', Labconfig.get_mthemis_log_path()])
        except Exception as e:
            print(e)
            pass

    @classmethod
    def pull_mthemis_log(cls):
        # 如果res目录不存在，需要创建该目录
        if not os.path.exists(Labconfig.getRootPath() + os.sep + "res"):
            os.makedirs(Labconfig.getRootPath() + os.sep + "res")
        local_log_path = Labconfig.getRootPath() + os.sep + "res" + os.sep + "error.log"
        dev = current_device()
        adb = ADB(serialno=dev.serialno)
        try:
            pull_command = "pull {} {}".format(Labconfig.get_mthemis_log_path(), local_log_path)
            print("run command: {}".format(pull_command))
            adb.cmd(pull_command)
        except Exception as e:
            return None
        if os.path.exists(local_log_path):
            return local_log_path
        else:
            return None

    @classmethod
    def pull_firewall_log(cls):
        # 如果res目录不存在，需要创建该目录
        if not os.path.exists(Labconfig.getRootPath() + os.sep + "res"):
            os.makedirs(Labconfig.getRootPath() + os.sep + "res")
        local_log_path = Labconfig.getRootPath() + os.sep + "res" + os.sep + "firewall_log.txt"
        dev = current_device()
        adb = ADB(serialno=dev.serialno)
        try:
            pull_command = "pull {} {}".format(Labconfig.get_firewall_log_path(), local_log_path)
            print("run command: {}".format(pull_command))
            adb.cmd(pull_command)
        except Exception as e:
            return None
        if os.path.exists(local_log_path):
            return local_log_path
        else:
            return None

    @classmethod
    def isDebug(cls):
        return True if sys.gettrace() else False

    @classmethod
    def tearDownClass(cls, poco=None):
        FlutterDriver.logcat_end()
        if Labconfig.getIsStopApp() == "True":
            cls.printCaseDevice("开始退出APP")
            stop_app(cls.package_name)
            cls.printCaseDevice("成功退出APP")

        if Labconfig.getIsClearApp() == "True" and DeviceInfo.getPlatform().lower() == 'android':
            cls.printCaseDevice("开始清除应用缓存")
            clear_app(cls.package_name)
            cls.printCaseDevice("成功清除应用缓存")

        # push video and send case result
        screen_record = cls.get_result_emitter("screenRecorder")
        CmtLogger.uploadCaseVideo(screen_record)
        try:
            cls.printCaseDevice("开始生成报告")
            cls.getReport(cls)
            HandleInfo.setRequest(cls, '')
            HandleInfo.currentCirculate = 1
        except Exception as e:
            cls.printCaseDevice("生成报告异常%s" % traceback.format_exc())
        else:
            upload_log_dir = cls._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + cls.__name__
            if CmtLogger.case_result_local:
                upload_log_dir += os.sep + CmtLogger.case_result_local
            if os.path.exists(upload_log_dir + os.sep + "export/log.log"):
                t = threading.Thread(target=CmtLogger.uplodeReport, args=(upload_log_dir,))
                t.start()

    def getReport(self):
        # export_log = self._resule_collector.root + os.sep + "export"
        script_root = self._resule_collector.testcase_dir + os.sep + self.__name__ + ".py"
        # airPath = self._resule_collector.root + os.sep + self.__name__ + ".air"

        log_dir = self._resule_collector.project_root + os.sep + "pocounit-results" + os.sep + self.__name__
        if CmtLogger.case_result_local:
            log_dir += os.sep + CmtLogger.case_result_local

        currentMockKey = "mockKey=" + HandleInfo.getCurrentMockKey()
        airPath = log_dir + os.sep + self.__name__ + ".air--" + currentMockKey

        shutil.copytree(self._resule_collector.root, log_dir)
        export_log = log_dir + os.sep + "export"

        # 创建air文件
        try:
            os.makedirs(airPath)
            if not os.path.isfile(airPath + os.sep + "__init__.py"):
                fd = open(airPath + os.sep + "__init__.py", mode="w", encoding="utf-8")
                fd.close()
            shutil.copy(script_root, airPath + os.sep + self.__name__ + ".py--" + currentMockKey)
        except IOError as e:
            self.printCaseDevice("Unable to copy file. %s" % e)

        # 复制视频到log中
        if Labconfig.get_upload_video() == "True":
            screen_record = self.get_result_emitter("screenRecorder")
            try:
                if DeviceInfo.getDeviceName() != '':
                    file_path = screen_record.record_filepaths[DeviceInfo.getDeviceName()].replace(self._resule_collector.root, log_dir)
                else:
                    file_path = screen_record.record_filepaths[device().uuid].replace(self._resule_collector.root, log_dir)
            except Exception as e:
                print("No screenRecorder file. %s" % e)
            try:
                # shutil.copy(file_path, self._resule_collector.root + os.sep + "log" + os.sep + file_path.split(os.sep)[-1])
                video_path = log_dir + os.sep + "log"
                if not os.path.isdir(video_path):
                    os.makedirs(video_path)
                shutil.copy(file_path, video_path + os.sep + file_path.split(os.sep)[-1])
            except Exception as e:
                print("Unable to copy .mp4 file. %s" % e)

        # 修改log中截图的绝对路径
        logBody = ""
        # with open(self._resule_collector.root + os.sep + "log" + os.sep + "log.txt",'r') as object:
        with open(log_dir + os.sep + "log" + os.sep + "log.txt", 'r') as object:
            logBody = object.read()
            a = "screenshot_.*\.jpg\""
            pattern = re.compile(a)
            list = pattern.findall(logBody)
            for item in list:
                item = item.split(",")[0].replace("\"", "")
                imgPath = self._resule_collector.project_root + os.sep + 'screenshot' + os.sep + item
                if os.path.exists(imgPath):
                    # shutil.copy(imgPath, self._resule_collector.root + os.sep + "log" + os.sep + item)
                    shutil.copy(imgPath, log_dir + os.sep + "log" + os.sep + item)
                    imgPath = imgPath.replace("\\", "\\\\")
                    logBody = logBody.replace(imgPath, item)
            del list
            object.close()

        # with open(self._resule_collector.root + os.sep + "log" + os.sep + "log.txt", 'w') as object:
        with open(log_dir + os.sep + "log" + os.sep + "log.txt", 'w') as object:
            object.write(logBody)
            object.close()

        # 生成报告
        # if Labconfig.get_ctaskid() > 0:
        #    rpt = report.LogToHtml(script_root=airPath, log_root=self._resule_collector.root + os.sep + "log", export_dir=export_log, lang='zh', static_root="http://mobile.test.ctripcorp.com/static/airtest")
        rpt = report.LogToHtml(script_root=airPath, log_root=log_dir + os.sep + "log", export_dir=export_log, lang='zh', static_root="https://pages.release.ctripcorp.com/wireless_docs/mpaasdocs-system/ReportStatic/")
        # else:
        #     rpt = report.LogToHtml(script_root=airPath, log_root=self._resule_collector.root + os.sep + "log", export_dir=export_log, lang='zh')

        try:
            rpt.report()
        except Exception as e:
            exc_type, exc_value, exc_traceback_obj = sys.exc_info()
            traceback.print_tb(exc_traceback_obj, limit=None, file=sys.stdout)
            raise Exception(exc_traceback_obj)

        # 修改报告中的代码绝对路径
        os.rename(export_log + os.sep + self.__name__ + ".log", export_log + os.sep + "log.log")
        if os.path.exists(export_log + os.sep + "log.log" + os.sep + "log.html"):
            html = ""
            with open(export_log + os.sep + "log.log" + os.sep + "log.html", "r", encoding='UTF-8') as object:
                html = object.read()
                path = export_log + os.sep
                html = html.replace(path, "")
                path = path.replace("\\", "\\\\")
                html = html.replace(path, "")
                object.close()

            with open(export_log + os.sep + "log.log" + os.sep + "log.html", "w", encoding='UTF-8') as object:
                object.write(html)
                object.close()

    def run(self, result=None):
        result = result or self.defaultTestResult()
        # CmtLogger.logStep("****************aaxxx*****************")
        if isinstance(result, PocoTestResult):
            self.caseResult = super(AppTestCase, self).run(result)

            # send back case result
            # CmtLogger.logStep("*******Result:"+str(self.caseResult))
            # print("*******ResultDetail:"+str(self.caseResult.detail_errors))
            # 增加case错误信息上报
            message = ""
            failType = ""
            if len(self.caseResult.detail_errors) > 0:
                try:
                    if "eventlet.timeout.Timeout" in str(self.caseResult.detail_errors):
                        message = "用例耗时超出{}秒".format(Labconfig.final_timeout)
                        failType = CmtLogger.case_overtime_error
                        try:
                            raise RuntimeError(self.caseResult.detail_errors)
                        except RuntimeError as e:
                            log(e, desc="Run Fail", snapshot=True)
                    else:
                        detail_error = self.caseResult.detail_errors[0][2]
                        # 页面未进入场景错误信息记录
                        if "crash" in str(detail_error):
                            message = CmtLogger.app_crash_message_error
                            failType = CmtLogger.app_crash_fail_type_error
                        elif "红屏,无需重试" in str(detail_error):
                            message = CmtLogger.page_message_error
                            failType = CmtLogger.code_bug_fail_type_error
                        elif "黑屏,无需重试" in str(detail_error):
                            message = CmtLogger.page_message_error2
                            failType = CmtLogger.code_bug_fail_type_error
                        elif CmtLogger.net_message_error in str(detail_error):
                            message = CmtLogger.net_message_error
                            failType = CmtLogger.net_fail_type_error
                        elif CmtLogger.mock_message_error in str(detail_error):
                            message = CmtLogger.mock_message_error
                            failType = CmtLogger.mock_type_business_change_error
                        # 元素未找到场景错误信息记录
                        elif "Cannot find any visible node" in str(detail_error):
                            message = str(detail_error)[str(detail_error).find('"'):] + "-元素未找到"
                            failType = CmtLogger.UIcase_error_type_error
                        # 数组越界
                        elif "list index out of range" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # name 'xx' is not defined
                        elif "is not defined" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # MAXTOUCH does not support to_json 
                        elif "MAXTOUCH does not support to_json method" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # 'NoneType' object has no attribute 'exists'
                        elif ("object has no attribute" in str(detail_error)) or ("has no attribute" in str(detail_error)):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # missing 1 required positional argument:
                        elif ("missing" in str(detail_error)) & ("required positional argument" in str(detail_error)):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # takes 1 positional argument but 2 were given
                        elif (("takes" in str(detail_error)) & ("positional argument but" in str(detail_error)) & (
                                "were given" in str(detail_error))):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        # File not exist
                        elif "File not exist" in str(detail_error):
                            message = str(detail_error)
                            failType = CmtLogger.UIcase_error_type_error
                        elif "后端服务问题" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.Backend_service_error
                        elif "前端服务问题" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.Frontend_service_error
                        elif "支付失败" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.Pay_fail_error
                        elif "socket超时" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.socket_overtime_error
                        elif "APP问题" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.APP_error
                        elif "数据问题" in str(detail_error):
                            message=str(detail_error)
                            failType=CmtLogger.data_bds_error
                        # 页面超时未进入增加一种错误类型
                        elif CmtLogger.page_jump_message_error in str(detail_error):
                            message = CmtLogger.page_jump_message_error
                            failType = CmtLogger.ui_case_fail_type_error
                        elif "是否进入" in str(detail_error) or '页面进入' in str(detail_error):
                            print('AirTest_FailReason--页面未进入')
                            message = CmtLogger.page_check_message_error
                            failType = CmtLogger.ui_case_fail_type_error
                        # 断言失败场景错误信息记录
                        elif ("False is not true" in str(detail_error)) or ("True is not false" in str(detail_error)):
                            message = str(detail_error)[str(detail_error).find(':') + 1:] + "-验证失败"
                            failType = CmtLogger.ui_case_fail_type_error
                        else:
                            print('AirTest_FailReason--case执行中失败')
                            message = str(detail_error)
                            failType = CmtLogger.ui_case_fail_type_error
                        # log(detail_error,desc="Run Fail", snapshot=True)
                        try:
                            raise RuntimeError(detail_error)
                        except RuntimeError as e:
                            log(e, desc="Run Fail", snapshot=True)
                except:
                    print("未处理的错误信息：{}".format(str(self.caseResult.detail_errors)))
            self.end_time = int(round(time.time() * 1000))

            excute_time = int((self.end_time - self.start_time) / 1000)
            Labconfig.setTimeSplitToCase(
                excute_time - Labconfig.getTimeSplitToFramework() - Labconfig.getTimeSplitToHotelPage() - Labconfig.getTimeSplitToLoadPage())

            if len(self.caseResult.detail_errors) > 0:
                self.cmt_capture.getScreenShot()

            log_url = ""
            try:
                # 拉取mthemis log
                if DeviceInfo.getPlatform().lower() == 'android':
                    mthemis_log_path = self.pull_mthemis_log()
                    if mthemis_log_path is not None:
                        log_url = CmtLogger.upload_safeinfo_log("MTHEMIS", mthemis_log_path)

                    # 拉取防火墙日志firewall_log
                    firewall_log_path = self.pull_firewall_log()
                    if firewall_log_path is not None:
                        log_url = CmtLogger.upload_safeinfo_log("FIREWALL", firewall_log_path)
                elif DeviceInfo.getPlatform().lower() == 'ios':
                    print("ios任务跳过防火墙日志")
                newCaseResult = self.caseResult
                CmtLogger.writeBackCaseResult(newCaseResult, self.start_time, self.end_time, message, log_url, failType,
                                              HandleInfo.getCurrentClientid(), HandleInfo.getCurrentMockKey())
            except Exception as e:
                print(e)
            if Labconfig.getIsHotelWirelessAppGroup() == "True":
                HandleInfo.clearSocketMessageDict()
                HandleInfo.clearBdsData()
                self.logOut()
            return self.caseResult
        else:
            return result

    def logOut(self):
        if HandleInfo.logoutSelfUid != 1:
            return
        HandleInfo.logoutSelfUid = 0
        if DeviceInfo.is_android_run():

            dev = current_device()
            stop_app(self.package_name)
            printUtil.printCaseDevice("********自定义账号case需额外启动一次来退出自定义账号")
            logoutConfig = "{\"forceLogout\": true}" if Labconfig.getAppid() == "37" else "{\"loginOut\":{\"desc\":\"退出登录\",\"status\":1}}"
            config_str = str(base64.b64encode(logoutConfig.encode('utf-8')), 'utf-8')
            start_app_configenv(dev, self.package_name, "1", config_str)

            # if Labconfig.getAppid() == "37":
            #     logoutConfig = "{\"forceLogout\": true}"
            #     config_str = str(base64.b64encode(logoutConfig.encode('utf-8')), 'utf-8')
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName), '--es', 'hideDebug 1',
            #          '--es', 'configEnv %s' % (config_str)])
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName + '.alias'), '--es',
            #          'hideDebug 1',
            #          '--es',
            #          'configEnv %s' % (config_str)])
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName + '.alias2'), '--es',
            #          'hideDebug 1',
            #          '--es',
            #          'configEnv %s' % (config_str)])
            # else:
            #     logoutConfig = "{\"loginOut\":{\"desc\":\"退出登录\",\"status\":1}}"
            #     config_str = str(base64.b64encode(logoutConfig.encode('utf-8')), 'utf-8')
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (self.package_name, self.activityName), '--es',
            #          'hideDebug 1',
            #          '--es',
            #          'configEnv %s' % (config_str)])
            printUtil.printCaseDevice("退出登录config_str:{}".format(config_str))


    def getPackageName(self):
        try:
            package_name = os.environ['app']
            return package_name
        except Exception as e:
            print("Get APP Exception:", e)

    # log step 平台日志
    def logStep(self, detail):
        CmtLogger.logStep(detail)

    # assertions
    def assertTrue(self, expr, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        if isinstance(expr, (str)):
            message = msg + "  期望结果：[True]" + " 实际结果：[" + str(expr) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            self.printCaseDevice('断言:' + str(message))
            super(AppTestCase, self).assertTrue(False, message)
        else:
            expr = bool(expr)
            img = ""
            url = ""
            message = msg + "  期望结果：[True]" + " 实际结果：[" + str(expr) + "]"
            if not expr:
                # log_result_detail(False,"True",expr,img,url,msg)
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", expr, message)
                self.cmt_capture.getScreenShot()
                log(message, desc=msg, snapshot=True)
                if Labconfig.getIsLogJavis() == 'True':
                    self.mySqlConnect.setCoreCase(0, caseName)
            else:
                # log_result_detail(True, "True", expr, img, url, msg)
                CmtLogger.writeToDatabase(LogCategory.assert_pass, "True", expr, message)
                log(message, snapshot=True)
                if Labconfig.getIsLogJavis() == 'True':
                    self.mySqlConnect.setCoreCase(1, caseName)
            # os.system('echo 断言:' + str(message))
            self.printCaseDevice('断言:' + str(message))
            super(AppTestCase, self).assertTrue(expr, msg)

    def assertFalse(self, expr, msg='', caseName=None):
        expr = bool(expr)
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        message = msg + "  期望结果：[False]" + " 实际结果：[" + str(expr) + "]"
        if expr:
            # log_result_detail(False, "False", expr, img, url, msg)
            CmtLogger.writeToDatabase(LogCategory.assert_error, "False", expr, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
        else:
            # log_result_detail(True, "False", expr, img, url, msg)
            CmtLogger.writeToDatabase(LogCategory.assert_pass, "False", expr, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertFalse(expr, msg)

    def assertEqual(self, first, second, msg='', caseName=None):
        # print("Labconfig.getIsLogJavis():"+ Labconfig.getIsLogJavis())
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not (first == second or first.strip() == second.strip()):
            # log_result_detail(False, first, second, img, url, msg)
            message = msg + "  期望结果：[" + str(first) + "]等于[" + str(second) + "]  实际结果：[" + str(first) + "]不等于[" + str(second) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, first, second, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
        else:
            # log_result_detail(True, first, second, img, url, msg)
            message = msg + "  期望结果：[" + str(first) + "]等于[" + str(second) + "]  实际结果：[" + str(first) + "]等于[" + str(second) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, first, second, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertEqual(first.strip(), second.strip(), msg)

    def assertNotEqual(self, first, second, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not first != second:
            # log_result_detail(False, first, second, img, url, msg)
            message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(first) + "]等于[" + str(second) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, first, second, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
        else:
            # log_result_detail(True, first, second, img, url, msg)
            message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(first) + "]不等于[" + str(second) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, first, second, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertNotEqual(first, second, msg)

    def assertLess(self, a, b, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not a < b:
            # log_result_detail(False, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]小于[" + str(b) + "]  实际结果：[" + str(a) + "]不小于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
        else:
            # log_result_detail(True, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]小于[" + str(b) + "]  实际结果：[" + str(a) + "]小于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertLess(a, b, msg)

    def assertLessEqual(self, a, b, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not a <= b:
            # log_result_detail(False, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]小于等于[" + str(b) + "]  实际结果：[" + str(a) + "]大于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            self.cmt_capture.getScreenShot()
        else:
            # log_result_detail(True, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]小于等于[" + str(b) + "]  实际结果：[" + str(a) + "]小于等于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertLessEqual(a, b, msg)

    def assertGreater(self, a, b, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not a > b:
            # log_result_detail(False, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]大于[" + str(b) + "]  实际结果：[" + str(a) + "]小于等于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            self.cmt_capture.getScreenShot()
        else:
            # log_result_detail(True, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]大于[" + str(b) + "]  实际结果：[" + str(a) + "]大于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertGreater(a, b, msg)

    def assertGreaterEqual(self, a, b, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if not a >= b:
            # log_result_detail(False, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]大于等于[" + str(b) + "]  实际结果：[" + str(a) + "]小于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            self.cmt_capture.getScreenShot()
        else:
            # log_result_detail(True, a, b, img, url, msg)
            message = msg + "  期望结果：[" + str(a) + "]大于等于[" + str(b) + "]  实际结果：[" + str(a) + "]大于等于[" + str(b) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertGreaterEqual(a, b, msg)

    def assertIn(self, member, container, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if member not in container:
            # log_result_detail(False, member, container, img, url, msg)
            message = msg + "  期望结果：[" + str(container) + "]包含[" + str(member) + "]  实际结果：[" + str(container) + "]不包含[" + str(member) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, member, container, message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            self.cmt_capture.getScreenShot()
        else:
            # log_result_detail(True, member, container, img, url, msg)
            message = msg + "  期望结果：[" + str(container) + "]包含[" + str(member) + "]  实际结果：[" + str(container) + "]包含[" + str(member) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, member, container, message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertIn(member, container, msg)

    def assertNotIn(self, member, container, msg='', caseName=None):
        if caseName == None:
            caseName = self.casename
        img = ""
        url = ""
        if member in container:
            message = msg + "  期望结果：[" + str(container) + "]不包含[" + str(member) + "]  实际结果：[" + str(container) + "]包含[" + str(member) + "]"
            CmtLogger.writeToDatabase(LogCategory.assert_error, member, container, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            # if Labconfig.getIsLogJavis() == 'True':
            #     self.mySqlConnect.setCoreCase(0, caseName)
        else:
            message = msg + "  期望结果：[" + str(container) + "]与[" + str(member) + "]无包含关系  实际结果：[" + str(container) + "]与[" + str(member) + "]无包含关系"
            # if Labconfig.getIsLogJavis() == 'True':
            #     self.mySqlConnect.setCoreCase(1, caseName)
            log(message, snapshot=True)
            CmtLogger.writeToDatabase(LogCategory.assert_pass, member, container, message)
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))
        return super(AppTestCase, self).assertNotIn(member, container, msg)

    def assertReqLenth(self, type='list', expectlenth=0, msg="", caseName=None):
        self.printCaseDevice('check type is' + type)

        if caseName == None:
            caseName = self.casename
        reqLenth = HandleInfo.getRequestLenth()
        if reqLenth == 0:
            HandleInfo.getServiceRequest(type)
            reqLenth = HandleInfo.getRequestLenth()

        super(AppTestCase, self).assertTrue(reqLenth == expectlenth, msg)

    def assertReqEqual(self, jPath, b, msg='', type='list', caseName=None, reGet=0):
        self.printCaseDevice('check type is' + type)

        if caseName == None:
            caseName = self.casename
        req = self.getSocketMessageByKey(type, reGet)

        if req == '':
            message = '未拿到服务的请求数据'
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            a = None
        else:
            a = self.getJsPath(req, jPath)

            if a == b:
                # log_result_detail(True, a, b, img, url, msg)
                message = msg + "  期望结果：[" + str(b) + "] 实际结果：[" + str(a) + "]等于[" + str(b) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, a, b, message)
                log(message, snapshot=True)
            else:
                message = msg + "  期望结果：[" + str(b) + "]  实际结果：[" + str(a) + "]不等于[" + str(b) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_error, a, b, message)
                log(message, desc=msg, snapshot=True)
                self.cmt_capture.getScreenShot()
            # os.system('echo 断言:' + str(message))
            self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertTrue(a == b, msg)

    def assertReqNotEqual(self, jPath, second, msg='', type='list', caseName=None, reGet=0):
        self.printCaseDevice('check type is' + type)

        if caseName == None:
            caseName = self.casename
        req = self.getRequestBody(type, reGet)

        if req == '':
            message = '未拿到服务的请求数据'
            first = None
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
        else:
            first = self.getJsPath(req, jPath)
            if not first != second:
                # log_result_detail(False, first, second, img, url, msg)
                message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(first) + "]等于[" + str(second) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_error, first, second, message)
                self.cmt_capture.getScreenShot()
                log(message, desc=msg, snapshot=True)
                # if Labconfig.getIsLogJavis() == 'True':
                #     self.mySqlConnect.setCoreCase(0, caseName)
            else:
                # log_result_detail(True, first, second, img, url, msg)
                message = msg + "  期望结果：[" + str(first) + "]不等于[" + str(second) + "]  实际结果：[" + str(first) + "]不等于[" + str(second) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, first, second, message)
                log(message, snapshot=True)
                # if Labconfig.getIsLogJavis() == 'True':
                #     self.mySqlConnect.setCoreCase(1, caseName)
            # os.system('echo 断言:' + str(message))
            self.printCaseDevice('断言:' + str(message))
            super(AppTestCase, self).assertTrue(first != second, msg)

    def assertReqContains(self, b, msg='', type='list', caseName=None, reGet=0):
        self.printCaseDevice('check type is' + type)

        if caseName == None:
            caseName = self.casename
        req = self.getSocketMessageByKey(type, reGet)

        if req == '':
            message = '未拿到服务的请求数据'
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
        else:
            if str(b) in req:
                # log_result_detail(True, a, b, img, url, msg)
                message = msg + "  期望结果：[" + str(b) + "] 包含在请求参数中"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, b, "", message)
                log(message, snapshot=True)
            else:
                message = msg + "  期望结果：[" + str(b) + "] 不包含在请求参数中"
                CmtLogger.writeToDatabase(LogCategory.assert_error, b, "", message)
                log(message, desc=msg, snapshot=True)
                self.cmt_capture.getScreenShot()
            # os.system('echo 断言:' + str(message))
            self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertTrue(str(b) in req, msg)

    def assertReqNotContains(self, b, msg='', type='list', caseName=None, reGet=0):
        self.printCaseDevice('check type is' + type)

        if caseName == None:
            caseName = self.casename
        req = self.getSocketMessageByKey(type, reGet)

        if req == '':
            message = '未拿到服务的请求数据'
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
        else:
            if str(b) not in req:
                # log_result_detail(True, a, b, img, url, msg)
                message = msg + "  期望结果：[" + str(b) + "] 不包含在请求参数中"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, b, "", message)
                log(message, snapshot=True)
            else:
                message = msg + "  期望结果：[" + str(b) + "] 包含在请求参数中"
                CmtLogger.writeToDatabase(LogCategory.assert_error, b, "", message)
                log(message, desc=msg, snapshot=True)
                self.cmt_capture.getScreenShot()
            # os.system('echo 断言:' + str(message))
            self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertTrue(str(b) not in req, msg)


    def getRequestBody(self, type, reGet=0):
        if (reGet == 0):
            req = HandleInfo.getRequest()
            if req == "":
                HandleInfo.getServiceRequest(type)
                req = HandleInfo.getRequest()
        elif (reGet == 1):
            HandleInfo.getServiceRequest(type)
            req = HandleInfo.getRequest()
        return req

    def sendSocketMessage(cls, key, message):
        return HandleInfo.sendSocketMessage(key, message)

    # reGet=1对全部key进行更新
    def getSocketMessageByKey(self, type, reGet=0):
        if (reGet == 0):
            res = HandleInfo.getSocketMessageDict(type)
            if res == "":
                HandleInfo.getAllSocketMessage()
                res = HandleInfo.getSocketMessageDict(type)
        elif (reGet == 1):
            HandleInfo.getAllSocketMessage()
            res = HandleInfo.getSocketMessageDict(type)
        if res== '':
            print("未拿到服务接口{}的请求数据".format(type))
        return res

    def getRequestBodyWithClear(self, type):
        req = HandleInfo.getRequest()
        if req == "":
            HandleInfo.getServiceRequest(type)
            req = HandleInfo.getRequest()
            HandleInfo.clearRequest()
        return req

    def getFirstRequestBodyWithClear(self, type):
        req = HandleInfo.getFirstRequest()
        if req == "":
            HandleInfo.getServiceRequest(type)
            req = HandleInfo.getFirstRequest()
            HandleInfo.clearRequest()
        return req

    def getBookOrderInfo(self):
        return HandleInfo.getAutoOrderInfo()

    def getBookOrderInfoNewCombine(self):
        return HandleInfo.getAutoOrderInfoNewCombine()

    def getResponseBody(self, type, reGet=0):
        if (reGet == 0):
            res = HandleInfo.getResponse()
            if res == "":
                HandleInfo.getServiceResponse(type)
                res = HandleInfo.getResponse()
        elif (reGet == 1):
            HandleInfo.getServiceResponse(type)
            res = HandleInfo.getResponse()
        print("获得返回报文：{}".format(res))
        return res

    def getJsPath(self, req, jPath):
        try:
            # print("拿到的请求是：",req)
            req = json.loads(req)
            print("jsonpath", str(jsonpath.jsonpath(req, jPath)))
            res = jsonpath.jsonpath(req, jPath)
            if not res:
                return ""
            if len(res):
                return res[0]
            else:
                return ""
        except Exception as e:
            print('jsonpath-str(e):\t\t', str(e))
            print('jsonpath-repr(e):\t', repr(e))
            return ""

    # 埋点值检查
    def assertTraceValueEqual(self, valuePath, value, msg='', traceKey='traceKey', caseName=None):
        self.printCaseDevice('check traceKey is' + traceKey)

        if caseName == None:
            caseName = self.casename
        keyValue = HandleInfo.getCurrentTraceKey(traceKey, valuePath)

        if keyValue == '':
            message = '未获取到trace或者trace值未拿到'
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            a = None
        else:
            if keyValue == value:
                message = msg + "  期望结果：[" + str(value) + "]等于[" + str(keyValue) + "]  实际结果：[" + str(value) + "]等于[" + str(keyValue) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, value, keyValue, message)
                log(message, snapshot=True)
            else:
                message = msg + "  期望结果：[" + str(value) + "]等于[" + str(keyValue) + "]  实际结果：[" + str(value) + "]不等于[" + str(keyValue) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_error, value, keyValue, message)
                log(message, desc=msg, snapshot=True)
                self.cmt_capture.getScreenShot()

            self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertTrue(value == keyValue, msg)
        # 埋点值检查

    def assertTraceValueContain(self, valuePath, value, msg='', traceKey='traceKey', caseName=None):
        self.printCaseDevice('check traceKey is' + traceKey)

        if caseName == None:
            caseName = self.casename
        keyValue = HandleInfo.getCurrentTraceKey(traceKey, valuePath, value)

        if keyValue == '':
            message = '未获取到trace或者trace值未拿到'
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            self.cmt_capture.getScreenShot()
            log(message, desc=msg, snapshot=True)
            a = None
        else:
            if value in keyValue:
                message = msg + "  期望结果：[" + str(value) + "]包含[" + str(keyValue) + "]  实际结果：[" + str(value) + "]包含[" + str(keyValue) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_pass, value, keyValue, message)
                log(message, snapshot=True)
            else:
                message = msg + "  期望结果：[" + str(value) + "]包含[" + str(keyValue) + "]  实际结果：[" + str(keyValue) + "]不包含[" + str(value) + "]"
                CmtLogger.writeToDatabase(LogCategory.assert_error, value, keyValue, message)
                log(message, desc=msg, snapshot=True)
                self.cmt_capture.getScreenShot()

            self.printCaseDevice('断言:' + str(message))
        super(AppTestCase, self).assertTrue(value in keyValue, msg)

    def getTraceValue(self, traceKey='traceKey', caseName=None):
        self.printCaseDevice('check traceKey is ' + traceKey)

        if caseName == None:
            caseName = self.casename
        keyValue = HandleInfo.getCurrentTraceValue(traceKey)
        return keyValue

    # 图片断言
    def assertImgExists(self, filename, msg="", caseName=None, threshold=ST.THRESHOLD):
        if caseName == None:
            caseName = self.casename
        """
            Assert target exists on device screen

            :param filename: target to be checked
            :param msg: short description of assertion, it will be recorded in the report
            :return: coordinates of the target
        """
        pic = Capture.fullPicturePath(filename)
        logfile = CmtLogger.logImage(pic)
        try:
            pos = assert_exists(Template(pic, threshold=threshold), msg)
            message = msg + "  期望结果：图片 [" + filename + "] 存在  实际结果：图片 [" + filename + "] 存在,位置为" + str(pos)
            CmtLogger.writeToDatabase(LogCategory.assert_pass, logfile, "", message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
            return pos
        except AssertionError:
            message = msg + "  期望结果：图片 [" + filename + "] 存在  实际结果：图片 [" + filename + "] 不存在"
            CmtLogger.writeToDatabase(LogCategory.assert_error, logfile, "", message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            raise AssertionError("%s does not exist in screen, message: %s" % (filename, msg))
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))

    def assertImgNotExists(self, filename, msg="", caseName=None, threshold=ST.THRESHOLD):
        if caseName == None:
            caseName = self.casename
        """
            Assert target exists on device screen

            :param filename: target to be checked
            :param msg: short description of assertion, it will be recorded in the report
            :return: coordinates of the target
        """
        pic = Capture.fullPicturePath(filename)
        logfile = CmtLogger.logImage(pic)
        try:
            assert_not_exists(Template(pic, threshold=threshold), msg)
            message = msg + "  期望结果：图片 [" + filename + "] 不存在  实际结果：图片 [" + filename + "] 不存在"
            CmtLogger.writeToDatabase(LogCategory.assert_pass, logfile, "", message)
            log(message, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(1, caseName)
        except AssertionError:
            message = msg + "  期望结果：图片 [" + filename + "] 不存在  实际结果：图片 [" + filename + "] 存在"
            CmtLogger.writeToDatabase(LogCategory.assert_error, logfile, "", message)
            log(message, desc=msg, snapshot=True)
            if Labconfig.getIsLogJavis() == 'True':
                self.mySqlConnect.setCoreCase(0, caseName)
            raise AssertionError("%s does exist in screen, message: %s" % (filename, msg))
        # os.system('echo 断言:' + str(message))
        self.printCaseDevice('断言:' + str(message))

    ##############################################################trace check new fun start######################################################################################################
    # 埋点规则校验
    @classmethod
    def assertTraceKeyRuleCheck(self, ruleId="", caseName=None):
        if caseName == None:
            caseName = self.casename
        if ruleId == "":
            return
        # 根据规则ID获取校验埋点的字段信息
        # ruleDetail = MySqlConnect.queryEventTraceByRuleId(ruleId)
        taskId, caseId, mockKey = self.getBaseCheckInfo(self, caseName)
        # traceKey = MySqlConnect.queryEventTraceRuleCheck(ruleId)
        # 
        # traceValue = HandleInfo.getTraceValue(traceKey)
        cid = HandleInfo.getClientid()
        t1 = threading.Thread(target=mockApi.sysTraceResult, args=(ruleId, taskId, caseId, mockKey, cid))
        t1.start()

    currentTaskId = ''

    def getBaseCheckInfo(self, caseName):
        caseId = CmtLogger.case_result_id
        if caseId == 0:
            caseId = caseName
        taskId = Labconfig.get_ctaskid()
        if taskId == 0:
            if self.currentTaskId != '':
                taskId = self.currentTaskId
            else:
                self.currentTaskId = ''.join(random.sample(string.ascii_letters + string.digits, 15))
                taskId = self.currentTaskId
            # taskId = ''.join(random.sample(string.digits, 9))
            print("^^^^^^taskID:", taskId)
        mockKey = HandleInfo.getCurrentMockKey()
        return taskId, caseId, mockKey

    ##############################################################trace check new fun end######################################################################################################

    # 埋点值检查
    def assertTraceValueEqualMockValue(self, ruleId, msg="", caseName=None):
        try:
            if caseName == None:
                caseName = self.casename
            try:
                caseId = CmtLogger.case_result_id
            except Exception as e:
                print('caseId值获取失败:\t\t', str(e))
                caseId = 0
            taskId = Labconfig.get_ctaskid()
            mockKey = HandleInfo.getCurrentMockKey()
            queryEventTrackingTableResult = MySqlConnect.queryEventTrackingTable(ruleId)
            traceKey = queryEventTrackingTableResult[0][0]
            serviceName = queryEventTrackingTableResult[0][1]
            jsonPath = queryEventTrackingTableResult[0][2]
            ruleType = queryEventTrackingTableResult[0][3]
            ruleValue = queryEventTrackingTableResult[0][4]
            self.printCaseDevice('check traceKey is' + traceKey)
            if mockKey == '' or mockKey is None:
                result = 0
                message = '未获取到当前mockKey'
                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                     result, message, ruleId, '', '', '{}')
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                log(message, desc=None, snapshot=True)

            else:
                if ruleType == 1:
                    # 通过traceKey拿到的埋点校验值
                    tracekeDatas = self.getTracekeyData(traceKey, ruleValue)
                    jsonpathExpectValue = tracekeDatas[0]
                    tracekeyjsonpathvalue = tracekeDatas[1]
                    if jsonpathExpectValue == '':
                        result = 0
                        message = '获取上报的埋点数据失败'
                        self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                             result, message, ruleId, '', '', tracekeyjsonpathvalue)
                        CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                        log(message, desc=None, snapshot=True)
                        self.cmt_capture.getScreenShot()
                        return None
                    queryEventTrackingMockResult = MySqlConnect.queryEventTrackingMockTable(ruleId, jsonPath, serviceName)
                    # 读取接口返回报文数据
                    if queryEventTrackingMockResult == []:
                        mockKeyResponse = mockApi.getCaseApiInfoResponse(mockKey, serviceName)
                        if mockKeyResponse is None:
                            result = 0
                            message = '调用getCaseApiInfo接口失败'
                            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                 result, message, ruleId, '', '', tracekeyjsonpathvalue)
                            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                            log(message, desc=None, snapshot=True)
                            self.cmt_capture.getScreenShot()
                            return None
                        else:
                            mockKeyResponseJson = json.loads(mockKeyResponse)
                            try:
                                jsonpathRealValue = jsonpath.jsonpath(mockKeyResponseJson, jsonPath)[0]
                            except Exception as e:
                                print('获取jsonpathRealValue异常:\t\t', str(e))
                                print('获取jsonpathRealValue异常:\t', repr(e))
                                result = 0
                                message = '读取接口获取jsonpathRealValue失败'
                                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                     result, message, ruleId, '', '', tracekeyjsonpathvalue)
                                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                                log(message, desc=None, snapshot=True)
                                self.cmt_capture.getScreenShot()
                                return None
                            # 写入mock关系表testtool.event_tracking_mock
                            MySqlConnect.writeEventTrackingMockTable(mockKey, jsonpathRealValue, ruleId, jsonPath, serviceName)
                    # 读取接口返回报文数据
                    elif queryEventTrackingMockResult[0][0] <= 0:
                        mockKeyResponse = mockApi.getCaseApiInfoResponse(mockKey, serviceName)
                        if mockKeyResponse is None:
                            result = 0
                            message = '调用getCaseApiInfo接口失败'
                            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                 result, message, ruleId, '', '', tracekeyjsonpathvalue)
                            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                            log(message, desc=None, snapshot=True)
                            self.cmt_capture.getScreenShot()
                            return None
                        else:
                            mockKeyResponseJson = json.loads(mockKeyResponse)
                            try:
                                jsonpathRealValue = jsonpath.jsonpath(mockKeyResponseJson, jsonPath)[0]
                            except Exception as e:
                                print('获取jsonpathRealValue异常:\t\t', str(e))
                                print('获取jsonpathRealValue异常:\t', repr(e))
                                result = 0
                                message = '读取接口获取jsonpathRealValue失败'
                                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                     result, message, ruleId, '', '', tracekeyjsonpathvalue)
                                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                                log(message, desc=None, snapshot=True)
                                self.cmt_capture.getScreenShot()
                                return None
                            # 写入mock关系表testtool.event_tracking_mock
                            MySqlConnect.writeEventTrackingMockTable(mockKey, jsonpathRealValue, ruleId, jsonPath, serviceName)
                    else:
                        # 读取数据库中缓存的数据
                        jsonpathRealValue = queryEventTrackingMockResult[0][1]
                    if jsonpathExpectValue == jsonpathRealValue:
                        result = 1
                        message = "校验成功"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]等于[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]等于[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                    else:
                        result = 0
                        message = "检验失败，期望结果和实际结果不一致"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]等于[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]不等于[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                        self.cmt_capture.getScreenShot()
                    self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message,
                                                         ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)

        except Exception as e:
            print('assertTraceValueEqualMockValue(e):\t\t', str(e))
            print('assertTraceValueEqualMockValue(e):\t', repr(e))
            result = 0
            message = '调用assertTraceValueEqualMockValue方法异常'
            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                                 '', '', '{}')
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            log(message, desc=None, snapshot=True)
            self.cmt_capture.getScreenShot()
            return None

        # 埋点值检查

    def assertTraceValueContainMockValue(self, ruleId, msg="", caseName=None):
        try:
            if caseName == None:
                caseName = self.casename
            # caseId =""
            try:
                caseId = CmtLogger.case_result_id
            except Exception as e:
                print('caseId值获取失败:\t\t', str(e))
                print('caseId值获取失败:\t', repr(e))
                caseId = 0
            taskId = Labconfig.get_ctaskid()
            mockKey = HandleInfo.getCurrentMockKey()
            queryEventTrackingTableResult = MySqlConnect.queryEventTrackingTable(ruleId)
            traceKey = queryEventTrackingTableResult[0][0]
            serviceName = queryEventTrackingTableResult[0][1]
            jsonPath = queryEventTrackingTableResult[0][2]
            ruleType = queryEventTrackingTableResult[0][3]
            ruleValue = queryEventTrackingTableResult[0][4]
            self.printCaseDevice('check traceKey is' + traceKey)
            if mockKey == '' or mockKey is None:
                result = 0
                message = '未获取到当前mockKey'
                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                     result, message, ruleId, '', '', '{}')
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                log(message, desc=None, snapshot=True)
                self.cmt_capture.getScreenShot()
            else:
                if ruleType == 1:
                    # 通过traceKey拿到的埋点校验值
                    # jsonpathExpectValue = 1
                    tracekeDatas = self.getTracekeyData1(traceKey, ruleValue)
                    jsonpathExpectValue = tracekeDatas[0]
                    tracekeyjsonpathvalue = tracekeDatas[1]
                    if jsonpathExpectValue == []:
                        result = 0
                        message = '获取上报的埋点数据失败'
                        self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                             result, message, ruleId, '', '', tracekeyjsonpathvalue)
                        CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                        log(message, desc=None, snapshot=True)
                        self.cmt_capture.getScreenShot()
                        return None
                    queryEventTrackingMockResult = MySqlConnect.queryEventTrackingMockTable(ruleId, jsonPath, serviceName)
                    # 读取接口返回报文数据
                    if queryEventTrackingMockResult == []:
                        mockKeyResponse = mockApi.getCaseApiInfoResponse(mockKey, serviceName)
                        if mockKeyResponse is None:
                            result = 0
                            message = '调用getCaseApiInfo接口失败'
                            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                 result, message, ruleId, '', '', tracekeyjsonpathvalue)
                            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                            log(message, desc=None, snapshot=True)
                            self.cmt_capture.getScreenShot()
                            return None
                        else:
                            mockKeyResponseJson = json.loads(mockKeyResponse)
                            try:
                                jsonpathRealValue = jsonpath.jsonpath(mockKeyResponseJson, jsonPath)
                            except Exception as e:
                                print('获取jsonpathRealValue异常:\t\t', str(e))
                                print('获取jsonpathRealValue异常:\t', repr(e))
                                result = 0
                                message = '读取接口获取jsonpathRealValue失败'
                                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                     result, message, ruleId, '', '', tracekeyjsonpathvalue)
                                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                                log(message, desc=None, snapshot=True)
                                self.cmt_capture.getScreenShot()
                                return None
                            # 写入mock关系表testtool.event_tracking_mock
                            MySqlConnect.writeEventTrackingMockTable(mockKey, jsonpathRealValue, ruleId, jsonPath, serviceName)
                    # 读取接口返回报文数据
                    elif queryEventTrackingMockResult[0][0] <= 0:
                        mockKeyResponse = mockApi.getCaseApiInfoResponse(mockKey, serviceName)
                        if mockKeyResponse is None:
                            result = 0
                            message = '调用getCaseApiInfo接口失败'
                            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                 result, message, ruleId, '', '', tracekeyjsonpathvalue)
                            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                            log(message, desc=None, snapshot=True)
                            self.cmt_capture.getScreenShot()
                            return None
                        else:
                            mockKeyResponseJson = json.loads(mockKeyResponse)
                            try:
                                jsonpathRealValue = jsonpath.jsonpath(mockKeyResponseJson, jsonPath)
                            except Exception as e:
                                print('获取jsonpathRealValue异常:\t\t', str(e))
                                print('获取jsonpathRealValue异常:\t', repr(e))
                                result = 0
                                message = '读取接口获取jsonpathRealValue失败'
                                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                                     result, message, ruleId, '', '', tracekeyjsonpathvalue)
                                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                                log(message, desc=None, snapshot=True)
                                self.cmt_capture.getScreenShot()
                                return None
                            # 写入mock关系表testtool.event_tracking_mock
                            MySqlConnect.writeEventTrackingMockTable(mockKey, jsonpathRealValue, ruleId, jsonPath, serviceName)
                    else:
                        # 读取数据库中缓存的数据
                        jsonpathRealValue = queryEventTrackingMockResult[0][1]
                    if jsonpathExpectValue in jsonpathRealValue:
                        result = 1
                        message = "校验成功"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]包含[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]包含[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                    else:
                        result = 0
                        message = "期望结果和实际结果不一致"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]包含[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]不包含[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                        self.cmt_capture.getScreenShot()
                    self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message,
                                                         ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)
        except Exception as e:
            print('assertTraceValueEqualMockValue(e):\t\t', str(e))
            print('assertTraceValueEqualMockValue(e):\t', repr(e))
            result = 0
            message = '调用assertTraceValueEqualMockValue方法异常'
            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                                 '', '', '{}')
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            log(message, desc=None, snapshot=True)
            self.cmt_capture.getScreenShot()
            return None

    def assertTraceKeyFixedValue(self, ruleId, expr, msg="", caseName=None):
        try:
            if caseName == None:
                caseName = self.casename
            try:
                caseId = CmtLogger.case_result_id
            except Exception as e:
                print('caseId值获取失败:\t\t', str(e))
                print('caseId值获取失败:\t', repr(e))
                caseId = 0
            taskId = Labconfig.get_ctaskid()
            mockKey = HandleInfo.getCurrentMockKey()
            queryEventTrackingTableResult = MySqlConnect.queryEventTrackingTable(ruleId)
            traceKey = queryEventTrackingTableResult[0][0]
            ruleType = queryEventTrackingTableResult[0][3]
            ruleValue = queryEventTrackingTableResult[0][4]
            self.printCaseDevice('check traceKey is' + traceKey)
            if mockKey == '' or mockKey is None:
                result = 0
                message = '未获取到当前mockKey'
                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                     result, message, ruleId, '', '', '{}')
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                log(message, desc=None, snapshot=True)
                self.cmt_capture.getScreenShot()
            else:
                if ruleType == 2:
                    jsonpathExpectValue = expr
                    jsonPath = "$.." + ruleValue
                    tracekeDatas = self.getTracekeyData(traceKey, jsonPath)
                    jsonpathRealValue = tracekeDatas[0]
                    tracekeyjsonpathvalue = tracekeDatas[1]
                    if jsonpathRealValue == '':
                        result = 0
                        message = '获取上报的埋点数据失败'
                        self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result,
                                                             message, ruleId, jsonpathRealValue, jsonpathExpectValue,
                                                             tracekeyjsonpathvalue)
                        CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                        log(message, desc=None, snapshot=True)
                        self.cmt_capture.getScreenShot()
                        return None
                    if jsonpathExpectValue == jsonpathRealValue:
                        result = 1
                        message = "校验成功"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]等于[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]等于[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                    else:
                        result = 0
                        message = "检验失败，期望值和实际值不一致"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]等于[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]不等于[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                        self.cmt_capture.getScreenShot()
                    self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message,
                                                         ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)
        except Exception as e:
            print('assertTraceKeyFixedValue(e):\t\t', str(e))
            print('assertTraceKeyFixedValue(e):\t', repr(e))
            result = 0
            message = '调用assertTraceKeyFixedValue方法异常'
            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                                 '', '', '{}')
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            log(message, desc=None, snapshot=True)
            self.cmt_capture.getScreenShot()
            return None

    def assertTraceKeyFixedValueContain(self, ruleId, expr, msg="", caseName=None):
        try:
            if caseName == None:
                caseName = self.casename
            try:
                caseId = CmtLogger.case_result_id
            except Exception as e:
                print('caseId值获取失败:\t\t', str(e))
                print('caseId值获取失败:\t', repr(e))
                caseId = 0
            taskId = Labconfig.get_ctaskid()
            mockKey = HandleInfo.getCurrentMockKey()
            queryEventTrackingTableResult = MySqlConnect.queryEventTrackingTable(ruleId)
            traceKey = queryEventTrackingTableResult[0][0]
            ruleType = queryEventTrackingTableResult[0][3]
            ruleValue = queryEventTrackingTableResult[0][4]
            self.printCaseDevice('check traceKey is' + traceKey)
            if mockKey == '' or mockKey is None:
                result = 0
                message = '未获取到当前mockKey'
                self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey,
                                                     result, message, ruleId, '', '', '{}')
                CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                log(message, desc=None, snapshot=True)
                self.cmt_capture.getScreenShot()
            else:
                if ruleType == 2:
                    jsonpathExpectValue = expr
                    jsonPath = "$.." + ruleValue
                    tracekeDatas = self.getTracekeyData1(traceKey, jsonPath)
                    jsonpathRealValue = tracekeDatas[0][0]
                    tracekeyjsonpathvalue = tracekeDatas[1]
                    if jsonpathRealValue == []:
                        result = 0
                        message = '获取上报的埋点数据失败'
                        self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result,
                                                             message,
                                                             ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)
                        CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
                        log(message, desc=None, snapshot=True)
                        self.cmt_capture.getScreenShot()
                        return None
                    if jsonpathExpectValue in jsonpathRealValue:
                        result = 1
                        message = "校验成功"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]包含[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]包含[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_pass, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                    else:
                        result = 0
                        message = "检验失败，实际值不包含期望值"
                        resultMessage = msg + "  期望结果：[" + str(jsonpathExpectValue) + "]包含[" + str(
                            jsonpathRealValue) + "]  实际结果：[" + str(
                            jsonpathRealValue) + "]不包含[" + str(jsonpathExpectValue) + "]"
                        CmtLogger.writeToDatabase(LogCategory.assert_error, jsonpathExpectValue, jsonpathRealValue,
                                                  message)
                        log(resultMessage, desc=msg, snapshot=True)
                        self.cmt_capture.getScreenShot()
                    self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message,
                                                         ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)

        except Exception as e:
            print('assertTraceKeyFixedValueContain(e):\t\t', str(e))
            print('assertTraceKeyFixedValueContain(e):\t', repr(e))
            result = 0
            message = '调用assertTraceKeyFixedValueContain方法异常'
            self.OperateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                                 '', '', '{}')
            CmtLogger.writeToDatabase(LogCategory.assert_error, "True", False, message)
            log(message, desc=None, snapshot=True)
            self.cmt_capture.getScreenShot()
            return None

    # 更新和写表event_tracking_result
    def OperateEventTrackingResultTable(self, mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                        jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue):
        queryEventTrackingResult = MySqlConnect.queryEventTrackingResultTable(ruleId, taskId, caseId)
        if queryEventTrackingResult[0] > 0:
            MySqlConnect.updateEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message, ruleId,
                                                        jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)

        else:
            # 写入结果表testtool.event_tracking_result
            MySqlConnect.writeEventTrackingResultTable(mockKey, taskId, caseId, caseName, traceKey, result, message,
                                                       ruleId, jsonpathRealValue, jsonpathExpectValue, tracekeyjsonpathvalue)

    def getTracekeyData(self, traceKey, jsonPath):
        list = []
        tracekeyData = HandleInfo.getCurrentTraceKeyNew(traceKey, jsonPath)
        if (tracekeyData != []):
            jsonpathRealValue = tracekeyData[0]
            tracekeyjsonpathvalue = tracekeyData[1]
        else:
            jsonpathRealValue = ""
            tracekeyjsonpathvalue = {}
        list.append(jsonpathRealValue)
        list.append(tracekeyjsonpathvalue)
        return list

    def getTracekeyData1(self, traceKey, jsonPath):
        list = []
        tracekeyData = HandleInfo.getCurrentTraceKey1(traceKey, jsonPath)
        if (tracekeyData != []):
            jsonpathRealValue = tracekeyData[0]
            tracekeyjsonpathvalue = tracekeyData[1]
        else:
            jsonpathRealValue = ""
            tracekeyjsonpathvalue = {}
        list.append(jsonpathRealValue)
        list.append(tracekeyjsonpathvalue)
        return list

    def checkTraceInfo(self):
        endTime = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() + 5))
        dev = current_device()
        clientId = CommonAction.getClientId(Labconfig.getAppid(), dev.serialno)
        if (clientId == ''):
            clientId = HandleInfo.getClientid()
        caseName = self.casename
        mockKey = HandleInfo.getCurrentMockKey()
        queryEventTrackingTableResult = MySqlConnect.queryAllValidEventTraceRule(caseName)
        traceKeyList = []
        for item in queryEventTrackingTableResult:
            traceKeyList.append(item[0])
        if len(traceKeyList) == 0 or clientId == "":
            self.printCaseDevice("case未设置校验埋点或未获取到cid")
            return
        if (str(mockKey) == ""):
            mockKey = "0"
        req = {
            "taskId": Labconfig.get_ctaskid(),
            "suitFinish": False,
            "caseInfo": {
                "caseName": caseName,
                "result": len(self.caseResult.detail_errors) == 0,
                "keyList": traceKeyList,
                "mockKey": str(mockKey),
                "clientId": clientId,
                "startTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time / 1000)),
                "endTime": endTime
            }

        }
        self.printCaseDevice("case需要调用伏羲接口校验埋点")
        CommonAction.caseTraceCheck(req)

    def checkABExpResult(self, expCode, abVersion="", type=1):
        headers = {'content-type': 'application/json;charset=utf8'}
        url = "http://offline.fx.ctripcorp.com/soa2/22336/getIsTestVersion"
        dev = current_device()
        clientId = CommonAction.getClientId(Labconfig.getAppid(), dev.serialno)
        printUtil.printCaseDevice("校验分流操作需要20s，请确保case简洁不会超时")
        # 分流结果数据库5s刷新一次
        sleep(5)
        body = {
            "abVersion": abVersion,
            "beginTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time / 1000)),
            "endTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() + 5)),
            "cid": clientId,
            "expCode": expCode,
            "type": type
        }
        responseInfo = requests.post(url, data=json.dumps(body), headers=headers).json()
        result = responseInfo['result']
        message = responseInfo['resultMessage']
        printUtil.printCaseDevice(expCode + " " + abVersion + "版 实验分流校验结果为" + str(result) + " " + message)
        return result
