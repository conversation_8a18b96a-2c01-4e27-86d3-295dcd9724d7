# -*- coding: utf-8 -*-
from airtest.core.android.adb import ADB
import threading

from labuiframe.lib.utils.commonAction import CommonAction
from threading import Thread

__author__ = "tingxiao"

from pocounit.addons.poco.action_tracking import ActionTracker
from pocounit.result import PocoTestResult

from labuiframe.lib.case.suite import *
from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.mcd_util import Mcd_Util

import os
from CtestCommon.StartStage.RNConfig import *


class AndroidAppCase(AppTestCase):
    caseResult = PocoTestResult()
    mySqlConnect = MySqlConnect()
    casename = ""
    caseConfig = None
    activityName = None

    def __init__(self, methodName='runTest'):
        self.printCaseDevice("开始执行case")
        AppTestCase.__init__(self, methodName)

    @classmethod
    def setUpClass(cls):
        super(AndroidAppCase, cls).setUpClass()
        auto_setup(logdir=True, project_root=cls._resule_collector.project_root, basedir=cls._resule_collector.root)
        cls.printCaseDevice("********初始化poco**************")
        # cls.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
        cls.poco = Suite.poco
        if not current_device():
            DeviceInfo.connect_android_device()
        else:
            DeviceInfo.device = current_device()
        dev = current_device()
        cls.printCaseDevice("********device wake up**************")
        # 唤醒设备
        wake()
        # log cmt
        # 关闭系统弹窗
        ADB(serialno=dev.serialno).cmd("shell input keyevent KEYCODE_BACK")
        # 收缩下拉状态栏，避免上个case滑动导致状态栏展示
        try:
            name = DeviceInfo.getModelBySystem(DeviceInfo.deviceName)
            if 'MI' in name.upper() or 'Redmi' in name:
                ADB(serialno=dev.serialno).cmd("shell service call statusbar 3")
            else:
                ADB(serialno=dev.serialno).cmd("shell service call statusbar 2")
        except:
            cls.printCaseDevice("************收缩下拉状态栏***********")
        # 兼容模拟器出现pixel异常弹窗
        if cls.poco(text="Close app").exists():
            cls.poco(text="Close app").click()
        metaInfo = cls.getMetaInfo()
        CmtLogger.createCaseInfo(metaInfo, class_name=cls.__module__)
        cls.casename = metaInfo['casename']
        CommonAction.setTestCaseName(cls.casename)
        try:
            if metaInfo.get('cacheClear', 'false') == "false" and not Labconfig.cache_clear:
                cls.printCaseDevice("************不需要清理缓存***********")
            else:
                cls.printCaseDevice("************需要清理缓存***********")
                CommonAction.clearCache()
                # 清理缓存后需要重新启动一下获取AB实验
                start_app(Labconfig.getPackageName())
                stop_app(Labconfig.getPackageName())
        except Exception:
            cls.printCaseDevice("************默认不清理缓存***********")
        try:
            requestCount = metaInfo['requestCount']
            Labconfig.setRequestCount(requestCount)
            cls.printCaseDevice("************case设置requestCount数量为" + str(requestCount))
        except Exception as e:
            cls.printCaseDevice(str(e))
            cls.printCaseDevice("************case默认requestCount数量为20***********")
        try:
            tags = metaInfo['tags']
            # 初始化第一个tag
            if Labconfig.getIsHotelWirelessAppGroup() == "True":
                CommonAction.getPrimaryCaseId()
                taglist = tags.split("|")
                HandleInfo.setCirculateInfos(HandleInfo.currentCirculate, len(taglist), taglist, cls.casename)
                metaInfo = cls.getMetaInfo()
        except Exception as e:
            cls.printCaseDevice(str(e))
            cls.printCaseDevice("************case没有关联的tag信息***********")
        HandleInfo.setMetaInfo(metaInfo)
        if Labconfig.getAppid() == "37":
            if (Labconfig.get_breakSoaHookLogin() == 1):
                CommonAction.getAccountInfo("_TSHK58mtk96ypml", "123456asd", "IBUAPPTESTAUTHENTICATE",2)
            else:
                CommonAction.getAccountInfo("_TIHK105r80r5y5bd", "123456asd", "IBUAPPTESTAUTHENTICATE",2)
        # cls.casename = metaInfo['casename']
        # CommonAction.setTestCaseName(cls.casename)
        # cls.casePlatform = metaInfo['platform']
        try:
            Labconfig.setPackageName(metaInfo['package'])
        except Exception:
            cls.printCaseDevice("************metaInfo未获取到package信息***********")

        try:
            extention = Labconfig.get_extention()
            # extention = {'group': 0, 'uiCaseInfoList': [{'id': 3671853, 'tags': ['345417:国内预付房型', '345419:国内现付房型']}], 'labelID': 0, 'jSceneID': 0, 'operatorInfo': {'displayName': 'shali'}}
            cls.printCaseDevice("************extention:{}s***********".format(str(extention)))
            if extention is not None and extention.get('isDebug', 0) == 1:
                # aiagent超时时间设置为1200s
                Labconfig.set_finalTimeOut(1200)
                Labconfig.set_ai_agent_debug(True)
                cls.printCaseDevice(
                    "************AiAgent调试场景超时时间为{}***********".format(str(Labconfig.get_finalTimeOut())))
            else:
                timeout = metaInfo.get('timeout', Labconfig.get_caseTimeOut())
                Labconfig.set_finalTimeOut(int(timeout))
                cls.printCaseDevice("************用例超时时间为" + str(timeout) + "s***********")
        except Exception:
            self.printCaseDevice("************   超时逻辑  异常地方未处理¥¥¥¥   s***********")

        # 默认true,不影响整个项目的配置
        singleLocalMock = "true"
        try:
            singleLocalMock = metaInfo['localMock']
            cls.printCaseDevice("*****singleLocalMock config:" + singleLocalMock)
        except Exception:
            cls.printCaseDevice("*****未获取到localMock信息,走项目配置默认配置****")
        try:
            ori_case_config_json = json.loads(metaInfo.get('config'))
            if Labconfig.getAppid() == "37":
                HandleInfo.setCurrentMockKey(ori_case_config_json.get("flightMockKey", ""))
                if Labconfig.getOpenLocalMock() == "True" and singleLocalMock.lower() == "true":
                    cls.pushMockKeyMessage(HandleInfo.getCurrentMockKey())
                elif singleLocalMock.lower() == "false":
                    cls.removeLocalMockFile(HandleInfo.getCurrentMockKey())
            elif Labconfig.getAppid() == "99999999":
                HandleInfo.setCurrentMockKey(ori_case_config_json.get("mockKey", ""))
                if Labconfig.getOpenLocalMock() == "True" and singleLocalMock.lower() == "true":
                    cls.pushMockKeyMessage(HandleInfo.getCurrentMockKey())
                elif singleLocalMock.lower() == "false":
                    cls.removeLocalMockFile(HandleInfo.getCurrentMockKey())
            # 如果case中设置了cid，就使用case中的；否则使用数据库数据
            if ori_case_config_json.get("SetCid") != None and ori_case_config_json.get("SetCid") > 0:
                cls.printCaseDevice("************case中已设置cid", ori_case_config_json.get("SetCid"))
                # 日志用于自动分析失败case，请不要变更！！
                cls.printCaseDevice("get clientid为:", str(ori_case_config_json.get("SetCid")))
                cls.caseConfig = metaInfo['config']
                HandleInfo.setCurrentClientid(str(ori_case_config_json.get("SetCid")))
            else:
                cls.printCaseDevice("******case中没有设置cid")
                if HandleInfo.getCurrentClientid() == "":
                    tableCid = CommonAction.getClientId(Labconfig.getAppid(), dev.serialno)
                else:
                    tableCid = HandleInfo.getCurrentClientid()
                if len(tableCid) == 0:
                    cls.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "get clientid is empty")
                else:
                    cls.printCaseDevice(time.strftime("%H:%M:%S", time.localtime(time.time())), "get clientid:", tableCid)
                    HandleInfo.setCurrentClientid(tableCid)
                if tableCid:
                    try:
                        cidDict = int(tableCid)
                        ori_case_config_json["SetCid"] = cidDict
                    except Exception as e:
                        printUtil.printCaseDevice("cid转换异常：{}".format(str(e)))
                cls.caseConfig = json.dumps(ori_case_config_json)
            CmtLogger.createMetaConfig(metaInfo)
            cls.printCaseDevice("*****case config:" + cls.caseConfig)
            # cls.caseConfig = json.dumps(ori_case_config_json)

        except Exception as e:
            cls.printCaseDevice('*****get client config fail:\t\t', str(e))

        if Labconfig.getAppid() == "37":
            cls.activityName = "com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity"
        elif Labconfig.getAppid() == "99999999":
            cls.activityName = "ctrip.business.splash.CtripSplashActivity"
        else:
            try:
                cls.activityName = metaInfo['activity']
            except Exception as e:
                cls.activityName = Labconfig.get_activity()
                Labconfig.set_activity(Labconfig.get_activity())
                cls.printCaseDevice("************case未设置activity,默认使用Labconfig：" + str(Labconfig.get_activity()) + "***********")
        # 日志用于自动化分析case，请勿变更
        cls.printCaseDevice("*****activity config:" + cls.activityName)
        cls.package_name = Labconfig.getPackageName()

        action_tracker = ActionTracker(cls.poco)
        cls.register_addon(action_tracker)

        if 'tracer' in cls._result_emitters:
            cls._result_emitters.pop('tracer')

        cls.printCaseDevice("********启动app**************")
        timeTempToFramework = int(round(time.time() * 1000))
        Labconfig.setTimeSplitToFramework(int(round((timeTempToFramework - cls.start_time) / 1000, 0)))
        if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            Labconfig.set_config_data(Labconfig.get_config_file_path())
            # print("**获取到的configEnv值为**：{}".format(Labconfig.get_configEnv()))
            if cls.activityName is None and Labconfig.get_activity() is not None:
                cls.activityName = Labconfig.get_activity()

            if Labconfig.get_configEnv() is not None and cls.activityName is not None:
                if cls.caseConfig is not None and len(cls.caseConfig) > 0:
                    env_config_json = json.loads(Labconfig.get_configEnv())
                    case_config_json = json.loads(cls.caseConfig)
                    config_json = dict(env_config_json, **case_config_json)
                    config_str = str(base64.b64encode(json.dumps(config_json).encode('utf-8')), 'utf-8')
                else:
                    config_str = Labconfig.get_encode_configEnv()
                    cls.printCaseDevice("获取到的configEnv值为：{}".format(config_str))
                start_app_configenv(dev, cls.package_name, "1", config_str)
                # if Labconfig.getAppid() == "37":
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName), '--es', 'hideDebug 1',
                #          '--es', 'configEnv %s' % (config_str)])
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias'),
                #          '--es', 'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias2'),
                #          '--es', 'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
                # else:
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName), '--es',
                #          'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
            elif cls.caseConfig is not None and len(cls.caseConfig) > 0 and cls.activityName is not None:
                config_str = str(base64.b64encode(cls.caseConfig.encode('utf-8')), 'utf-8')
                cls.printCaseDevice("获取到的configEnv值为：{}".format(config_str))
                start_app_configenv(dev, cls.package_name, "1", config_str)
                # if Labconfig.getAppid() == "37":
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName), '--es', 'hideDebug 1',
                #          '--es', 'configEnv %s' % (config_str)])
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias'), '--es', 'hideDebug 1', '--es',
                #          'configEnv %s' % (config_str)])
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias2'), '--es', 'hideDebug 1', '--es',
                #          'configEnv %s' % (config_str)])
                # else:
                #     ADB(serialno=dev.serialno).shell(
                #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName), '--es',
                #          'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
            else:
                start_app(cls.package_name)
        elif cls.caseConfig is not None and len(cls.caseConfig) > 0 and cls.activityName is not None and len(cls.activityName) > 0:
            config_str = str(base64.b64encode(cls.caseConfig.encode('utf-8')), 'utf-8')
            cls.printCaseDevice("获取到的configEnv值为：{}".format(config_str))
            start_app_configenv(dev, cls.package_name, "1", config_str)
            # if Labconfig.getAppid() == "37":
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName), '--es', 'hideDebug 1',
            #          '--es', 'configEnv %s' % (config_str)])
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias'),
            #          '--es', 'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
            #     ADB(serialno=dev.serialno).shell(
            #         ['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName + '.alias2'),
            #          '--es', 'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
            # else:
            #     ADB(serialno=dev.serialno).shell(['am', 'start', '-n', '%s/%s' % (cls.package_name, cls.activityName),
            #                                       '--es', 'hideDebug 1', '--es', 'configEnv %s' % (config_str)])
        else:
            start_app(cls.package_name)
        if cls.poco(text="OK").exists():
            cls.poco(text="OK").click()
        cls.printCaseDevice("********成功启动app2**************")
        timeTempToWakeupAPPSecondEnd = int(round(time.time() * 1000))
        Labconfig.setTimeSplitToHotelPage(int(round((timeTempToWakeupAPPSecondEnd - timeTempToFramework) / 1000, 0)))
        # threading.Thread(target=HandleInfo.getServiceRequest())
        HandleInfo.clearData()

    # @classmethod
    # def printLog(cls):
    #     # 日志用于自动分析case，请不要变更！
    #     # ctrip-case日志增加登录打印，trip已有
    #     if Labconfig.get_loginInfo() != "":
    #         cls.printCaseDevice("登录信息为**" + Labconfig.get_loginInfo() + "**")
    #     # 增加增量获取日志--去除增量下载逻辑
    #     '''
    #     if len(Labconfig.get_channelCode()) > 0 and Labconfig.getAppversion() != "":
    #         cls.printCaseDevice("需要下载增量信息**" + str(set(Labconfig.get_channelCode())).replace("\'", "\"") + "**")
    #         # cls.printCaseDevice("增量分支信息**" + Labconfig.get_crnBranchName() + "**")
    #         if Labconfig.get_downloadChannelCode() != "":
    #             cls.printCaseDevice("成功获取的增量信息**" + str(Labconfig.get_downloadChannelCode()) + "**")
    #             cls.printCaseDevice("下载增量时间**" + Labconfig.get_downloadChannelCodeTime() + '**')
    #             # 增加增量下载结果日志信息
    #             if len(Labconfig.get_failLoadChannelCode()) > 0:
    #                 cls.printCaseDevice("下载失败增量信息**" + str(Labconfig.get_failLoadChannelCode()) + "**")
    #             else:
    #                 printUtil.printCaseDevice("**增量下载成功**")
    #         else:
    #             cls.printCaseDevice("**未获取到下载增量信息**")
    #     else:
    #         cls.printCaseDevice("**无需下载增量**")
    #     '''
    #     # 增加clientId获取日志
    #     if HandleInfo.getCurrentClientid() != "":
    #         cls.printCaseDevice("clientId信息**" + HandleInfo.getCurrentClientid() + "**")
    #     # 增加MockId获取日志
    #     if HandleInfo.getCurrentMockKey() != "":
    #         cls.printCaseDevice("mockId信息**" + HandleInfo.getCurrentMockKey() + "**")
    #     # 增加openUrl获取日志
    #     if Labconfig.get_openUrl() != "":
    #         cls.printCaseDevice("openUrl信息**" + Labconfig.get_openUrl() + "**")
    #     # 增加本地mock日志
    #     if Labconfig.getLocalMockStatus():
    #         cls.printCaseDevice("**本地Mock成功**")
    #     else:
    #         cls.printCaseDevice("**非本地Mock**")

    @classmethod
    def getConfigEnv(cls, caseConfig):
        try:
            configEnv = json.loads(caseConfig)
            print("转换前的configEnv:{}".format(str(configEnv)))
            if "channelCode" in configEnv and len(configEnv["channelCode"]) > 0 and Labconfig.getAppversion() != "":
                channelList = []
                for i in range(len(configEnv["channelCode"])):
                    dict = {"channelCode": "", "buildId": 0}
                    branchName = cls.transferBranchName(configEnv["channelCode"][i])
                    buildId = Mcd_Util.getCrnBuildId(configEnv["channelCode"][i], branchName, Labconfig.getAppversion(), Labconfig.getAppid())
                    if (buildId > 0):
                        dict["channelCode"] = configEnv["channelCode"][i]
                        dict["buildId"] = buildId
                        channelList.append(dict)
                    else:
                        cls.printCaseDevice('*******branch has no buildId!*****')
                        cls.printCaseDevice("channelCode:{},builid:{}".format(configEnv["channelCode"][i], buildId))
                if len(channelList) > 0:
                    channelList = "," + "\"channelList\"" + ":" + str(channelList).replace("\'", "\"")
                    configEnv = caseConfig[:-1] + channelList + "}"
            else:
                cls.printCaseDevice("未获取到channelcode相关信息，无需转换！")
                configEnv = caseConfig
            return str(configEnv)
        except:
            return caseConfig
            cls.printCaseDevice("转换异常！")

    @classmethod
    def transferBranchName(cls, channelCode):
        branchName = ""
        presaleBundleName = ["rn_hotel_packageList", "rn_hotel_packageOrders", "rn_hotel_package", "rn_boom"]
        tripCommentBundleName = ["rn_hotel_comment_submit"]
        ctripCommentBundleName = ["rn_hotelCommentSubmit"]
        if (datetime.datetime.today().weekday()) == 3:
            # 3是周四
            if channelCode in presaleBundleName:
                branchName = "rel/" + Labconfig.getAppversion() + "_prod"
            elif channelCode in ctripCommentBundleName:
                branchName = "release/ctrip/" + Labconfig.getAppversion()
            elif channelCode in tripCommentBundleName:
                branchName = "release/trip/" + Labconfig.getAppversion()
            else:
                branchName = "rel/" + Labconfig.getAppversion()
        else:
            if channelCode in presaleBundleName:
                branchName = "rel/" + Labconfig.getAppversion() + "_dev"
            elif channelCode in tripCommentBundleName or channelCode in ctripCommentBundleName:
                branchName = "develop"
            else:
                branchName = "dev/" + Labconfig.getAppversion()
        del presaleBundleName, ctripCommentBundleName, tripCommentBundleName
        return branchName

    @classmethod
    def pushMockKeyMessage(cls,mockKey):
        if mockKey == "":
            return
        headers = {'content-type': 'application/json;charset=utf8'}
        dev = current_device()
        serial = dev.serialno
        url = "http://mock.fws.qa.nt.ctripcorp.com/SuiteApi/GetCaseConfigInfo"
        body = {
            "CaseID": int(mockKey),
            "UserDomain": "cn1",
            "UserName": "hyang9"
        }
        dataInfo = requests.post(url, data=json.dumps(body), headers=headers).json()
        apiList = dataInfo["MessageBody"]["ApiConfigs"]
        try:
            if (len(apiList) > 0):
                Labconfig.setLocalMockStatus(True)
        except:
            printUtil.printCaseDevice("获取本地Mock信息失败！")
        for item in apiList:
            serviceName = item["InterfaceName"]
            messageList = item["MessageList"]
            if item["IsMocking"] == False:
                continue
            for i in messageList:
                if i["MessageType"] == "1":
                    messageContent = i["MessageContent"]
                    fileName = serial + "_" + serviceName + ".json"
                    HandleInfo.localMockNameList.append(fileName)
                    url = cls._resule_collector.project_root + os.sep + "mockKey" + os.sep
                    if not os.path.exists(url):
                        os.mkdir(url)
                    printUtil.printCaseDevice("开始写入文件")
                    f = open(url + fileName, "w", encoding="UTF-8")
                    f.write(messageContent)
                    f.close()
                    printUtil.printCaseDevice("开始推送文件")
                    if "-" in serviceName:
                        serviceName = str(serviceName).split('-')[1]
                    out = ADB(serialno=serial).cmd("push " + url + fileName + " /storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey +"/" + serviceName + ".json")
                    printUtil.printCaseDevice("out")
                    os.remove(url + fileName)

    @classmethod
    def removeLocalMockFile(cls, mockKey):
        # 先删除手机里面mockkey的文件夹下的内容
        try:
            url = "/storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey + "/*.*"
            dev = current_device()
            serial = dev.serialno
            ADB(serialno=serial).cmd("shell rm " + url)
            # 再删除文件夹
            url = "/storage/emulated/0/Android/data/" + Labconfig.getPackageName() + "/files/mockkey/" + mockKey
            ADB(serialno=serial).cmd("shell rm -r " + url)
        except:
            printUtil.printCaseDevice("本地mock文件不存在或无需删除")
