# -*- coding: utf-8 -*-
__author__ = "tingxiao"

import subprocess

from pocounit.addons.poco.action_tracking import ActionTracker
from pocounit.addons.poco.capturing import SiteCaptor

from poco.drivers.ios import iosPoco
import wda
from airtest.core.api import device as current_device, start_app,stop_app,snapshot

from labuiframe.lib.case.app import AppTestCase
from labuiframe.lib.case.suite import *
from labuiframe.lib.result.logger import *
from labuiframe.lib.utils.device import DeviceInfo

from labuiframe.lib.config.labconfig import Labconfig
import sys
class IOSAppCase(AppTestCase):

    def __init__(self):
        AppTestCase.__init__(self)


    @classmethod
    def setUpClass(cls):


        print(sys._getframe().f_code.co_filename)  # 当前文件名，可以通过__file__获得
        print(sys._getframe(0).f_code.co_name)  # 当前函数名
        print(sys._getframe(1).f_code.co_name)  # 调用该函数的函数名字，如果没有被调用，则返回<module>
        print(sys._getframe(0).f_lineno)  # 当前函数的行号
        print(sys._getframe(1).f_lineno)  # 调用该函数的行号

        super(IOSAppCase, cls).setUpClass()

        metaInfo = cls.getMetaInfo()
        cls.casename = metaInfo['casename']

        # log cmt
        CmtLogger.createCaseInfo(cls.getMetaInfo(),class_name=cls.__module__)

        if not current_device():
            DeviceInfo.connect_android_device()

        dev = current_device()

        cls.package_name = CommonAction.getIosPackageName(Labconfig.getAppid(), DeviceInfo.get_is_simulator())

        subprocess.getoutput("xcrun simctl openurl " + HandleInfo.currentSim +
                             " 'ctrip://wireless/hotel_inland_detail?hotelId=345016&hotelDataType=1&cityid=2'")
        print("1222222\n")
        meta_info_emitter = cls.get_result_emitter('metaInfo')
        print("1222222\n")
        cls.poco = iosPoco()
        if cls.poco("open").exists():
            cls.poco("open").click()
        print("1222222\n")
        auto_setup(logdir=True, project_root=cls._resule_collector.project_root, basedir=cls._resule_collector.root)
        action_tracker = ActionTracker(cls.poco)
        cls.register_addon(action_tracker)
        print("1222222\n")
        # cls.site_capturer = SiteCaptor(cls.poco)
        # cls.register_addon(cls.site_capturer)

        if 'tracer' in cls._result_emitters:
            cls._result_emitters.pop('tracer')
