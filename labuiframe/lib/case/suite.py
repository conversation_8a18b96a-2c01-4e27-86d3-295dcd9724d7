# coding=utf-8
import base64
import datetime
import json
import time

from airtest.core.android import adb
from airtest.core.android.adb import ADB

from labuiframe.lib.case.app import AppTestCase
from labuiframe.lib.result.logger import CmtLogger
from labuiframe.lib.utils.HandleInfo import HandleInfo
from labuiframe.lib.utils.capture import Capture, Template
from labuiframe.lib.utils.commonAction import CommonAction
from labuiframe.lib.utils.installation import install_app, uninstall_app
from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.utils.installation import install_app
from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.mcd_util import Mcd_Util
from labuiframe.lib.utils.printUtil import printUtil
import threading
from labuiframe.lib.utils.watcher_util import Watcher_util
from pocounit.suite import PocoTestSuite
from airtest.core.api import device as current_device, connect_device, wake, start_app, exists, click
from airtest.core.api import start_app, stop_app, install, uninstall,keyevent,sleep
import os
import urllib.request
from labuiframe.lib.runner.AndroidUiautomationPoco import AndroidUiautomationPoco
from poco.drivers.ios import iosPoco
import re
import subprocess
import wda
import configparser
from CtestCommon.StartStage.RNConfig import *

class Suite(PocoTestSuite):
    # 是否重装包
    isinstall = 0
    packageList = []
    # 装包路径
    root_path = ""
    package = 'res/app/Ctrip_V7.15.0_auto2304_test_4631224_jdyfb.apk' #initial value
    file_url = ''
    # 平台
    platform = 'Android'
    device = ""
    suite_name = ""
    appIDOrName = "ctrip"
    simulatorDeviceIp = ""
    ip_port = ""


    def __init__(self, pocoTestSuite):
        super(PocoTestSuite, self).__init__(pocoTestSuite)
        if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            Labconfig.set_config_data(Labconfig.get_config_file_path())
            Labconfig.set_wacher_config(Labconfig.get_config_file_path())
            Labconfig.setAppVersion(Labconfig.get_config_file_path())
        CmtLogger.addAllCaseInfo(pocoTestSuite)
        # self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)

    def setUp(self):
        if self.platform.lower() == 'ios' or self.platform.lower() == 'android':
            if not DeviceInfo.get_device_exist():
                # TODO: will modify the device initial
                Labconfig.suitPlatform = self.platform.lower()
                if self.device != '':
                    DeviceInfo.deviceName = self.device
                if Labconfig.getOpenAppPerf() and self.simulatorDeviceIp != '':
                    #连接模拟器
                    DeviceInfo.connect_android_simulator_device(self.simulatorDeviceIp)
                elif self.platform.lower() == 'android':
                    DeviceInfo.connect_android_device()
                elif self.platform.lower() == 'ios':
                    self.ip_port = self.get_ip_port_ios()

                    DeviceInfo.connect_ios_device(ip=self.ip_port)
                else:
                    raise RuntimeError('Please set \'platform\' value in Suite ')
            dev = current_device()
            if self.platform.lower() == 'android':
                adb = ADB(serialno=dev.serialno)
                self.package_name = Labconfig.getPackageName()
                if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
                    try:
                        print("get ctaskid not null, to uninstall other app", )
                        packageName = "ctrip.android.view" if Labconfig.getPackageName() == "ctrip.english.debug" else "ctrip.english.debug"
                        print("[Lab] Uninstall package:" + packageName)
                        uninstall_app(packageName, 'android')
                    except Exception as e:
                        print("****** get ctaskid",)
            elif self.platform.lower() == 'ios':
                self.package_name = CommonAction.getIosPackageName(Labconfig.getAppid(), DeviceInfo.get_is_simulator())
            self.root_path = Labconfig.getRootPath()
            # 判断项目是否是CRN页面
            # Labconfig.setIsFlutterPackage(True)
            # package_path = Labconfig.getFlutterPackage()
            # if package_path is not None and package_path != '':
            #     flutterPackageNameList = Labconfig.getFlutterPackageNameList()
            #     if len(flutterPackageNameList) > 0:
            #         for flutterPackageName in flutterPackageNameList:
            #             if flutterPackageName in package_path:
            #                 Labconfig.setIsFlutterPackage(False)
            #                 break
            self.isFlutterPackage()

            self.package_name_list = Labconfig.getPackageNameList()

            self.platform = DeviceInfo.getPlatform()
            #设置手机导航栏隐藏，规避导航栏展示导致滑动失败问题--注：Android11系统以上不支持overscan命令
            if DeviceInfo.is_android_run():
                if not AppTestCase.isDebug() and Labconfig.getIsHotelWirelessAppGroup() == "True":
                    try:
                        ADB(serialno=current_device().serialno).cmd("shell wm overscan 0,0,0,-210", device=True)
                    except Exception as e:
                        try:
                            ADB(serialno=current_device().serialno).cmd("shell cmd overlay enable com.android.internal.systemui.navbar.gestural", device=True)
                        except Exception as e:
                            printUtil.printCaseDevice("***不屏蔽导航栏***")
            if self.platform.lower() == 'android':
                Suite.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            elif self.platform.lower() == 'ios':
                Suite.poco = iosPoco()

            #对于appperf项目，本地场景需要将语言设置为en,teardown中再恢复
            if Labconfig.getAppPerfProject() and not Labconfig.getAppperfAwsType() == "AWS":
                try:
                    ADB(serialno=current_device().serialno).cmd("shell am start -a android.settings.LOCALE_SETTINGS", device=True)
                    if self.poco(text="English").wait(2).exists():
                        self.poco(text="English").click()
                        print("系统语言设置为英文成功")
                    else:
                        print("系统语言未查找到英文选项!")
                except Exception as e:
                    printUtil.printCaseDevice("系统语言设置英文失败：{}".format(str(e)))


            if self.isinstall:
                if len(self.packageList) == 0:
                    self.downloadPackage() # download package
                    try:
                        # uninstall_android_app(current_device().adb,self.package_name)
                        # uninstall(self.package_name)
                        if self.platform.lower() == 'android':
                            print("[Lab] Uninstall package:"+self.package_name)
                            uninstall_app(self.package_name,'android')
                        elif  self.platform.lower() == 'ios':
                            print("[Lab] Uninstall package:" + self.package_name)
                            uninstall_app(self.package_name, "ios")
                    except Exception as e:
                        printUtil.printCaseDevice("****** Uninstall app error",e)
                    # apk_path = self.R(self.package)
                    apk_path = "{}{}{}".format(self.root_path, os.sep, self.package)
                    printUtil.printCaseDevice("[Lab] Package to be installed:" + apk_path)
                    install_app(localpath=apk_path, platform=self.platform)
                else:
                    try:

                        if len(self.package_name_list) > 0:
                            for packageName in self.package_name_list:
                                print("[Lab] Uninstall package:" + packageName)
                                if self.platform.lower() == 'android':
                                    uninstall_app(packageName)
                                elif self.platform.lower() == 'ios':
                                    subprocess.getoutput("xcrun simctl uninstall " + self.device + " " + packageName)
                    except Exception as e:
                        printUtil.printCaseDevice("****** Uninstall app error", e)
                    # apk_path = self.R(self.package)
                    for package in self.packageList:
                        apk_path = "{}{}{}{}{}{}{}".format(self.root_path,os.sep,'res',os.sep,'app', os.sep, package)
                        printUtil.printCaseDevice("[Lab] Package to be installed:" + apk_path)
                        install_app(localpath=apk_path, platform=self.platform)

            printUtil.printCaseDevice("###########配置中的appid" , Labconfig.getAppid())

            # 小程序扫码流程
            if Labconfig.get_scan_code() == 'True' and Labconfig.get_extention() is not None:
               self.scan_code()
            timeTempToFirstWakeupStart = int(round(time.time() * 1000))

            # 启动app后的一些处理，比如关闭弹窗，根据应用号设置activity
            self.handle_package_process()
            # 下载crn增量--去除增量下载逻辑
            if self.platform.lower() == 'android':
                stop_app(self.package_name)
            elif self.platform.lower() == 'ios':
                stop_app(Labconfig.getIosPackageName())

            timeTempToFirstWakeupEnd = int(round(time.time() * 1000))
            Labconfig.setTimeToFistWakeUp(int(round((timeTempToFirstWakeupEnd - timeTempToFirstWakeupStart) / 1000, 0)))

            # 判断是否是覆盖率包
            Labconfig.set_iscoverage(self.is_coverage_package(self.package))
            Labconfig.setPackage(self.package)
            Labconfig.setPlatformEnv(CmtLogger.getServiceUrlByRunId())
            self.handle_page_info()
        elif self.platform.lower() == 'web':
            self.handle_package_process()
            Labconfig.suitPlatform = self.platform.lower()

    # 小程序扫码流程
    def scan_code(self):
        extention = Labconfig.get_extention()
        if extention["qrcodeUrl"] is not None:
            stop_app(self.package_name)  # 初始化关闭应用app
            wake()
            file_name = os.path.basename(extention["qrcodeUrl"])
            local_image_path = Labconfig.getRootPath() + os.sep + "res" + os.sep + file_name
            print("开始下载图片，链接：" + extention["qrcodeUrl"])
            urllib.request.urlretrieve(extention["qrcodeUrl"], local_image_path)
            if os.path.exists(local_image_path):
                print("下载图片完成（路径：{}），开始导入相册".format(local_image_path))
                push_command = "push {} /sdcard/DCIM/Camera/{}".format(local_image_path, file_name)
                adb.cmd(push_command)
                adb.shell(
                    ['am', 'broadcast', '-a', 'android.intent.action.MEDIA_SCANNER_SCAN_FILE', '-d',
                     'file:///sdcard/DCIM/Camera/%s' % (file_name)])
                print("图片导入相册完成")
                start_app(self.package_name)
                sleep(2)
                print("打开应用：" + self.package_name)
                self.click_image(self.get_wechatmore_button())
                sleep(1)
                self.click_image(self.get_wechatquery_button())
                sleep(1)
                self.click_image(self.get_wechatpic_button())
                print("扫描二维码开始！")
                sleep(4)
                click(Template(local_image_path))
                sleep(1)
                # self.poco(name='com.tencent.mm:id/f1c')[1].wait(10).click()
                # self.poco(text='扫一扫').wait(2).click()
                # self.poco(text='相册').wait(2).click()
                # Suite.poco = AndroidUiautomationPoco(force_restart=True, use_airtest_input=True,
                #                                      screenshot_each_action=False)
                # self.poco(name='com.tencent.mm:id/gqx')[1].wait(2).click()
                print("扫描二维码完成！")
                print("开始删除二维码图片")
                os.remove(local_image_path)
                if not os.path.exists(local_image_path):
                    print("成功删除下载图片")
                adb.shell(['rm', '/sdcard/DCIM/Camera/%s' % (file_name)])
                adb.shell(
                    ['am', 'broadcast', '-a', 'android.intent.action.MEDIA_SCANNER_SCAN_FILE', '-d',
                     'file:///sdcard/DCIM/Camera/%s' % (file_name)])
            else:
                print("下载图片失败！")

    # 安装包后的处理
    def handle_package_process(self):
        # 兜底默认配置
        if Labconfig.getAppid() == "********":
            if (Labconfig.get_breakSoaHookLogin() == 1):
                CommonAction.getAccountInfo("hotelbreaksoa", "123456asd", "APPTESTAUTEHNTICATE",1)
            else:
                CommonAction.getAccountInfo("htlautotest", "123456asd", "APPTESTAUTEHNTICATE",1)
            self.loadOfCtrip()
            if Labconfig.activity is None:
                Labconfig.set_activity("ctrip.business.splash.CtripSplashActivity")
        elif Labconfig.getAppid() == "37":
            if (Labconfig.get_breakSoaHookLogin() == 1):
                CommonAction.getAccountInfo("_TSHK58mtk96ypml", "123456asd", "IBUAPPTESTAUTHENTICATE",2)
            else:
               CommonAction.getAccountInfo("_TIHK105r80r5y5bd", "123456asd", "IBUAPPTESTAUTHENTICATE",2)
            self.lodeOfTrip()
            if Labconfig.activity is None:
                Labconfig.set_activity("com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity")

        elif Labconfig.getAppid() == "5093":
            self.systemActionBar(self.poco)
        elif Labconfig.getAppid() == "5109":
            self.systemActionBar(self.poco)

        pass

    #打开页面后，处理页面信息，比如关闭大首页弹窗
    def handle_page_info(self):
        if self.platform.lower() == 'android':
            self.close_popups()
        elif self.platform.lower() == 'ios':
            self.close_popups_ios()
        printUtil.printCaseDevice("开始终止进程")
        stop_app(self.package_name)

    def close_popups_ios(self):
        print("HandleInfo.currentIp_port:", HandleInfo.currentIp_port)
        driver = wda.Client(HandleInfo.currentIp_port)
        driver.app_launch(self.package_name)
        if driver(name="同意并继续").wait(5) != None:
            driver(name="同意并继续").click()
            sleep(2)
        if driver(name="Allow").wait(5) != None:
            driver(name="Allow").click()
        if driver(name="Allow").wait(5) != None:
            driver(name="Allow").click()
        pass

    def close_popups_qunar(self):
        printUtil.printCaseDevice("启动完毕，关闭去哪儿APP弹窗")
        if self.poco(name="com.Qunar:id/spider_splash_privacy_tv_agree").wait(2).exists():
            self.poco(name="com.Qunar:id/spider_splash_privacy_tv_agree").click()
        printUtil.printCaseDevice("弹窗处理完毕")
        # 下载QP包
        if self.poco(text="QP包已是最新版本").wait(2).exists():
            printUtil.printCaseDevice("QP包已是最新版本")
        elif self.poco(text="QP包下载成功,发挥作用需要重新启动").wait(2).exists(): # QP包已经下载成功
            printUtil.printCaseDevice('QP包下载成功,发挥作用需要重新启动')
            sleep(3)
        elif self.poco(text="本地QP包和目标QP包版本不一致，开始下载QP包").wait(2).exists(): # 开始下载QP包
            printUtil.printCaseDevice('本地QP包和目标QP包版本不一致，开始下载QP包')
            if self.poco(text="QP包下载成功,发挥作用需要重新启动").wait(15).exists(): # 等待QP包下载成功
                printUtil.printCaseDevice('QP包下载成功，即将重新启动APP')
                sleep(3)


    # 关闭相关弹框
    def close_popups(self):
        wake()
        printUtil.printCaseDevice("wake up device")
        if Labconfig.getAppid() == "5125": # 第一次启动去哪儿APP，启动传递QP包增量参数用于自动下载
            activityName = Labconfig.get_activity() if Labconfig.get_activity() is not None else 'com.mqunar.splash.SplashActivity'
            launchConfig = {}
            # 读取labconfig配置文件，获取QP包
            rootpath = '{}{}{}'.format(Labconfig.root_path, os.sep, 'labconfig.ini')
            print("rootpath:", rootpath)
            if os.path.exists(rootpath):
                print("[Lab] labconfig.ini exists")
                conf = configparser.ConfigParser()
                conf.read(rootpath, encoding="utf-8-sig")
                qpTag = conf.get('QUNARConfig', 'qpTag')
                printUtil.printCaseDevice("启动去哪儿APP，处理弹窗并下载指定QP包")
                printUtil.printCaseDevice("QP包:" + qpTag)
                launchConfig['qpTag'] = qpTag
            # base64加密
            config_str = str(base64.b64encode(json.dumps(launchConfig, ensure_ascii=False).encode('utf-8')), 'utf-8')
            dev = current_device()
            # 授予显示在应用上层的权限
            ADB(serialno=dev.serialno).cmd("shell appops set com.Qunar SYSTEM_ALERT_WINDOW allow ",
                                                        device=True)
            sleep(1)
            # 启动APP
            ADB(serialno=dev.serialno).shell(
                ['am', 'start', '-n', '%s/%s' % (self.package_name, activityName), '--es',
                 'hideDebug 1',
                 '--es',
                 'configEnv %s' % config_str])
            self.close_popups_qunar()
            return
        else:
            start_app(self.package_name)
        printUtil.printCaseDevice("启动完毕，关闭弹窗")
        try:
            if self.poco(text="允许").wait(2).exists():
                self.poco(text="允许").click()
            # 规避app主页面未展示就执行关闭弹窗问题
            if self.poco(text="OK").wait(2).exists():
                self.poco(text="OK").click()
            if self.poco(text="取消").exists():
                self.poco(text="取消").click()
            printUtil.printCaseDevice("弹窗处理完毕")
        except Exception as e:
            printUtil.printCaseDevice("弹窗处理失败:{}".format(str(e)))


    #判断是flutter项目
    def isFlutterPackage(self):
        # 判断项目是否是CRN页面
        Labconfig.setIsFlutterPackage(True)
        package_path = Labconfig.getFlutterPackage()
        if package_path is  None or package_path != '':
            return True
        flutterPackageNameList = Labconfig.getFlutterPackageNameList()
        if len(flutterPackageNameList) <=0:
            return True
        for flutterPackageName in flutterPackageNameList:
            if flutterPackageName in package_path:
                Labconfig.setIsFlutterPackage(False)
                break

    # 通过图片点击元素
    def click_image(self, path):
        if exists(path):
            click(path)
            print("image exists and clicked")
        else:
            print("no image exists")

    def click_img(self, imagename, threshold=0.8, record_pos=None, resolution=()):
        self.click_image(self.full_image_path(imagename, threshold=threshold, record_pos=record_pos,
                                                  resolution=resolution))

    # 返回图片本地全路径  threshold图片匹配百分比，框架里默认0.7
    def full_image_path(self, imagename, threshold=0.85, record_pos=None, resolution=()):
        imagepath = "{}{}{}".format("weixin", os.sep, imagename)
        fullimagepath = Capture.fullPicturePath(imagepath)
        return Template(fullimagepath, threshold=threshold, record_pos=record_pos, resolution=resolution)
    # 微信更多按钮
    def get_wechatmore_button(self):
        return self.full_image_path("微信更多.png", threshold=0.85)
    # 微信扫一扫按钮
    def get_wechatquery_button(self):
        return self.full_image_path("微信扫一扫.png", threshold=0.85)
    # 微信相册入口
    def get_wechatpic_button(self):
        return self.full_image_path("微信相册.png", threshold=0.85)



    def tearDown(self):
        if Labconfig.is_coverage():
            keyevent('BACK')
            sleep(3)
        if Labconfig.getIsStopApp() == "True":
            if self.platform.lower() != "web":
                # mockApi.deleteWhiteList()
                print("stop app")
                stop_app(self.package_name)
        self.processEndTask()
        self.stopSuite()

    # 处理结束任务前的逻辑
    def processEndTask(self):
        if self.platform.lower() == 'android':
            # ctest的任务，结束后，卸载app
            if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
                uninstall_app(self.package_name, 'android')
            # 安卓模拟器处理
            self.androidVirtualProcess()
            #运行完后，卸载pocoservice,避免长时间运行导致poco service不稳定，出现process crash问题
            # uninstall_app("com.netease.open.pocoservice", 'android')
            # 退出登录操作
            self.logout()
        elif self.platform.lower() == 'ios':
            pass

    # 退出登录操作，频繁的登录，不退出登录，会导致登录那边登录次数满了，后面登录不上【这里后面可以改成通过接口api的形式退出登录】减少app唤起处理的逻辑
    # 退出登录操作
    def logout(self):
        if Labconfig.getAppid() == "********" and Labconfig.get_need_logout():
            printUtil.printCaseDevice("suite执行完成，退出登录")
            activityName = "ctrip.business.splash.CtripSplashActivity"
            logoutConfig = "{\"loginOut\":{\"desc\":\"退出登录\",\"status\":1}}"
            config_str = str(base64.b64encode(logoutConfig.encode('utf-8')), 'utf-8')
            dev = current_device()
            start_app_configenv(dev, self.package_name, "1", config_str)
            # ADB(serialno=dev.serialno).shell(
            #     ['am', 'start', '-n', '%s/%s' % (self.package_name, activityName), '--es',
            #      'hideDebug 1',
            #      '--es',
            #      'configEnv %s' % (config_str)])
            sleep(3)

    # 安卓模拟器处理
    def androidVirtualProcess(self):
        if self.platform.lower() == 'ios':
            return False
        if not AppTestCase.isDebug():
            return False
        try:
            ADB(serialno=current_device().serialno).cmd("shell wm overscan 0,0,0,0", device=True)
        except Exception as e:
             printUtil.printCaseDevice("***不屏蔽导航栏***")




    def downloadPackage(self):
        try:
            package_name = os.environ['app']
            self.file_url = package_name
        except Exception as e :
            print("[Lab] Get APP Exception:",e)

        if self.file_url == "":
            print("[Lab] file_url is uninitial")
            return

        file_path = '{}{}{}{}{}'.format(self.root_path,os.sep,'res',os.sep,'app')

        if not os.path.exists(file_path):
            os.makedirs(file_path)
        file_name = os.path.split(self.file_url)[1]
        fileName = '{}{}{}'.format(file_path,os.sep,file_name)
        print("[Lab] Download file at:"+fileName)
        urllib.request.urlretrieve(self.file_url,fileName)

        self.package = '{}{}{}{}{}'.format('res',os.sep,'app',os.sep,file_name)
        # print("********package:"+ self.package)


    def is_coverage_package(self,file_name):
        if "CtripMain-debug" in file_name:
            return True
        else:
            return False


    def log_suite_run(self,suite_name, appIDOrName='ctrip'):
        """
        For Watcher
        :param suite_name:
        :return:
        """
        if suite_name is None or suite_name == "":
            Labconfig.set_watcher_enabled('False')
            return
        _run_id = 0
        if Labconfig.get_ctaskid() is not None and int(Labconfig.get_ctaskid()) > 0:
            _run_id = int(Labconfig.get_ctaskid())
            print('[Lab] _run_id:' + str(_run_id))
        else:
            try:
                _run_id = os.environ['runId']
                print('[Lab] _run_id:' + str(_run_id))
            except Exception:
                Labconfig.set_watcher_enabled('False')
                return
        print('[Lab] Start watcher log suite run')
        self.suite_name = suite_name
        if Labconfig.getAppid() is not None:
            if Labconfig.getAppid()  == "********":
                appIDOrName = "ctrip"
            elif Labconfig.getAppid() == "37":
                appIDOrName = "Trip"
        self.appIDOrName = appIDOrName
        Watcher_util().watcher_testsuitelog(_run_id, suite_name, appIDOrName)

    def stopSuite(self):
        """
        For Watcher
        :param suite_name:
        :return:
        """
        _run_id = 0
        if Labconfig.is_watcher_enabled() == 'False' or self.suite_name == "":
            return
        if Labconfig.get_ctaskid() is not None and int(
                Labconfig.get_ctaskid()) > 0:
            _run_id = int(Labconfig.get_ctaskid())
            print('[Lab] Stop Suite, _run_id is:' + str(_run_id))
            CmtLogger.uploadWatcherUrl(_run_id, self.suite_name)
        else:
            try:
                _run_id = os.environ['runId']
                print('[Lab] Stop Suite, _run_id is:' + str(_run_id))
                # _run_id = "1234"
            except Exception:
                Labconfig.set_watcher_enabled('False')
                return
        print('[Lab] stop watcher log suite run')
        Watcher_util().watcher_stopSuite(_run_id, self.suite_name, self.appIDOrName)

    def loadOfCtrip(self):
        printUtil.printCaseDevice("###################关闭系统弹窗####################")
        if self.platform.lower() == 'android':
            try:
                if not self.poco(self.byId("ctrip.android.view:id/tab_host_index")).exists():
                    i = 0
                    start = time.time()
                    while i < 6:
                        end = time.time()
                        if self.poco(text="同意并继续").exists():
                            self.poco(text="同意并继续").click()
                            print("关闭同意并继续")
                            if (end - start) > 5:
                                return
                        if self.poco(text="知道了").exists():
                            self.poco(text="知道了").click()
                            if (end - start) > 5:
                                return
                            printUtil.printCaseDevice("关闭获取权限提示页面-知道了")
                        if self.poco(text="始终允许").exists():
                            self.poco(text="始终允许").click()
                            printUtil.printCaseDevice("关闭始终允许--权限设置弹窗")
                            if (end - start) > 5:
                                return
                        if self.poco(text="确定").exists():
                            self.poco(text="确定").click()
                            printUtil.printCaseDevice("确定-poco弹窗")
                            if (end - start) > 5:
                                return
                        if self.poco(text="使用本应用时允许").exists():
                            self.poco(text="使用本应用时允许").click()
                            printUtil.printCaseDevice("关闭位置权限信息")
                            if (end - start) > 5:
                                return
                        if self.poco(text="取消").exists():
                            self.poco(text="取消").click()
                            print("取消")
                            if (end - start) > 5:
                                return
                        i = i + 1
                        end = time.time()
                        if (end - start) > 10:
                            printUtil.printCaseDevice('处理系统弹窗耗时{}秒,超出10S，直接退出'.format((end - start)))
                            return
                    end = time.time()
                    printUtil.printCaseDevice('处理系统弹窗耗时{}秒'.format((end - start)))
                    # 首页跳过
                    if self.poco(text="跳过").exists():
                        self.poco(text="跳过").click()
            except Exception as e:
                printUtil.printCaseDevice("处理系统弹窗异常:{}".format(str(e)))
        elif self.platform.lower() == 'ios':
            a = 1


    def systemActionBar(self,poco):
        printUtil.printCaseDevice("关闭系统浮层")
        if poco(text='允许显示在其他应用的上层').exists():
            poco(text='允许显示在其他应用的上层').click()
            self.clickBack()
        if poco(text='在其他应用上层显示').exists():
            poco("android:id/switch_widget").click()
            self.clickBack()
        if poco("miui:id/action_bar_title").exists():
            i = 0
            while i < 4:
                if poco(text="系统设置").exists():
                    poco(text="系统设置").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="后台弹出界面").exists():
                    poco(text="后台弹出界面").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="桌面快捷方式").exists():
                    poco(text="桌面快捷方式").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="锁屏显示").exists():
                    poco(text="锁屏显示").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="显示悬浮窗").exists():
                    poco(text="显示悬浮窗").click()
                    sleep(1)
                    poco(text="允许").click()
                    self.clickBack()
                    break
                i = i + 1
                poco().swipe([-0.0613, -0.4500])

    def closeLogin(self):
        if self.poco("android:id/content").child("android.widget.FrameLayout").child("android.widget.ImageView").exists():
            self.setabtest("201016_HTL_qzdl", "A")

    def clickBack(self, poco=None):
        '''
        native页面返回，支持安卓和ios
        :param poco:
        :return:
        '''
        platform = DeviceInfo.getPlatform()
        if platform.lower() == 'android':
            keyevent('BACK')
            sleep(2)
        elif platform.lower() == 'ios':
            poco.click([0.055, 0.059])
            sleep(2)

    def byId(self, id):
        if Labconfig.is_coverage():
            list = id.split(":")
            if len(list) > 1:
                if "ctrip.android" in list[0]:
                    list[0] = "ctrip.android.view"
                overageId = ""
                a = 0
                for item in list:
                    overageId = overageId + item
                    a = a + 1
                    if a != len(list):
                        overageId = overageId + ":"
                id = overageId
            del list
        return id

    def byTripId(self, id):
        list = id.split(":")
        if len(list) > 1:
            isDebugPackage = Labconfig.get_is_debug_package()
            if isDebugPackage == "True":
                list[0] = "ctrip.english.debug"
            else:
                list[0] = "ctrip.english"
            overageId = ""
            a = 0
            for item in list:
                overageId = overageId + item
                a = a + 1
                if a != len(list):
                    overageId = overageId + ":"
            id = overageId
        return id

    def lodeOfTrip(self, timeout =0):
        # sleep(3)
        # self.systemActionBarOfTrip(self.poco)
        i = 0
        # if self.poco(text="仅充电").exists():
        #     self.poco(text="仅充电").click()
        start = time.time()
        print("[{}]关闭系统弹窗开始处理中".format(time.strftime("%H:%M:%S"), time.localtime(start)))
        try:
            while i < 8:
                if DeviceInfo.is_ios_run():
                    if self.poco(name="close button").exists():
                        self.poco(name="close button").click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                if DeviceInfo.is_android_run():
                    print("[{}]，处理批次".format(time.strftime("%H:%M:%S"), time.localtime(start)),i)
                    if self.poco(text="始终允许").exists():
                        self.poco(text="始终允许").click()
                        print("关闭始终允许-软件更新弹窗")
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # ALLOW
                    if self.poco(textMatches='([\s\S]*)ALLOW([\s\S]*)').exists():
                        self.poco(text='([\s\S]*)ALLOW([\s\S]*)').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # 允许
                    if self.poco(textMatches='([\s\S]*)允许([\s\S]*)').exists():
                        self.poco(text='([\s\S]*)ALLOW([\s\S]*)').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    if self.poco(text='略過').exists():
                        self.poco(text='略過').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # 暫不需要
                    if self.poco(text='暫不需要').exists():
                        self.poco(text='暫不需要').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # 稍後
                    if self.poco(text='稍後').exists():
                        self.poco(text='稍後').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # 好的
                    if self.poco(text='好的').exists():
                        self.poco(text='好的').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    if self.poco(text='明白').exists():
                        self.poco(text='明白').click()
                    end = time.time()
                    if (end - start) > 8:
                        return
                    # sleep(2)
                    i = i + 1
                end = time.time()
                if (end-start) >10:
                    return
            end = time.time()
            print("[{}]关闭系统弹窗处理结束-".format(time.strftime("%H:%M:%S"), time.localtime(start)),'处理系统弹窗耗时{}秒'.format((end-start)))
        except Exception as e:
            printUtil.printCaseDevice("处理系统弹窗异常:{}".format(str(e)))

    def systemActionBarOfTrip(self, poco):
        print("关闭系统浮层")
        if poco(text="始终允许").exists():
            poco(text="始终允许").click()
        if poco(text="debug boom提示").exists():
            print("关闭debug boom")
            poco(self.byTripId("ctrip.english.debug:id/btn_negative")).click()
        if poco(text="允许").exists():
            poco(text="允许").click()
        if poco(text='允许显示在其他应用的上层').exists():
            if poco("android:id/switch_widget").exists():
                poco("android:id/switch_widget").click()
            else:
                poco(text='允许显示在其他应用的上层').click()
            self.clickBack()
            sleep(2)
        if poco(text='在其他应用上层显示').exists():
            if poco("android:id/switch_widget").exists():
                poco("android:id/switch_widget").click()
            else:
                poco(text='在其他应用上层显示').click()
            self.clickBack()
            sleep(2)
        if poco(text="继续").exists():
            poco(text="继续").click()
        if poco("miui:id/action_bar_title").exists():
            i = 0
            while i < 4:
                if poco(text="系统设置").exists():
                    poco(text="系统设置").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="后台弹出界面").exists():
                    poco(text="后台弹出界面").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="桌面快捷方式").exists():
                    poco(text="桌面快捷方式").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="锁屏显示").exists():
                    poco(text="锁屏显示").click()
                    sleep(1)
                    poco(text="允许").click()
                if poco(text="显示悬浮窗").exists():
                    poco(text="显示悬浮窗").click()
                    sleep(1)
                    poco(text="允许").click()
                    self.clickBack()
                    break
                i = i + 1
                poco().swipe([-0.0613, -0.4500])
        if poco(self.byTripId("ctrip.english.debug:id/allowButton")).exists():
            poco(self.byTripId("ctrip.english.debug:id/allowButton")).click()
        if poco(self.byTripId("ctrip.english.debug:id/v_app_market")).exists():
            poco(self.byTripId("ctrip.english.debug:id/icf_close")).click()

    # 设置客户端A/B实验
    def setabtest(self, abname, ab):
        if self.poco("android:id/content").child("android.widget.FrameLayout").child(
                "android.widget.ImageView").exists():
            self.poco("android:id/content").child("android.widget.FrameLayout").child(
                "android.widget.ImageView").click()
            self.poco(self.byId("ctrip.android.debug:id/debug_select_rg")).child(
                self.byId("ctrip.android.debug:id/debug_bu_rb")).click()
            sleep(1)
            i = 0
            while i < 9:
                if self.poco(text="酒店设置").exists():
                    self.poco(text="酒店设置").click()
                    sleep(2)
                    self.poco(text="酒店ABTest设置").click()
                    self.poco("ctrip.android.hotel:id/keyword_text").set_text(abname)
                    self.poco("ctrip.android.view:id/textView_title")[0].long_click(duration=3)
                    if ab == "A":
                        self.poco(text="A").click()
                    if ab == "B":
                        self.poco(text="B").click()
                    if ab == "C":
                        self.poco(text="C").click()
                    if ab == "D":
                        self.poco(text="D").click()
                    if ab == "E":
                        self.poco(text="E").click()
                    if ab == "F":
                        self.poco(text="F").click()
                    if ab == "G":
                        self.poco(text="G").click()
                    print(self.poco("ctrip.android.view:id/textView_desc")[0].get_text())
                    self.poco("ctrip.android.view:id/btn_back").click()
                    self.clickBack()
                    self.clickBack()
                    # self.poco("ctrip.android.view:id/btn_back").click()
                    print(abname + "实验切换成功")
                    break
                else:
                    self.poco().swipe([-0.0613, -0.4500])
                    i = i + 1


    def transferBranchName(self, channelCode):
        branchName = ""
        # presaleBundleName = ["rn_hotel_packageList", "rn_hotel_packageOrders", "rn_hotel_package", "rn_ibu_hotel_package"]
        # presaleBundleNameOldBranch = ["rn_boom"]
        tripPresaleBundleNameBranch = ["rn_boom","rn_ibu_hotel_package"]
        if (datetime.datetime.today().weekday()) == 3:
            # 3是周四
            printUtil.printCaseDevice("周四，获取rel分支")
            if channelCode in tripPresaleBundleNameBranch:
                branchName = "rel/trip_" + Labconfig.getAppversion() + "_prod"
            elif channelCode == "rn_search":
                branchName = "dev"
            else:
                branchName = "rel/0.7"
        else:
            printUtil.printCaseDevice("非周四，获取dev分支")
            if channelCode in tripPresaleBundleNameBranch:
                branchName = "rel/trip_" + Labconfig.getAppversion() + "_dev"
            elif channelCode == "rn_search":
                branchName = "dev"
            else:
                branchName = "dev/0.7"
        del tripPresaleBundleNameBranch
        return branchName

    def get_ip_port_ios(self):
        ip_port = ""
        is_virtual = False
        try:
            # portPath = os.environ['HOME'] + os.sep + self.device + ".txt"
            # f = open(portPath, encoding='utf-8')
            # ip_port = "http://localhost:" + f.read()
            # f.close()
            ip_port = "http://localhost:{}".format(DeviceInfo.get_port())
        #   需要使用idb判断一次是真机还是模拟器
            json_str = subprocess.getoutput("idb list-targets --json")
            json_list = json_str.split("\n")
            # is_virtual True：模拟器 / False：真机
            for json1 in json_list:
                json1 = json.loads(json1)
                if json1["state"] == "Booted" and json1["type"] == "simulator":
                    is_virtual = True
                    break
        #   如果是模拟器，设备device 0是真机 1是模拟器
            if is_virtual:
                DeviceInfo.set_is_simulator(1)
            else:
                DeviceInfo.set_is_simulator(0)
        except FileNotFoundError:
            # 判断是不是真机
            print("本地端口文件打开失败，这走本地执行单独获取一台设备处理")

            json_str = subprocess.getoutput("idb list-targets --json")
            ios_device_list = json_str.split("\n")
            real_device = subprocess.getoutput("idevice_id -l")
            real_device_list = real_device.split("\n")

            booted_devices = [json.loads(device_info) for device_info in ios_device_list if
                              json.loads(device_info)['state'] == 'Booted']
            if len(booted_devices) != 1:
                raise RuntimeError('Boot count not equal to 1. Error!')

            device_info = booted_devices[0]
            udid = device_info["udid"]
            is_simulator = device_info["type"] == "simulator"
            if not is_simulator:
                subprocess.Popen(["iproxy", "8100", "8100"])
                sleep(1)

            ip_port ="http://localhost:8100"
            # ip_port = "http://localhost:8100" if is_simulator else subprocess.getoutput("iproxy 8100 8100")

            DeviceInfo.set_is_simulator(int(is_simulator))
            DeviceInfo.deviceName = udid
            self.device = udid
            HandleInfo.currentSim = udid

            #
            #
            # json_str = subprocess.getoutput("idb list-targets --json")
            # ios_device_list = json_str.split("\n")
            # real_device = subprocess.getoutput("idevice_id -l")
            # real_device_list = real_device.split("\n")
            # boot_count = 0
            # ios_count = 0
            # udid = ""
            # for device_info in ios_device_list:
            #     device_info = json.loads(device_info)
            #     if device_info['state'] == 'Shutdown':
            #         continue
            #     if device_info["state"] == "Booted" and device_info["type"] == "simulator":
            #         boot_count += 1
            #         udid = json1["udid"]
            #     if device_info["state"] == "Booted" and device_info["type"] == "device":
            #         if device_info["udid"] in real_device_list:
            #             ios_count += 1
            #             udid = device_info["udid"]
            # if boot_count + ios_count > 1 :
            #     raise RuntimeError('Boot count above 1. Error!')
            # elif boot_count == 1:
            #     ip_port = "http://localhost:8100"
            #     DeviceInfo.set_is_simulator(1)
            # elif ios_count == 1:
            #     subprocess.getoutput("iproxy 8100 8100")
            #     ip_port = "http://localhost:8100"
            #     DeviceInfo.set_is_simulator(0)
            # DeviceInfo.deviceName = udid
            print('iOS 设备名称：', DeviceInfo.deviceName)
        HandleInfo.currentIp_port = ip_port
        printUtil.printCaseDevice("ip_port:{}".format(ip_port))
        return ip_port

