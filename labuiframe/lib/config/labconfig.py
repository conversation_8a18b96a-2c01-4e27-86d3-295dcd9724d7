# -*- coding: utf-8 -*-
import base64

__author__ = "tingxiao"

import os
import configparser
import json
from labuiframe.lib.utils.device import DeviceInfo


class Labconfig():
    configpath = ""
    root_path = ""
    isLogJavis = "False"
    isLogLab = "True"
    packageName = "ctrip.android.view.debug"
    iosPackageName = "com.ctrip.inner.wireless"
    isStopApp = "True"
    _is_coverage = False
    coverageRunId = ""
    package = ""
    listen_port = ''
    appUrl = ""
    _ctaskid = 0
    mpaas_main_taskid = 0
    _watcher_enabled = "True"
    appversion = ""
    _watcher_debug = "False"
    runId_debug = ""
    group = ""
    isClearApp = "False"
    _is_changEnv = "False"
    changEnv = ""
    platformEnv = ""
    isDebugPackage = None
    appid = None
    config_file_path = None
    configEnv = None
    activity = None
    retry = 0
    sync_watcher_result = 'False'
    ignore_watcher_result = 'True'
    json_extension = None
    crnPerformance = "0"
    # 单个case耗时时间，兼容其他bu场景，单位s
    caseTimeout = 1800
    final_timeout = 1800
    loginInfo = ""
    ticket = ""
    duid = ""
    # 是否忽略ctest设置的package，默认为False, 取ctest平台设置的package
    ignore_ctest_package = 'False'
    # Ctest传入extention信息
    extention = None
    # 是否需要扫码
    scan_code = 'False'
    # mthemis log路径
    mthemis_log_path = '/data/user_de/0/org.meowcat.edxposed.manager/log/error.log'
    # firewall log路径
    firewall_log_path = '/sdcard/firewall_log.txt'
    # 是否需要上传视频，默认为True，需要上传
    upload_video = "True"
    isCheckTraceInfo = "False"
    # 用来标识酒店app专属功能开关
    isHotelWirelessAppGroup = "False"
    # 获取从接口传入的指定频道增量获取数据
    channelBundles = []
    # 请求hook登录是否用breakSoa
    breakSoaHookLogin = 0
    openAppPerf = False
    packageNameList = []
    mainlandCity = []
    overseaCity = []
    runCount = 0
    overseaDetailCity = ""
    isAppPerfProject = False
    openUrl = ""
    # 这个是本次suit的平台
    suitPlatform = "android"
    openLocalMock = "False"
    localMockStatus = False
    flutterPackageNameList = []
    isFlutterPackage = True
    flutterPackage = ""
    requestCount = 200
    timeToFistWakeUp = 0
    timeSplitToFramework = 0
    timeSplitToHotelPage = 0
    timeSplitToLoadPage = 0
    timeSplitToCase = 0
    # 多语言shark key列表
    sharkList = []
    ai_agent_debug = False
    label_id = 0
    trace_log_id = ""
    case_platform = 1
    ai_agent_result = False
    need_logout = True
    # 是否走mpass新流程
    _is_mpass = False
    path = ""
    # 0代表真机，1代表模拟器
    mpass_device_type = 0
    # web自动化初始化浏览器参数
    headless = True
    ignore_https_errors = True
    bypass_csp = True
    slow_mo = 1000
    web_is_mobile = False
    device_scale_factor = 1
    viewport = None
    web_default_timeout = 20000
    custom_headers = {}
    web_cookies = []
    no_viewport = True
    # 增量信息，从mpaas平台获取
    module_name = ""
    module_id = ""
    # 项目维度是否清除缓存--售前项目存在测试+生产账号切换问题，为避免共用测试账号cticket失效，不能使用登出方法，只能清缓存
    cache_clear = True
    # ai生成服务传递的信息，例如：loginInfo、activity
    aiGenerateExtension = None
    account = None
    password = None
    accessCode = None
    platformId = None # 记录当前执行的平台id
    buId = 1 # 记录当前执行的buId
    # 记录启动的所有线程
    threading_list = []
    aiGenerateResult = False
    # 记录web端pipeline触发运行环境信息
    pipeline_env = None

    @classmethod
    def config(self, project_root):
        self.root_path = project_root
        rootpath = '{}{}{}'.format(project_root, os.sep, 'labconfig.ini')
        print("rootpath:", rootpath)
        if not os.path.exists(rootpath) and self.path != "":
            rootpath = '{}{}{}'.format(self.path, os.sep, 'labconfig.ini')
            print("兜底获取rootpath:", rootpath)
        if not os.path.exists(rootpath):
            if len(self.package) > 0:
                self.packageName = self.package
            else:
                self.packageName = 'ctrip.android.view'
            print("[lab] labconfig.ini does not exist")
            return
        else:
            print("[Lab] labconfig.ini exists")
            try:
                conf = configparser.ConfigParser()
                conf.read(rootpath, encoding="utf-8-sig")
                try:
                    self.isLogJavis = conf.get('UILab', 'isLogJavis')
                except:
                    print("isLogJavis config is not setted")
                try:
                    self.isLogLab = conf.get('UILab', 'isLogLab')
                except:
                    print("isLogLab config is not setted")
                try:
                    self.isStopApp = conf.get('APP', 'isStopApp')
                except:
                    print("isStopApp config is not setted")
                try:
                    self.flutterPackageNameList = conf.get('UILab', 'flutterPackageNameList').split(",")
                except:
                    self.flutterPackageNameList = []
                # print("*****Config:Platform:"+DeviceInfo.getPlatform())
                try:
                    idDebugConfig = conf.get("APP", "isDebugPackage")
                    self.isDebugPackage = self.is_debug_package(idDebugConfig)
                except:
                    self.isDebugPackage = None
                if self.isDebugPackage is not None:
                    if self.isDebugPackage == "True":
                        self.packageName = conf.get('Android Debug', 'packageName')
                    else:
                        self.packageName = conf.get('Android', 'packageName')
                else:
                    self.packageName = conf.get('Android', 'packageName')
                try:
                    self.appid = conf.get("APP", "appid")
                except:
                    self.appid = None
                try:
                    self.mainlandCity = conf.get("UILab", "mainlandCity").split(",")
                except:
                    print("mainlandCity config is not setted in UILab")
                try:
                    self.overseaCity = conf.get("UILab", "overseaCity").split(",")
                except:
                    print("overseaCity config is not setted in UILab")
                try:
                    self.runCount = int(conf.get("UILab", "runCount"))
                except:
                    print("runCount config is not setted in UILab")
                try:
                    self.overseaDetailCity = conf.get("UILab", "overseaDetailCity")
                except:
                    print("overseaDetailCity config is not setted in UILab")
                try:
                    cache_clear_flag = conf.get("UILab", "cache_clear")
                    self.cache_clear = True if cache_clear_flag == "True" else False
                except:
                    print("need_logout config is not setted in UILab")
                self.iosPackageName = conf.get('IOS', 'packageName')
                self.group = conf.get('UILab', 'group')
                self._watcher_enabled = conf.get("UILab", "_watcher_enabled")
                self._watcher_debug = conf.get("UILab", "_watcher_debug")
                self.runId_debug = conf.get("UILab", "runId_debug")
            except Exception as e:
                print("Read labconfig.ini Exception:" + str(e))
            try:
                self.appid = conf.get("APP", "appid")
            except:
                self.appid = None
            try:
                self.isCheckTraceInfo = conf.get('UILab', 'isCheckTraceInfo')
            except Exception as e:
                print("isCheckTraceInfo config is not setted")
            try:
                self.isHotelWirelessAppGroup = conf.get('UILab', 'isHotelWirelessAppGroup')
            except Exception as e:
                print("isHotelWirelessAppGroup config is not setted")
            try:
                self.openLocalMock = conf.get('UILab', 'openLocalMock')
            except Exception as e:
                print("openLocalMock config is not setted default false")
            try:
                self.retry = int(conf.get("UILab", "retry"))
            except Exception as e:
                print("retry config is not setted")
            try:
                self.caseTimeout = int(conf.get("UILab", "timeout"))
            except Exception as e:
                print("timeout config is not setted")
            try:
                self.platform = conf.get("APP", "platform")
            except Exception as e:
                print("platform config is not setted")
            try:
                self.env = conf.get("APP", "env")
            except Exception as e:
                print("env config is not setted")
            try:
                self.activity = conf.get("APP", "activity")
            except Exception as e:
                print("activity config is not setted")
            try:
                self.breakSoaHookLogin = int(conf.get("UILab", "breakSoaHookLogin"))
            except Exception as e:
                print("breakSoaHookLogin config is not setted")
            try:
                self.sync_watcher_result = conf.get('watcher', 'syncResult')
                self.ignore_watcher_result = conf.get('watcher', 'ignoreResult')
            except Exception as e:
                print("watcher result config is not setted")

            print("[lab] watcher syncResult config is： " + self.sync_watcher_result)
            print("[lab] watcher ignoreResult config is： " + self.ignore_watcher_result)

            try:
                self.scan_code = conf.get("Android", "needScanCode")
            except Exception as e:
                print("needScanCode config is not setted")

            try:
                self.ignore_ctest_package = conf.get('APP', 'ignore_ctest_package')
            except Exception as e:
                print("ignore_ctest_package is not setted, packageName is setted by ctest")

            try:
                self.upload_video = conf.get('UILab', "upload_video")
            except Exception as e:
                print("upload_video config is " + self.upload_video)

            try:
                self.headless = conf.get('Web', "headless") == "True"
            except Exception as e:
                print("headless config is " + str(self.headless))

            try:
                self.ignore_https_errors = conf.get('Web', "ignore_https_errors") == "True"
            except Exception as e:
                print("ignore_https_errors config is " + str(self.ignore_https_errors))

            try:
                self.bypass_csp = conf.get('Web', "bypass_csp") == "True"
            except Exception as e:
                print("bypass_csp config is " + str(self.bypass_csp))

            try:
                self.slow_mo = int(conf.get('Web', "slow_mo"))
            except Exception as e:
                print("slow_mo config is " + str(self.slow_mo))

            try:
                self.web_is_mobile = conf.get('Web', "web_is_mobile") == "True"
            except Exception as e:
                print("web_is_mobile config is " + str(self.web_is_mobile))

            try:
                self.device_scale_factor = int(conf.get('Web', "device_scale_factor"))
            except Exception as e:
                print("device_scale_factor config is " + str(self.device_scale_factor))

            try:
                self.viewport = {"width": int(conf.get('Web', "viewport_width")), "height": int(conf.get('Web', "viewport_height"))}
                self.no_viewport = False
            except Exception as e:
                print("viewport config is " + str(self.viewport))

            try:
                self.web_default_timeout = int(conf.get('Web', "web_default_timeout"))
            except Exception as e:
                print("web_default_timeout config is " + str(self.web_default_timeout))

            try:
                self.isClearApp = conf.get("UILab", "isClearApp")
            except Exception as e:
                print("Read labconfig.ini Exception:" + str(e))
            ctask_id = Labconfig.get_ctaskid()
            if ctask_id is not None and int(ctask_id) > 0 and not Labconfig.getAppPerfProject():
                Labconfig.set_watcher_enabled('True')
                if len(self.package) > 0:
                    self.setPackageName(self.package)
                return
            else:
                try:
                    run_id = os.environ['runId']
                except Exception:
                    if not self._watcher_debug.lower() == 'true':
                        self._watcher_enabled = 'False'

    @classmethod
    def set_wacher_config(cls, param_file_path):
        if param_file_path is not None and os.path.exists(param_file_path):
            try:
                f = open(param_file_path, "r", encoding='utf-8')
                args_content = f.read()
                args = json.loads(args_content)
                try:
                    if args.get("watcher", None) is not None and args["watcher"] == '1':
                        cls.set_watcher_enabled('True')
                    else:
                        cls.set_watcher_enabled('False')
                except Exception:
                    cls.set_watcher_enabled('False')
                    print("Read watcher_enabled failed")
            except Exception as e:
                print("reading uiAutoArgs.json error!!!")
        else:
            print("uiAutoArgs.json not exist!!!")

    @classmethod
    def setAppVersion(cls, param_file_path):
        if param_file_path is not None and os.path.exists(param_file_path):
            try:
                f = open(param_file_path, "r", encoding='utf-8')
                args_content = f.read()
                args = json.loads(args_content)
                try:
                    if args.get("versionName", None) is not None:
                        cls.appversion = args["versionName"]
                except Exception:
                    print("get app version name error!")
            except Exception as e:
                print("reading uiAutoArgs.json error!!!")
        else:
            print("uiAutoArgs.json not exist!!!")

    # 统一处理mpaas下发的args信息
    @classmethod
    def set_config_data(cls, param_file_path):
        if param_file_path is not None and os.path.exists(param_file_path):
            try:
                f = open(param_file_path, "r", encoding='utf-8')
                args_content = f.read()
                args = json.loads(args_content)
                print("uiAutoArgs参数为：{}".format(args_content))
                try:
                    if args.get("package", None) is not None and len(args["package"]) > 0 and not Labconfig.get_ignore_ctest_package() == "True" and not Labconfig.getAppPerfProject():
                        cls.setPackage(args["package"])
                        cls.setPackageName(args["package"])
                        if args["package"] == "ctrip.english":
                            cls.appid = "37"
                            cls.set_is_debug_package("False")
                            args["activityName"] = "com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity"
                        elif args["package"] == "ctrip.english.debug":
                            cls.set_is_debug_package("True")
                            cls.appid = "37"
                            args["activityName"] = "com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity"
                        elif args["package"] == "ctrip.android.view":
                            cls.appid = "99999999"
                            args["activityName"] = "ctrip.business.splash.CtripSplashActivity"
                        elif args["package"] == "ctrip.android.view.debug":
                            cls.appid = "99999999"
                            args["activityName"] = "ctrip.business.splash.CtripSplashActivity"
                except Exception:
                    print("Read package config failed")

                try:
                    if args.get("activityName", None) is not None and len(args["activityName"]) > 0:
                        activity = args["activityName"]
                        cls.set_activity(activity)
                except Exception:
                    print("Read activityName failed")
                # 处理args中extension相关参数
                try:
                    if args.get("extention", None) is not None and len(args["extention"]) > 0:
                        extention = json.loads(args["extention"])
                        cls.set_extention(extention)
                        try:
                            cls.setCrnChannelBuildle(extention["crnChannel"])
                        except Exception:
                            print("crnBuildles is null")
                        try:
                            if extention["sharkList"] is not None:
                                cls.setSharkList(extention["sharkList"])
                        except Exception:
                            print("shark info is null")
                        try:
                            # 要保证在最后设置，避免被覆盖
                            cls.aiGenerateExtension =  json.loads(extention["aiGenerateExtension"])
                            print("aiGenerateExtension:{}".format(cls.aiGenerateExtension))
                            if cls.aiGenerateExtension.get("activityName"):
                                cls.activity = cls.aiGenerateExtension["activityName"]
                                print("aiGenerateExtension activity is {}".format(cls.activity))
                            if cls.aiGenerateExtension.get("appId"):
                                cls.appid = cls.aiGenerateExtension["appId"]
                                print("aiGenerateExtension appId is {}".format(cls.appid))
                            if cls.aiGenerateExtension.get("account"):
                                cls.account = cls.aiGenerateExtension["account"]
                                print("aiGenerateExtension account is {}".format(cls.account))
                            if cls.aiGenerateExtension.get("password"):
                                cls.password = cls.aiGenerateExtension["password"]
                                print("aiGenerateExtension password is {}".format(cls.password))
                            if cls.aiGenerateExtension.get("packageName"):
                                cls.packageName = cls.aiGenerateExtension["packageName"]
                                print("aiGenerateExtension packageName is {}".format(cls.packageName))
                        except Exception:
                            print("aiGenerateExtension is null")
                        # 判断是否下发captain环境信息
                        try:
                            if extention["pipelineExt"]:
                                try:
                                    pipeline_info = json.loads(extention["pipelineExt"])
                                    if pipeline_info["subEnvName"]:
                                        cls.pipeline_env = pipeline_info["subEnvName"]
                                        print("pipeline_env运行环境为：{}".format(cls.pipeline_env))
                                except Exception:
                                    print("pipelineExt 不是json格式，解析失败！")
                        except Exception:
                            print("pipelineExt 解析异常！")
                except Exception:
                    print("extention is not setted!!!")
                #判断是否走mpss新流程
                try:
                    if args.get("useNewVersion", None) is not None and args["useNewVersion"]:
                        cls._is_mpass = True
                        if args.get("deviceType", None) is not None and args["deviceType"] == "Simulator":
                            DeviceInfo.set_is_simulator(1)
                            cls.mpass_device_type = 1  #暂仅用于mpass判断是否录屏
                            if args.get("port", None) is not None and args["port"]:
                                DeviceInfo.set_port(args["port"])
                except Exception:
                    print("Read is_mpaas failed")
                #读取mpaas下发的增量信息，传给公共CtestCommon在启动时进行增量下载
                try:
                    if args.get("module", None) and args.get("buildId", None):
                        cls.module_name = args["module"]
                        cls.module_id = args["buildId"]
                        print(f'mpaas下发增量信息为module_name:{cls.module_name},module_id：{cls.module_id}')
                except Exception:
                    print("Read module info failed")
                # 读取mpaas下发的主taskId信息，任务触发参数中的taskId为子taskId
                try:
                    if args.get("mainTaskId", None):
                        cls.mpaas_main_taskid = args["mainTaskId"]
                        print(f'mpaas下发主taskId信息为mainTaskId:{cls.mpaas_main_taskid}')
                except Exception:
                    print("Read mainTaskId info failed")
            except Exception as e:
                print("reading uiAutoArgs.json error!!!")
        else:
            print("uiAutoArgs.json not exist!!!")

    @classmethod
    def setCrnChannelBuildle(cls, data: str):
        cls.channelBundles = json.loads(data)

    @classmethod
    def getCrnChannelBuildle(cls):
        return cls.channelBundles

    @classmethod
    def getAppversion(cls):
        return cls.appversion

    @classmethod
    def getPlatformEnv(cls):
        return cls.platformEnv

    @classmethod
    def setPlatformEnv(cls, platformEnv):
        cls.platformEnv = platformEnv

    @classmethod
    def getIsChangEnv(cls):
        return cls._is_changEnv

    @classmethod
    def setIsChangEnv(cls, isChangEnv):
        cls._is_changEnv = isChangEnv

    @classmethod
    def getChangEnv(cls):
        return cls.changEnv

    @classmethod
    def setChangEnv(cls, changEnv):
        cls.changEnv = changEnv

    @classmethod
    def getIsClearApp(cls):
        return cls.isClearApp

    @classmethod
    def setIsClearApp(cls, isClearApp):
        cls.isClearApp = isClearApp

    @classmethod
    def getGroup(cls):
        return cls.group

    @classmethod
    def setGroup(cls, group):
        cls.group = group

    @classmethod
    def setPackageNameList(cls, packageNameList):
        cls.packageNameList = packageNameList

    @classmethod
    def getPackageNameList(cls):
        return cls.packageNameList

    @classmethod
    def setOpenAppPerf(cls, openAppPerf):
        cls.openAppPerf = openAppPerf

    @classmethod
    def getOpenAppPerf(cls):
        return cls.openAppPerf

    @classmethod
    def setPackageName(cls, packageName):
        cls.packageName = packageName

    @classmethod
    def getPackageName(cls):
        return cls.packageName

    @classmethod
    def getIosPackageName(cls):
        return cls.iosPackageName

    @classmethod
    def getPackage(cls):
        return cls.package

    @classmethod
    def setPackage(cls, package):
        cls.package = package

    @classmethod
    def set_listen_port(cls, listen_port):
        cls.listen_port = listen_port

    @classmethod
    def get_listen_port(cls):
        return cls.listen_port

    @classmethod
    def getIsLogJavis(cls):
        return cls.isLogJavis

    @classmethod
    def getIsLogLab(cls):
        return cls.isLogLab

    @classmethod
    def setRootPath(cls, path):
        cls.root_path = path

    @classmethod
    def getRootPath(cls):
        return cls.root_path

    @classmethod
    def getIsStopApp(cls):
        return cls.isStopApp

    @classmethod
    def getCoverageRunId(cls):
        return cls.coverageRunId

    @classmethod
    def setCoverageRunId(cls, coverageRunId):
        cls.coverageRunId = coverageRunId

    @classmethod
    def get_headless(cls):
        return cls.headless

    @classmethod
    def getAppid(cls):
        return cls.appid

    @classmethod
    def is_coverage(cls):
        return cls._is_coverage

    @classmethod
    def set_path(cls, path):
        cls.path = path

    @classmethod
    def get_path(cls):
        return cls.path

    @classmethod
    def set_iscoverage(cls, iscoverage):
        cls._is_coverage = iscoverage

    @classmethod
    def is_watcher_enabled(cls):
        return cls._watcher_enabled

    @classmethod
    def set_watcher_enabled(cls, watcher_enabled):
        cls._watcher_enabled = watcher_enabled

    @classmethod
    def is_watcher_debug(cls):
        return cls._watcher_debug

    @classmethod
    def set_watcher_debug(cls, _watcher_debug):
        cls._watcher_debug = _watcher_debug

    @classmethod
    def is_runId_debug(cls):
        return cls.runId_debug

    @classmethod
    def set_runId_debug(cls, runId_debug):
        cls.runId_debug = runId_debug

    @classmethod
    def setPackage(cls, package):
        cls.package = package

    @classmethod
    def getPackage(cls):
        return cls.package

    @classmethod
    def setAppurl(cls, appUrl):
        cls.appUrl = appUrl

    @classmethod
    def getAppurl(cls):
        return cls.appUrl

    @classmethod
    def get_ignore_https_errors(cls):
        return cls.ignore_https_errors

    @classmethod
    def set_ignore_https_errors(cls, ignore_https_errors):
        cls.ignore_https_errors = ignore_https_errors

    @classmethod
    def get_bypass_csp(cls):
        return cls.bypass_csp

    @classmethod
    def set_bypass_csp(cls, bypass_csp):
        cls.bypass_csp = bypass_csp

    @classmethod
    def get_slow_mo(cls):
        return cls.slow_mo

    @classmethod
    def set_slow_mo(cls, slow_mo):
        cls.slow_mo = slow_mo

    @classmethod
    def get_web_is_mobile(cls):
        return cls.web_is_mobile

    @classmethod
    def set_web_is_mobile(cls, web_is_mobile):
        cls.web_is_mobile = web_is_mobile

    @classmethod
    def get_device_scale_factor(cls):
        return cls.device_scale_factor

    @classmethod
    def set_device_scale_factor(cls, device_scale_factor):
        cls.device_scale_factor = device_scale_factor

    @classmethod
    def get_viewport(cls):
        return cls.viewport

    @classmethod
    def set_viewport(cls, viewport):
        cls.viewport = viewport

    @classmethod
    def get_no_viewport(cls):
        return cls.no_viewport

    @classmethod
    def get_web_default_timeout(cls):
        return cls.web_default_timeout

    @classmethod
    def set_web_default_timeout(cls, web_default_timeout):
        cls.web_default_timeout = web_default_timeout

    @classmethod
    def get_web_cookies(cls):
        return cls.web_cookies

    @classmethod
    def set_web_cookies(cls, web_cookies):
        cls.web_cookies = web_cookies

    @classmethod
    def get_custom_headers(cls):
        return cls.custom_headers

    @classmethod
    def set_custom_headers(cls, custom_headers):
        cls.custom_headers = custom_headers

    @classmethod
    def get_ctaskid(cls):
        return cls._ctaskid

    @classmethod
    def get_is_mpass(cls):
        return cls._is_mpass

    @classmethod
    def get_mpass_device_type(cls):
        return cls.mpass_device_type

    @classmethod
    def set_ctaskid(cls, taskid):
        cls._ctaskid = taskid

    @classmethod
    def get_is_debug_package(cls):
        return cls.isDebugPackage

    @classmethod
    def set_is_debug_package(cls, isDebugPackage):
        cls.isDebugPackage = isDebugPackage

    @classmethod
    def is_debug_package(self, isDebugPackage):
        ctask_id = Labconfig.get_ctaskid()
        if ctask_id is not None and int(ctask_id) > 0:
            package_name = self.package
            if package_name == "ctrip.english":
                return "False"
            elif package_name == "ctrip.english.debug":
                return "True"
        else:
            try:
                package_name = os.environ['app'].rsplit("/", 1)[1]
            except Exception as e:
                return isDebugPackage
        if package_name is not None and package_name.startswith("IBU"):
            if "Debug" in package_name:
                return "True"
            else:
                return "False"
        else:
            return isDebugPackage

    @classmethod
    def set_appid(self, appid):
        self.appid = appid

    @classmethod
    def get_config_file_path(cls):
        return cls.config_file_path

    @classmethod
    def set_ticket(cls, ticket):
        cls.ticket = ticket

    @classmethod
    def get_ticket(cls):
        return cls.ticket
    
    @classmethod
    def set_duid(cls, duid):
        cls.duid = duid

    @classmethod
    def get_duid(cls):
        return cls.duid

    @classmethod
    def set_config_file_path(cls, config_file_path):
        cls.config_file_path = config_file_path

    @classmethod
    def get_configEnv(cls):
        return cls.configEnv

    @classmethod
    def get_encode_configEnv(cls):
        return str(base64.b64encode(cls.configEnv.encode('utf-8')), 'utf-8')

    @classmethod
    def set_configEnv(cls, configEnv):
        if len(json.loads(configEnv)) == 0:
            return None
        cls.configEnv = configEnv

    @classmethod
    def get_activity(cls):
        return cls.activity

    @classmethod
    def set_activity(cls, activity):
        cls.activity = activity

    @classmethod
    def set_sync_watcher_result(cls, sync_watcher_result):
        cls.sync_watcher_result = sync_watcher_result

    @classmethod
    def get_sync_watcher_result(cls):
        return cls.sync_watcher_result

    @classmethod
    def set_ignore_watcher_result(cls, ignore_watcher_result):
        cls.ignore_watcher_result = ignore_watcher_result

    @classmethod
    def get_ignore_watcher_result(cls):
        return cls.ignore_watcher_result

    @classmethod
    def set_ignore_ctest_package(cls, ignore_ctest_package):
        cls.ignore_ctest_package = ignore_ctest_package

    @classmethod
    def get_ignore_ctest_package(cls):
        return cls.ignore_ctest_package

    @classmethod
    def get_upload_video(cls):
        return cls.upload_video

    @classmethod
    def set_upload_video(cls, upload_video):
        cls.upload_video = upload_video

    @classmethod
    def get_extension(cls):
        return cls.json_extension

    @classmethod
    def set_extension(cls, extension):
        cls.json_extension = extension

    @classmethod
    def get_extention(cls):
        return cls.extention

    @classmethod
    def set_extention(cls, extention):
        cls.extention = extention

    @classmethod
    def get_scan_code(cls):
        return cls.scan_code

    @classmethod
    def set_scan_code(cls, scan_code):
        cls.scan_code = scan_code

    @classmethod
    def get_mthemis_log_path(cls):
        return cls.mthemis_log_path

    @classmethod
    def set_mthemis_log_path(cls, mthemis_log_path):
        cls.mthemis_log_path = mthemis_log_path

    @classmethod
    def get_firewall_log_path(cls):
        return cls.firewall_log_path

    @classmethod
    def set_firewall_log_path(cls, firewall_log_path):
        cls.firewall_log_path = firewall_log_path

    @classmethod
    def get_crnPerformance(cls):
        return cls.crnPerformance

    @classmethod
    def set_crnPerformance(cls, crnPerformance):
        cls.crnPerformance = crnPerformance

    @classmethod
    def set_caseTimeOut(cls, caseTimeout):
        cls.caseTimeout = caseTimeout

    @classmethod
    def get_caseTimeOut(cls):
        return cls.caseTimeout

    @classmethod
    def set_finalTimeOut(cls, final_timeout):
        cls.final_timeout = final_timeout

    @classmethod
    def get_finalTimeOut(cls):
        return cls.final_timeout

    @classmethod
    def set_ai_agent_debug(cls, ai_agent_debug):
        cls.ai_agent_debug = ai_agent_debug

    @classmethod
    def get_ai_agent_debug(cls):
        return cls.ai_agent_debug

    @classmethod
    def set_ai_agent_result(cls, ai_agent_result):
        cls.ai_agent_result = ai_agent_result

    @classmethod
    def get_ai_agent_result(cls):
        return cls.ai_agent_result

    @classmethod
    def set_label_id(cls, label_id):
        cls.label_id = label_id

    @classmethod
    def get_label_id(cls):
        return cls.label_id

    @classmethod
    def set_trace_log_id(cls, trace_log_id):
        cls.trace_log_id = trace_log_id

    @classmethod
    def get_trace_log_id(cls):
        return cls.trace_log_id

    @classmethod
    def set_case_platform(cls, case_platform):
        cls.case_platform = case_platform

    @classmethod
    def get_case_platform(cls):
        return cls.case_platform

    @classmethod
    def get_loginInfo(cls):
        return cls.loginInfo

    @classmethod
    def set_loginInfo(cls, loginInfo):
        cls.loginInfo = loginInfo

    @classmethod
    def get_openUrl(cls):
        return cls.openUrl

    @classmethod
    def set_openUrl(cls, openUrl):
        cls.openUrl = openUrl

    @classmethod
    def getIsCheckTraceInfo(cls):
        return cls.isCheckTraceInfo

    @classmethod
    def setIsCheckTraceInfo(cls, isCheckTraceInfo):
        cls.isCheckTraceInfo = isCheckTraceInfo

    @classmethod
    def getIsHotelWirelessAppGroup(cls):
        return cls.isHotelWirelessAppGroup

    @classmethod
    def getOpenLocalMock(cls):
        return cls.openLocalMock

    @classmethod
    def get_breakSoaHookLogin(cls):
        return cls.breakSoaHookLogin

    @classmethod
    def set_breakSoaHookLogin(cls, breakSoaHookLogin):
        cls.breakSoaHookLogin = breakSoaHookLogin

    @classmethod
    def getMainlandCity(cls):
        return cls.mainlandCity

    @classmethod
    def getOverseaCity(cls):
        return cls.overseaCity

    @classmethod
    def getOverseaDetailCity(cls):
        return cls.overseaDetailCity

    @classmethod
    def getRunCount(cls):
        return cls.runCount

    @classmethod
    def setAppPerfProject(cls, isAppPerfProject):
        cls.isAppPerfProject = isAppPerfProject

    @classmethod
    def getAppPerfProject(cls):
        return cls.isAppPerfProject

    @classmethod
    def setAwsInstanceIds(cls, awsInstanceIds):
        cls.awsInstanceIds = awsInstanceIds

    @classmethod
    def getAwsInstanceIds(cls):
        return cls.awsInstanceIds

    @classmethod
    def setAwsClient(cls, awsClient):
        cls.awsClient = awsClient

    @classmethod
    def getAwsClient(cls):
        return cls.awsClient

    @classmethod
    def setAppperfAwsType(cls, appperfAwsType):
        cls.appperfAwsType = appperfAwsType

    @classmethod
    def getAppperfAwsType(cls):
        return cls.appperfAwsType

    # @classmethod
    # def set_env(cls, env):
    #     cls.env = env
    #
    # @classmethod
    # def get_env(cls):
    #     return cls.env
    #
    # @classmethod
    # def set_platform(cls, platform):
    #     cls.platform = platform
    #
    # @classmethod
    # def get_platform(cls):
    #     return cls.platform

    @classmethod
    def getLocalMockStatus(cls):
        return cls.localMockStatus

    @classmethod
    def setLocalMockStatus(cls, localMockStatus):
        cls.localMockStatus = localMockStatus

    @classmethod
    def setFlutterPackageNameList(cls, flutterPackageNameList):
        cls.flutterPackageNameList = flutterPackageNameList

    @classmethod
    def getFlutterPackageNameList(cls):
        return cls.flutterPackageNameList

    @classmethod
    def setIsFlutterPackage(cls, isFlutterPackage):
        cls.isFlutterPackage = isFlutterPackage

    @classmethod
    def getIsFlutterPackage(cls):
        return cls.isFlutterPackage

    @classmethod
    def setFlutterPackage(cls, flutterPackage):
        cls.flutterPackage = flutterPackage

    @classmethod
    def getFlutterPackage(cls):
        return cls.flutterPackage

    @classmethod
    def setRequestCount(cls, requestCount):
        cls.requestCount = requestCount

    @classmethod
    def getRequestCount(cls):
        return cls.requestCount

    @classmethod
    def setTimeSplitToFramework(cls, timeSplitToFramework):
        cls.timeSplitToFramework = timeSplitToFramework

    @classmethod
    def getTimeSplitToFramework(cls):
        return cls.timeSplitToFramework

    @classmethod
    def setTimeSplitToHotelPage(cls, timeSplitToHotelPage):
        cls.timeSplitToHotelPage = timeSplitToHotelPage

    @classmethod
    def getTimeSplitToHotelPage(cls):
        return cls.timeSplitToHotelPage

    @classmethod
    def setTimeSplitToCase(cls, timeSplitToCase):
        cls.timeSplitToCase = timeSplitToCase

    @classmethod
    def getTimeSplitToCase(cls):
        return cls.timeSplitToCase

    @classmethod
    def setTimeToFistWakeUp(cls, timeToFistWakeUp):
        cls.timeToFistWakeUp = timeToFistWakeUp

    @classmethod
    def getTimeToFistWakeUp(cls):
        return cls.timeToFistWakeUp

    @classmethod
    def setTimeSplitToLoadPage(cls, timeSplitToLoadPage):
        cls.timeSplitToLoadPage = timeSplitToLoadPage

    @classmethod
    def getTimeSplitToLoadPage(cls):
        return cls.timeSplitToLoadPage

    @classmethod
    def setSharkList(cls, sharkList):
        cls.sharkList = sharkList

    @classmethod
    def getSharkList(cls):
        return cls.sharkList

    @classmethod
    def set_need_logout(cls, need_logout):
        cls.need_logout = need_logout

    @classmethod
    def get_need_logout(cls):
        return cls.need_logout

    @classmethod
    def correct_mpass_platform(cls, platform, automation_type=None):
        # 调用这个方法的时候如果传入automation_type，则优先使用automation_type，否则使用suitPlatform
        if automation_type:
            mpass_platform = automation_type
        else:
            mpass_platform = Labconfig.suitPlatform
        # 用于校正platform，当下发为3时，根据platform区分C的H5和T的H5
        if platform < 3:
            if mpass_platform == "web" and platform == 1:
                platform = 5
            elif mpass_platform == "web" and platform == 2:
                platform = 6
        else:
            # 用于校正platform，当下发为1时，根据platform区分C的APP和T的APP，下发为2时IOS平台to do
            if mpass_platform == "android" and platform == 5:
                platform = 1
            elif mpass_platform == "android" and platform == 6:
                platform = 2
        return platform

    @classmethod
    def set_project_root_path(cls, current_file):
        """
        设置项目根路径

        通过向上遍历找到`requirements.txt`文件所在路径，最多遍历五次退出，
        找到后将路径设置为Labconfig的路径。

        参数:
        current_file (str): 调用文件的路径 __file__
        """
        if isinstance(current_file, str):
            path = current_file
        else:
            path = os.path.dirname(os.path.abspath(current_file))
        count = 0
        while not os.path.exists(os.path.join(path, 'requirements.txt')):
            path = os.path.dirname(path)
            if count > 5:
                break
            count += 1
        cls.set_path(path)
