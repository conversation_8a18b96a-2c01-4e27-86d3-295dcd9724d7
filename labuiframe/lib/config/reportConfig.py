import json
import shutil
from WirelessPlatformReport import render

class CaseInfo:
    upload_log_dir: str
    case_abs_path: str
    path: str
    title: str
    author: str
    desc: str
    run_start: float
    run_end: float
    records: list
    playwright: str

class ReportConfig:
    def __init__(self):
        self.reportJsonForWeb = {
            "info": {
                "path": "",
                "title": "",
                "author": "",
                "desc": ""
            },
            "lang": "zh",
            "run_start": 0.0,
            "run_end": 0.0,
            "records": [],
            "playwright": "trace.zip",
            "test_result": True
        }

    def commonReportJsonForWeb(self, path, case_info: CaseInfo):
        # 修改reportJsonForWeb的内容后写入path
        self.reportJsonForWeb["info"]["path"] = case_info.get("path")
        self.reportJsonForWeb["info"]["title"] = case_info.get("title")
        self.reportJsonForWeb["info"]["author"] = case_info.get("author")
        self.reportJsonForWeb["info"]["desc"] = case_info.get("desc")
        self.reportJsonForWeb["run_start"] = case_info.get("run_start")
        self.reportJsonForWeb["run_end"] = case_info.get("run_end")
        self.reportJsonForWeb["records"] = case_info.get("records")
        self.reportJsonForWeb["playwright"] = case_info.get("playwright")
        self.reportJsonForWeb["test_result"] = case_info.get("test_result")
        # 将case从case_abs_path复制到upload_log_dir
        shutil.copy(case_info.get("case_abs_path"), case_info.get("upload_log_dir"))
        # 将reportJsonForWeb写入path
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.reportJsonForWeb, f, ensure_ascii=False, indent=4)
        # html需要改成log.html与App的保持一致
        return render(data_path=path, output_file="log.html")

