import json

class FlutterFinder(object):
    @staticmethod
    def by_ancestor(serialized_finder, matching, match_root=False):
        return FlutterFinder._by_ancestor_or_descendant(
            type = 'Ancestor',
            serialized_finder = serialized_finder,
            matching = matching,
            match_root=match_root
        )
        
    @staticmethod
    def by_descendant(serialized_finder, matching, match_root=False):
        return FlutterFinder._by_ancestor_or_descendant(
            type = 'Descendant',
            serialized_finder = serialized_finder,
            matching = matching,
            match_root = match_root
        )
        
    @staticmethod
    def by_semantics_label(label, isRegExp=False):
        return FlutterFinder._serialize(dict(
            finderType = 'BySemanticsLabel',
            isRegExp = isRegExp,
            label = label
        ))    
    
    @staticmethod
    def by_tooltip(text):
        return FlutterFinder._serialize(dict(
            finderType = 'ByTooltipMessage',
            text = text
        ))
    
    
    @staticmethod
    def by_text(text):
        return FlutterFinder._serialize(dict(
            finderType = 'ByText',
            text = text
        ))
    
    @staticmethod
    def by_type(type_):
        return FlutterFinder._serialize(dict(
            finderType = 'ByType',
            type = type_
        ))
    
    @staticmethod
    def by_value_key(key):
        return FlutterFinder._serialize(dict(
            finderType = 'ByValueKey',
            keyValueString = key,
            keyValueType = 'String' if isinstance(key, str) else 'int'
        ))
    
    @staticmethod
    def page_back():
        return FlutterFinder._serialize(dict(
            finderType = 'PageBack'
        ))

    @staticmethod
    def _serialize(finder_dict):
        return finder_dict

    @staticmethod
    def _by_ancestor_or_descendant(type_, serialized_finder, matching, match_root=False):
        param = dict(finderType= type_, matchRoot=match_root)
        
        try:
            finder = json.dump(serialized_finder)
        except:
            finder = dict()
        for finder_key, finder_value in finder.items():
            param['of_{}'.format(finder_key)] = finder_value
            
        try:
            matching = json.dumps(matching)
        except:
            matching = dict()
        for matching_key, matching_value in matching.items():
            param['matching_{}'.format(matching_key)] = matching_value
            
        return FlutterFinder._serialize(param)
