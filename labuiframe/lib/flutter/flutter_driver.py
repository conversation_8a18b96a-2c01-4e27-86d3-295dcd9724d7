import websocket
import re
import json
from labuiframe.lib.flutter.finder import FlutterFinder

from .offType import *
import time
import threading
from airtest.core.android.adb import ADB
from labuiframe.lib.utils.capture import Capture
from airtest.core.helper import logwrap

from ..utils.device import DeviceInfo


class FlutterDriver:
    device = None
    observatory = None
    isolates = None

    def __init__(self, poco):
        self.ws = None
        self.cmt_capture = Capture()
        t = threading.Thread(target=self.connect, args=(poco, ))
        t.setDaemon(True)
        t.start()
        time.sleep(2)
        # self.connect(poco)
    def connect(self, poco):
        logcat_info = ADB(serialno=DeviceInfo.deviceName).logcat(read_timeout = 1)
        for line in logcat_info:
            line = line.decode('utf-8', 'ignore')
            if "CTFlutterWS: " in line:
                self.observatory = (line.split("CTFlutterWS: "))[-1]
                self.observatory = self.observatory.replace("http", "ws").replace('\r', '').replace('\n', '') + "ws"
                print("observatory:" + self.observatory)
                self.logcat_end()
                break
        self.id = 0
        time.sleep(2)
        self.initObservatory()
        time.sleep(3)

    @classmethod
    def logcat_end(self):
        process_info = None
        try:
            adb = ADB(serialno=DeviceInfo.device.serialno)
            adb.cmd("logcat -c")
            shell_command = "shell ps -A | grep logcat"
            process_info = adb.cmd(shell_command)
        except Exception as e:
            print(e)
            pass
        if process_info is not None and len(process_info) > 0:
            info_list = process_info.split(" ")
            while "" in info_list:
                info_list.remove("")
            if len(info_list) > 1:
                shell_command = "kill -s 2 {}".format(info_list[1])
                p = adb.start_shell(shell_command)
                p.wait()

    def initObservatory(self):
        port = re.findall(r"ws://127.0.0.1:(\d+)/.+", self.observatory)
        if port:
            ADB(serialno=DeviceInfo.deviceName).start_cmd("forward tcp:%s tcp:%s" % (port[0], port[0]))
        self.ws = websocket.create_connection(self.observatory)
        self.ws.settimeout(30)
        self.ws.send('{"jsonrpc": "2.0","id":"0","method":"streamListen","params":{"streamId":"VM"}}')
        print(self.ws.recv())
        self.ws.send('{"jsonrpc": "2.0","id":"1","method":"streamListen","params":{"streamId":"Isolate"}}')
        print(self.ws.recv())
        self.ws.send('{"jsonrpc": "2.0","id":"2","method":"getVM","params":{}}')
        res = self.ws.recv()
        print(res)
        self.isolates = json.loads(res).get("result").get("isolates")[0].get("id")
        self.ws.send('{"jsonrpc": "2.0","id":"3","method":"getIsolate","params":{"isolateId":"%s"}}' % self.isolates)
        self.id = 4

    def send_message(self, method = "ext.flutter.driver", params={}, timeout=30):
        start = time.time()
        while time.time() - start < timeout:
            if self.observatory is not None and self.ws is not None and self.isolates is not None:
                break
        params['isolateId'] = self.isolates
        message = dict(jsonrpc='2.0', id = self.id, method = method, params = params)
        message = json.dumps(message)
        print("send message:" + str(message))
        self.ws.send(str(message))
        try:
            result = self.get_result(timeout)
        except Exception as e:
            return None
        self.id += 1
        if result is None:
            return None
        return result.get("result")

    def get_result(self, timeout = 120):
        start = time.time()
        line = self.ws.recv()
        print(line)
        while json.loads(line).get("result") is None and json.loads(line).get("error") is None:
            try:
                line = self.ws.recv()
                print(line)
            except Exception as e:
                pass
            if time.time() - start > timeout:
                raise Exception("未找到对应的Flutter元素，timeout={}".format(str(timeout)))
        while json.loads(line).get("id") != self.id:
            try:
                line = self.ws.recv()
                print(line)
            except Exception as e:
                pass
            if time.time() - start > timeout:
                raise Exception("未找到对应的Flutter元素，timeout={}".format(str(timeout)))

        return json.loads(line)

    def wait_for_appearence(self, params, timeout = 30):
        start = time.time()
        try:
            while time.time() - start <= timeout:
                result = self.exist(params, timeout=10)
                if result is not None and result:
                    return self
                else:
                    continue
            raise Exception("未找到对应的Flutter元素{}，timeout={}".format(str(params),str(timeout)))
        except Exception as e:
            raise Exception("未找到对应的Flutter元素{}，timeout={}".format(str(params),str(timeout)))

    def exist(self, params, timeout = 10):
        params['command'] = "waitFor"
        # flutter里的时间单位是ms
        params['timeout'] = timeout*1000
        try:
            result = self.send_message(params=params, timeout=20)
            if result is not None and not result.get("isError"):
                return True
            else:
                return False
        except Exception as e:
            return False

    @logwrap
    def click(self, params, timeout=10):
        params['command'] = "tap"
        # flutter里的时间单位是ms
        params['timeout'] = timeout*1000
        try:
            result = self.send_message(params = params, timeout=20)
            if result is not None and not result.get("isError"):
                return True
            else:
                return False
        except Exception as e:
            return False

    def ScrollIntoView(self, params, alighnment=0.5, timeout =10):
        params['command'] = "scrollIntoView"
        params['alignment'] = alighnment
        # flutter里的时间单位是ms
        params['timeout'] = timeout*1000
        self.send_message(params = params, timeout=20)

    def getText(self, params):
        params['command'] = 'get_text'
        result = self.send_message(params = params)
        if result.get("isError"):
            return None
        else:
            return result.get("response").get("text")

    @logwrap
    def enterText(self, text):
        params = {"command": "enter_text", "text": text}
        return self.send_message(params=params)

    def getTopLeft(self, params):
        params["offsetType"] = topLeft
        return self._getOffset(params)

    def getTopRight(self, params):
        params["offsetType"] = topRight
        return self._getOffset(params)

    def getBottomLeft(self, params):
        params["offsetType"] = bottomLeft
        return self._getOffset(params)

    def getBottomRight(self, params):
        params["offsetType"] = bottomRight
        return self._getOffset(params)

    @logwrap
    def getCenter(self, params):
        params["offsetType"] = center
        return self._getOffset(params)

    def _getOffset(self, params):
        params['command'] = "get_offset"
        result = self.send_message(params = params)
        if result.get("isError"):
            return None
        else:
            response = result.get("response")
            return (int(float(response["dx"])), int(float(response["dy"])))


    def getLayerTree(self):
        params = {"command": "get_layer_tree"}
        result = self.send_message(params=params)
        if result.get("isError"):
            return False
        else:
            return result.get("response")

    def getRenderObjectDiagnostics(self, params, subtreeDepth=0, includeProperties=True):
        params['command'] = 'get_diagnostics_tree'
        params['subtreeDepth'] = subtreeDepth
        params['includeProperties'] = includeProperties
        result = self.send_message(params=params)
        if result.get("isError"):
            return False
        else:
            return result.get("response")

    def getRenderTree(self):
        params = {"command": "get_render_tree"}
        result = self.send_message(params=params)
        if result.get("isError"):
            return False
        else:
            return result.get("response")

    def generate_finder_byType(self, name, type):
        finder = FlutterFinder()
        if type == "text":
            return finder.by_text(name)
        elif type == "key":
            return finder.by_value_key(name)
        elif type == "regExp":
            return finder.by_semantics_label(name,True)


    def clickFlutterElement(self,name, type, timeout=10):
        finder = self.generate_finder_byType(name, type)
        return self.click(finder,timeout=timeout)

    def findFlutterElement(self,name,type,timeout=10):
        finder = self.generate_finder_byType(name, type)
        return self.exist(finder,timeout=timeout)

    def dragFlutterElement(self,name,type,alighnment=0.5, timeout=10):
        finder = self.generate_finder_byType(name, type)
        return self.ScrollIntoView(finder,alighnment,timeout=timeout)

    def close_ws(self):
        self.ws.close()

    def searchFlutterElement(self,poco,name,type,distance=0.2, duration=2,timeout=10):
        print("滑动,并以flutter方法开始查找元素")
        i = 0
        while i <= timeout:
            if self.findFlutterElement(name, type, timeout=5):
                print("找到元素" + name)
                return True
            else:
                print("开始滑动查找"+name)
                poco.scroll("vertical",distance, duration)
                i = i+1
        print("找不到元素"+ name)
        return False
