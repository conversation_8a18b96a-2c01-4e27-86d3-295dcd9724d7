# coding=utf-8
from __future__ import print_function

import os
import inspect

from labuiframe.lib.case.app import AppTestCase
from pocounit.case import PocoTestCase
from pocounit.runner import PocoTestRunner
from airtest.core.android.adb import ADB
from pocounit.suite import PocoTestSuite
from labuiframe.lib.utils.device import DeviceInfo
from labuiframe.lib.utils.aws import Aws
from labuiframe.lib.config.labconfig import Labconfig
from labuiframe.lib.utils.commonAction import CommonAction
from pocounit.utils.misc import has_override
from labuiframe.lib.runner.UILabRunner import UILabRunner
from airtest.utils.snippet import exitfunc
import threading
import time
from labuiframe.lib.utils.MySqlConnect import MySqlConnect

CT_RESULT = "CTest-AirTest-RunTest：Finish"

def run(suite):
    if isinstance(suite, PocoTestCase):
        case = suite
        suite = PocoTestSuite()
        suite.addTest(case)
    runner = UILabRunner(verbosity=2)
    if not AppTestCase.isDebug():
        try:
            if suite.device != "":
                t1 = threading.Thread(target=checkConnecttion,args=(suite.device,))
                t1.setDaemon(True)
                t1.start()
            elif suite.simulatorDeviceIp != '':
                t1 = threading.Thread(target=checkConnecttion, args=(suite.simulatorDeviceIp,))
                t1.setDaemon(True)
                t1.start()
        except:
            print("无法上报心跳日志")
    result = runner.run(suite)
    global CT_RESULT
    if not result.wasSuccessful():
        CT_RESULT = "CTest-AirTest-RunTest：FAILED ("
        failed, errored = list(map(len, (result.failures, result.errors)))
        if failed:
            CT_RESULT += "failures=" + str(failed)
        if errored:
            if failed: CT_RESULT += ", "
            CT_RESULT += "errors=" + str(errored) + ")"
    else:
        CT_RESULT = "CTest-AirTest-RunTest：OK"
    # # aws任务完成终止实例操作
    # if Labconfig.getAppPerfProject() and Labconfig.getAppperfAwsType() == "AWS":
    #     # 终止实例
    #     Aws.terminateEC2Instance(Labconfig.getAwsClient(),Labconfig.getAwsInstanceIds())
    #     mysqlConnect = MySqlConnect()
    #     mysqlConnect.updateAppperfOnAwsExampleTable(Labconfig.getAwsInstanceIds()[0], 0, time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    #     print("AWS任务完成，终止实例")
    
    # 避免主线程退出，导致报告上传失败，加上判断等待Labconfig.threading_list所有线程结束，线程结束了不会自动从列表中移除，所以需要手动移除
    i = 20
    while Labconfig.threading_list:
        for thread in list(Labconfig.threading_list):
            if not thread.is_alive():
                Labconfig.threading_list.remove(thread)
        time.sleep(1)
        i -= 1
        if i == 0:
            print("启动的所有线程未结束，超时退出")
            break
    
    return result

def checkConnecttion(serialNo):
    while True:
        curTime = time.strftime("%H:%M:%S", time.localtime(time.time()))
        print("[{0}][{1}][{2}]心跳上报".format(curTime,Labconfig.get_ctaskid(),serialNo))
        time.sleep(5)

def exitmainfunc():
    exitfunc()
    # ADB(serialno=DeviceInfo.deviceName).cmd("forward --remove-all")
    time.sleep(1)
    print(CT_RESULT)
    # if (Labconfig.getIsHotelWirelessAppGroup() == "True" and not Labconfig.getAppPerfProject()):
    #     print("suit结束,告知埋点校验接口")
    #     req = {
    #         "taskId": Labconfig.get_ctaskid(),
    #         "suitFinish": True
    #     }
    #     CommonAction.caseTraceCheck(req)

threading._shutdown = exitmainfunc

def main():
    # testcase detection
    current_frame = inspect.currentframe()
    caller = current_frame.f_back
    test_case_filename = os.path.abspath(caller.f_code.co_filename)  # 脚本务必是绝对路径才行
    caller_scope = caller.f_locals
    print('this testcase filename is "{}".'.format(test_case_filename))

    # 这部分代码放到loader里
    Cases = [v for k, v in caller_scope.items()
             if type(v) is type
             and v is not PocoTestCase
             and issubclass(v, PocoTestCase)
             and has_override("runTest", v, PocoTestCase)
             ]

    suite = PocoTestSuite()
    for Case in Cases:
        case = Case()
        suite.addTest(case)

    runner = UILabRunner()
    result = runner.run(suite)

    if not result.wasSuccessful():
        exit(-1)

