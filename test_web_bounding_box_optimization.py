#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web端边界框绘制优化的脚本
"""

import sys
import os
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import random
import colorsys
from io import BytesIO

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_random_color(seq_index):
    """根据seq_index生成随机但一致的颜色"""
    # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
    random.seed(hash(str(seq_index)) % 2147483647)
    
    # 生成高饱和度、中等亮度的颜色，确保视觉效果好
    hue = random.random()  # 色相：0-1
    saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
    lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7
    
    # 转换HSL到RGB
    rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
    return tuple(int(c * 255) for c in rgb)

def get_contrasting_text_color(bg_color):
    """根据背景颜色获取对比度高的文本颜色"""
    # 计算背景颜色的亮度
    r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
    brightness = (r * 299 + g * 587 + b * 114) / 1000
    
    # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
    return (255, 255, 255) if brightness < 128 else (0, 0, 0)

def create_test_web_dom_tree():
    """创建一个模拟的Web DOM树结构"""
    return {
        "seq_index": 0,
        "tag": "body",
        "type": "element",
        "payload": {
            "rect": {"x": 0, "y": 0, "width": 1200, "height": 800},
            "text": ""
        },
        "children": [
            {
                "seq_index": 1,
                "tag": "header",
                "type": "element",
                "payload": {
                    "rect": {"x": 0, "y": 0, "width": 1200, "height": 80},
                    "text": "网站标题"
                },
                "children": [
                    {
                        "seq_index": 2,
                        "tag": "nav",
                        "type": "element",
                        "payload": {
                            "rect": {"x": 800, "y": 20, "width": 300, "height": 40},
                            "text": "导航菜单"
                        }
                    }
                ]
            },
            {
                "seq_index": 3,
                "tag": "main",
                "type": "element",
                "payload": {
                    "rect": {"x": 50, "y": 100, "width": 1100, "height": 600},
                    "text": ""
                },
                "children": [
                    {
                        "seq_index": 4,
                        "tag": "section",
                        "type": "element",
                        "payload": {
                            "rect": {"x": 70, "y": 120, "width": 500, "height": 200},
                            "text": "主要内容区域"
                        }
                    },
                    {
                        "seq_index": 5,
                        "tag": "aside",
                        "type": "element",
                        "payload": {
                            "rect": {"x": 600, "y": 120, "width": 300, "height": 400},
                            "text": "侧边栏"
                        },
                        "children": [
                            {
                                "seq_index": 6,
                                "tag": "div",
                                "type": "element",
                                "payload": {
                                    "rect": {"x": 620, "y": 140, "width": 260, "height": 80},
                                    "text": "广告位"
                                }
                            },
                            {
                                "seq_index": 7,
                                "tag": "div",
                                "type": "element",
                                "payload": {
                                    "rect": {"x": 620, "y": 240, "width": 260, "height": 120},
                                    "text": "推荐文章"
                                }
                            }
                        ]
                    },
                    {
                        "seq_index": 8,
                        "tag": "form",
                        "type": "element",
                        "payload": {
                            "rect": {"x": 70, "y": 350, "width": 500, "height": 150},
                            "text": ""
                        },
                        "children": [
                            {
                                "seq_index": 9,
                                "tag": "input",
                                "type": "element",
                                "payload": {
                                    "rect": {"x": 90, "y": 370, "width": 200, "height": 30},
                                    "text": "用户名输入框"
                                }
                            },
                            {
                                "seq_index": 10,
                                "tag": "input",
                                "type": "element",
                                "payload": {
                                    "rect": {"x": 90, "y": 420, "width": 200, "height": 30},
                                    "text": "密码输入框"
                                }
                            },
                            {
                                "seq_index": 11,
                                "tag": "button",
                                "type": "element",
                                "payload": {
                                    "rect": {"x": 320, "y": 420, "width": 100, "height": 30},
                                    "text": "登录"
                                }
                            }
                        ]
                    }
                ]
            },
            {
                "seq_index": 12,
                "tag": "footer",
                "type": "element",
                "payload": {
                    "rect": {"x": 0, "y": 720, "width": 1200, "height": 80},
                    "text": "版权信息"
                }
            }
        ]
    }

def draw_web_bounding_boxes(screenshot_bytes, dom_tree, viewport_width=1200, viewport_height=800):
    """模拟Web端边界框绘制"""
    # 将截图字节转换为PIL图像
    img = Image.open(BytesIO(screenshot_bytes))
    draw = ImageDraw.Draw(img, 'RGBA')  # 使用RGBA模式以支持透明度
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("Arial", 14)
    except:
        font = ImageFont.load_default()
    
    # 重置随机种子，确保颜色生成的一致性
    random.seed(42)
    
    # 递归绘制边界框
    draw_node_bounding_box(draw, dom_tree, viewport_width, viewport_height, font)
    
    return img

def draw_node_bounding_box(draw, node, viewport_width, viewport_height, font):
    """递归地为每个节点绘制边界框"""
    if "payload" in node and "rect" in node["payload"]:
        rect = node["payload"]["rect"]
        
        # 使用rect中的坐标和大小信息
        x1 = rect["x"]
        y1 = rect["y"]
        x2 = x1 + rect["width"]
        y2 = y1 + rect["height"]
        
        # 获取seq_index用于生成颜色和标签
        seq_index = node.get("seq_index", "")
        
        # 生成随机颜色
        border_color = generate_random_color(seq_index)
        
        # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
        draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)
        
        # 构建标签文本 - 包含seq_index和文本内容（如果有且不太长）
        label_text = str(seq_index)
        
        # 在边界框顶部绘制标签
        if label_text:
            try:
                # 限制标签文本总长度
                if len(label_text) > 25:
                    label_text = label_text[:22] + "..."
                
                # 使用字体计算实际文本大小
                try:
                    bbox = draw.textbbox((0, 0), label_text, font=font)
                    text_width = bbox[2] - bbox[0] + 6  # 添加padding
                    text_height = bbox[3] - bbox[1] + 4  # 添加padding
                except:
                    # 如果textbbox不可用，使用估算
                    text_width = len(label_text) * 8 + 6
                    text_height = 16
                
                # 确保标签不会超出屏幕边界
                label_x = x1
                if label_x + text_width > viewport_width:
                    label_x = viewport_width - text_width
                if label_x < 0:
                    label_x = 0
                
                # 确保标签不会超出屏幕顶部
                label_y = y1 - text_height
                if label_y < 0:
                    label_y = y2  # 如果顶部放不下，放到底部
                    if label_y + text_height > viewport_height:
                        label_y = viewport_height - text_height
                
                # 创建标签背景颜色（使用边界框颜色的半透明版本）
                bg_color = border_color + (200,)  # 添加alpha通道
                
                # 绘制标签背景
                draw.rectangle(
                    [label_x, label_y, label_x + text_width, label_y + text_height],
                    fill=bg_color
                )
                
                # 获取对比度高的文本颜色
                text_color = get_contrasting_text_color(border_color)
                
                # 绘制文本
                draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)
                
            except Exception as e:
                print(f"绘制标签失败: {str(e)}")
    
    # 递归处理子节点
    if "children" in node and node["children"]:
        for child in node["children"]:
            draw_node_bounding_box(draw, child, viewport_width, viewport_height, font)

def create_test_web_screenshot():
    """创建一个模拟的Web页面截图"""
    # 创建一个1200x800的白色背景图像
    img = Image.new('RGB', (1200, 800), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制一些模拟的Web页面元素
    # 头部
    draw.rectangle([0, 0, 1200, 80], fill='lightblue', outline='blue')
    
    # 主要内容区域
    draw.rectangle([70, 120, 570, 320], fill='lightgray', outline='gray')
    
    # 侧边栏
    draw.rectangle([600, 120, 900, 520], fill='lightyellow', outline='orange')
    
    # 表单区域
    draw.rectangle([70, 350, 570, 500], fill='lightgreen', outline='green')
    
    # 底部
    draw.rectangle([0, 720, 1200, 800], fill='lightcoral', outline='red')
    
    # 转换为字节流
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def main():
    """主函数"""
    print("开始测试Web端边界框绘制优化...")
    
    # 创建模拟的Web页面截图
    screenshot_bytes = create_test_web_screenshot()
    
    # 创建模拟的DOM树
    dom_tree = create_test_web_dom_tree()
    
    # 绘制边界框
    annotated_img = draw_web_bounding_boxes(screenshot_bytes, dom_tree)
    
    # 确保output目录存在
    if not os.path.exists("output"):
        os.makedirs("output")
    
    # 保存测试图像
    output_path = "output/test_web_bounding_boxes.png"
    annotated_img.save(output_path)
    
    print(f"Web端测试图像已保存到: {output_path}")
    print("Web端优化特性:")
    print("1. ✅ 每个边界框使用不同的随机颜色")
    print("2. ✅ 标签包含seq_index和文本内容")
    print("3. ✅ 标签背景颜色与边界框颜色匹配")
    print("4. ✅ 文本颜色根据背景自动调整对比度")
    print("5. ✅ 标签位置智能调整，避免超出屏幕边界")
    print("6. ✅ 边界框线条加粗，提高可见性")
    print("7. ✅ 适配Web端的rect坐标系统")
    
    # 测试颜色生成的一致性
    print("\n测试Web端颜色生成一致性:")
    for i in range(5):
        color1 = generate_random_color(i)
        color2 = generate_random_color(i)
        print(f"seq_index {i}: {color1} == {color2} -> {color1 == color2}")

if __name__ == "__main__":
    main()
