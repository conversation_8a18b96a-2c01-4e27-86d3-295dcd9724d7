#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 在所有导入之前禁用警告
import warnings
warnings.filterwarnings("ignore", category=Warning)

import urllib3
from urllib3.exceptions import InsecureRequestWarning
urllib3.disable_warnings(InsecureRequestWarning)

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import subprocess
from pathlib import Path
import threading
import re
import json
from labuiframe.lib.utils.debug_tool_utils import DebugToolUtils
import time

# 禁用特定的警告
warnings.filterwarnings("ignore", category=Warning)
# 特别禁用 RequestsDependencyWarning
try:
    from requests.packages.urllib3.exceptions import RequestsDependencyWarning
    warnings.filterwarnings("ignore", category=RequestsDependencyWarning)
except ImportError:
    pass

"""
# feat/yangyangpeng/test
# 2026年1月9日token到期 aRkxdo_xsXkCyK1mSbg-
# https://git.dev.sh.ctripcorp.com/hotel-mob/hotel-order-detail-ui-test/-/tree/feat/yangyangpeng/test?ref_type=heads
# 读取dist下whl文件的版本，例如：labuiframe-2001.1.9.110250-py3-none-any.whl，读取版本为2001.1.9.110250
# 调用gitlab接口修改feat/yangyangpeng/test分支的requirements.txt，将labuiframe==2001.1.9.110250改为labuiframe==2001.1.9.110250

# https://git.dev.sh.ctripcorp.com/uiautomationwithai/aigenerate-test
# main
# 2025年1月9日token到期 _ugnErSzTKS-hMz9VPFH
# 触发指定id的case进行AI生成


# 触发mpaas任务，获取指定jobid的任务执行结果，获取任务执行报告页url
# jobid=464， Trip-Online-UiLabTestjob
# jobid=463， Trip-H5-UiLabTestjob
# jobid=454， UiLabTestjob

1. 使用python+tkinter制作调试工具UI
2. 让用户自己选择是否使用conda，如果不使用conda默认使用当前终端的python环境
3. 功能如下：
    1. 本地使用setup-test.py构建包(构建前需要重置当前的python环境，并且需要清理之前构建的目录)
    2. 本地安装刚刚构建的whl包(安装前需要重置当前的python环境)
    3. 将构建的whl包上传到测试环境（上传前需要重新安装twine）
    4. 修改hotel-order-detail-ui-test仓库的feat/yangyangpeng/test分支的requirements.txt，将labuiframe==xxxx.xx.xx.xxxx改为labuiframe==xxxx.xx.xx.xxxx（读取dist下whl文件的版本，例如：labuiframe-2001.1.9.110250-py3-none-any.whl，读取版本为2001.1.9.110250）
    5. 触发mpaas任务，获取指定jobid的任务执行结果，获取任务执行报告页url
    6. 修改aigenerate-test仓库的main分支的requirements.txt，将labuiframe==xxxx.xx.xx.xxxx改为labuiframe==xxxx.xx.xx.xxxx（读取dist下whl文件的版本，例如：labuiframe-2001.1.9.110250-py3-none-any.whl，读取版本为2001.1.9.110250）
    7. 触发AI生成接口，获取AI生成结果
4. 第3点内主要是功能介绍，不代表UI界面菜单，你需要从一个专业测试开发程序员和专业交互设计师的角度，设计一个UI界面菜单，让用户可以方便的进行操作
"""

class DebugToolUI:
    def __init__(self, root):
        self.root = root
        self.root.title("测试调试工具")
        self.root.geometry("800x850")
        
        # 初始化工具类
        self.utils = DebugToolUtils()
        
        # 初始化任务ID记录
        self.current_task_ids = {}  # 格式: {job_id: [task_id1, task_id2, ...]}
        
        # 初始化任务窗口实例
        self.task_window = None
        
        # 初始化轮询状态
        self.polling = False
        self.polling_thread = None
        
        # 初始化资源监控状态
        self.monitoring = True
        self.monitor_thread = None
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置主题样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 创建无边框样式
        style.configure("Noborder.TLabelframe", borderwidth=0)
        
        # 配置根窗口的网格权重
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的网格权重
        self.main_frame.grid_rowconfigure(0, weight=0)  # 环境配置区域
        self.main_frame.grid_rowconfigure(1, weight=1)  # 功能区域
        self.main_frame.grid_rowconfigure(2, weight=0)  # 日志标题
        self.main_frame.grid_rowconfigure(3, weight=1)  # 日志区域
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # 环境配置区域
        self.create_env_section()
        
        # 功能区域
        self.create_function_section()
        
        # 日志输出区域
        self.create_log_section()
        
        # 启动资源监控
        self.start_resource_monitoring()
        
    def create_env_section(self):
        env_frame = ttk.LabelFrame(self.main_frame, text="环境配置", padding="5")
        env_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # 配置env_frame的列权重
        env_frame.grid_columnconfigure(4, weight=1)
        
        # Python环境信息
        info_frame = ttk.Frame(env_frame)
        info_frame.grid(row=0, column=0, columnspan=5, sticky=(tk.W, tk.E), pady=(0, 5))
        info_frame.grid_columnconfigure(1, weight=1)
        
        python_info = self.utils.get_python_info()
        
        # 创建信息标签
        info_text = (
            f"Python {python_info['version']}  •  "
            f"pip {python_info['pip']}  •  "
            f"setuptools {python_info['setuptools']}  •  "
            f"wheel {python_info['wheel']}"
        )
        ttk.Label(info_frame, text=info_text).grid(row=0, column=0, sticky=tk.W, padx=5)
        
        # 添加路径显示（右对齐）
        path_text = f"路径: {python_info['path']}"
        ttk.Label(info_frame, text=path_text, foreground='gray').grid(row=0, column=1, sticky=tk.E, padx=5)
        
        # 添加系统资源监控框架
        resource_frame = ttk.Frame(env_frame)
        resource_frame.grid(row=1, column=0, columnspan=5, sticky=(tk.W, tk.E), pady=(0, 5))
        resource_frame.grid_columnconfigure(1, weight=1)
        
        # 创建资源监控标签
        self.memory_label = ttk.Label(resource_frame, text="内存使用: 计算中...")
        self.memory_label.grid(row=0, column=0, sticky=tk.W, padx=5)
        
        self.cpu_label = ttk.Label(resource_frame, text="CPU使用: 计算中...")
        self.cpu_label.grid(row=0, column=1, sticky=tk.E, padx=5)
        
        # Conda选择
        conda_frame = ttk.Frame(env_frame)
        conda_frame.grid(row=2, column=0, columnspan=5, sticky=(tk.W, tk.E))
        
        ttk.Label(conda_frame, text="Python环境:").grid(row=0, column=0, padx=5)
        self.env_var = tk.StringVar(value="system")
        ttk.Radiobutton(conda_frame, text="系统Python", variable=self.env_var, 
                       value="system").grid(row=0, column=1)
        ttk.Radiobutton(conda_frame, text="Conda", variable=self.env_var,
                       value="conda").grid(row=0, column=2)
        
        # Conda环境名输入
        ttk.Label(conda_frame, text="Conda环境名:").grid(row=0, column=3, padx=5)
        self.conda_env = ttk.Entry(conda_frame, width=20)
        self.conda_env.grid(row=0, column=4, sticky=(tk.W, tk.E))
        self.conda_env.insert(0, "hotellab39")
        
    def start_resource_monitoring(self):
        """启动资源监控"""
        try:
            import psutil
        except ImportError:
            self.log("正在安装必要的依赖 psutil...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "psutil"], check=True)
                self.log("psutil 安装成功！")
                import psutil
            except Exception as e:
                self.log(f"安装 psutil 失败: {str(e)}")
                return
        
        def update_resource_info():
            process = psutil.Process()
            while self.monitoring:
                try:
                    # 获取内存使用情况
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
                    
                    # 获取CPU使用情况
                    cpu_percent = process.cpu_percent(interval=1)
                    
                    # 使用after方法更新UI
                    self.root.after(0, self.update_resource_labels, memory_mb, cpu_percent)
                    
                    # 每秒更新一次
                    time.sleep(1)
                except Exception as e:
                    self.log(f"资源监控错误: {str(e)}")
                    break
        
        self.monitor_thread = threading.Thread(target=update_resource_info)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
    def update_resource_labels(self, memory_mb, cpu_percent):
        """更新资源监控标签"""
        try:
            if hasattr(self, 'memory_label') and self.memory_label.winfo_exists():
                self.memory_label.config(text=f"内存使用: {memory_mb:.1f} MB")
            if hasattr(self, 'cpu_label') and self.cpu_label.winfo_exists():
                self.cpu_label.config(text=f"CPU使用: {cpu_percent:.1f}%")
        except Exception:
            pass  # 忽略更新失败的错误
            
    def on_closing(self):
        """窗口关闭处理"""
        try:
            # 停止资源监控
            self.monitoring = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=0.1)
            
            # 停止任务轮询
            self.polling = False
            if self.polling_thread and self.polling_thread.is_alive():
                self.polling_thread.join(timeout=0.1)
            
            # 关闭任务窗口
            if self.task_window:
                self.task_window.destroy()
            
            # 销毁主窗口
            self.root.destroy()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            self.root.destroy()  # 确保窗口被关闭
            
    def __del__(self):
        """析构函数"""
        # 不在这里处理线程清理，改为在on_closing中处理
        pass
        
    def create_function_section(self):
        func_frame = ttk.LabelFrame(self.main_frame, text="功能区", padding="5")
        func_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 配置func_frame的列权重和行权重
        func_frame.grid_columnconfigure(0, weight=1)  # 包管理和仓库操作列
        func_frame.grid_columnconfigure(1, weight=1)  # 任务操作列
        func_frame.grid_rowconfigure(0, weight=1)  # 让行可以扩展
        
        # 创建左侧操作区域框架
        left_frame = ttk.Frame(func_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        left_frame.grid_columnconfigure(0, weight=1)
        left_frame.grid_rowconfigure(0, weight=0)  # 包管理区域不需要扩展
        left_frame.grid_rowconfigure(1, weight=0)  # 仓库操作区域不需要扩展
        
        # 构建操作
        build_frame = ttk.LabelFrame(left_frame, text="包管理", padding="3")
        build_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        
        # 配置build_frame的列权重
        build_frame.grid_columnconfigure(0, weight=1)
        
        # 创建包管理操作组
        build_ops_frame = ttk.Frame(build_frame)
        build_ops_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        build_ops_frame.grid_columnconfigure(1, weight=1)
        build_ops_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(build_ops_frame, text="构建操作").grid(row=0, column=0, padx=(0,10))
        ttk.Button(build_ops_frame, text="本地构建包", 
                  command=lambda: self.run_in_thread(self.build_package)).grid(
                      row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        ttk.Button(build_ops_frame, text="本地安装包", 
                  command=lambda: self.run_in_thread(self.install_package)).grid(
                      row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        # 创建发布操作组
        publish_ops_frame = ttk.Frame(build_frame)
        publish_ops_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        publish_ops_frame.grid_columnconfigure(1, weight=1)
        publish_ops_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(publish_ops_frame, text="发布操作").grid(row=0, column=0, padx=(0,10))
        ttk.Button(publish_ops_frame, text="重新构建并上传",
                  command=lambda: self.run_in_thread(self.upload_package)).grid(
                      row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        ttk.Button(publish_ops_frame, text="清理构建目录",
                  command=lambda: self.run_in_thread(self.clean_build_dirs)).grid(
                      row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        # 仓库操作
        repo_frame = ttk.LabelFrame(left_frame, text="仓库操作", padding="3")
        repo_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        
        # 配置repo_frame的列权重
        repo_frame.grid_columnconfigure(0, weight=1)
        
        # UI测试仓库操作组
        ui_test_frame = ttk.Frame(repo_frame)
        ui_test_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        ui_test_frame.grid_columnconfigure(1, weight=1)
        ui_test_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(ui_test_frame, text="UI测试仓库").grid(row=0, column=0, padx=(0,10))
        ttk.Button(ui_test_frame, text="查看依赖", 
                  command=lambda: self.run_in_thread(self.check_ui_test_version)).grid(
                      row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        ttk.Button(ui_test_frame, text="更新依赖", 
                  command=lambda: self.run_in_thread(self.update_ui_test_repo)).grid(
                      row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        # AI生成仓库操作组
        ai_repo_frame = ttk.Frame(repo_frame)
        ai_repo_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        ai_repo_frame.grid_columnconfigure(1, weight=1)
        ai_repo_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(ai_repo_frame, text="AI生成仓库").grid(row=0, column=0, padx=(0,10))
        ttk.Button(ai_repo_frame, text="查看依赖", 
                  command=lambda: self.run_in_thread(self.check_ai_repo_version)).grid(
                      row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        ttk.Button(ai_repo_frame, text="更新依赖", 
                  command=lambda: self.run_in_thread(self.update_ai_repo)).grid(
                      row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        # 任务操作
        task_frame = ttk.LabelFrame(func_frame, text="任务操作", padding="5")
        task_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        
        # 配置task_frame的列权重
        task_frame.grid_columnconfigure(0, weight=1)
        
        # MPAAS任务选择
        ttk.Label(task_frame, text="MPAAS任务:").grid(row=0, column=0, sticky=tk.W, padx=5)
        
        # 创建MPAAS任务列表框
        mpaas_frame = ttk.Frame(task_frame)
        mpaas_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        
        self.mpaas_jobs = {
            "Trip-Online-UiLabTestjob": ("464", tk.BooleanVar()),
            "Trip-H5-UiLabTestjob": ("463", tk.BooleanVar()),
            "UiLabTestjob": ("454", tk.BooleanVar())
        }
        # jobId为464、463的mpaas porjectId为1521
        # jobId为454的mpaas porjectId为1517
        
        for i, (name, (job_id, var)) in enumerate(self.mpaas_jobs.items()):
            ttk.Checkbutton(mpaas_frame, text=f"{name} ({job_id})", 
                          variable=var).grid(row=i, column=0, sticky=tk.W)
        
        # MPAAS任务按钮组
        mpaas_btn_frame = ttk.Frame(task_frame)
        mpaas_btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=5, pady=(5,10))
        mpaas_btn_frame.grid_columnconfigure(0, weight=1)
        mpaas_btn_frame.grid_columnconfigure(1, weight=1)
        mpaas_btn_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Button(mpaas_btn_frame, text="执行任务", 
                  command=lambda: self.run_in_thread(self.trigger_mpaas)).grid(
                      row=0, column=0, padx=2, pady=1, sticky=(tk.W, tk.E))
        ttk.Button(mpaas_btn_frame, text="查看结果", 
                  command=lambda: self.run_in_thread(self.check_mpaas_result)).grid(
                      row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        self.poll_button = ttk.Button(mpaas_btn_frame, text="开始轮询", 
                                    command=self.toggle_polling)
        self.poll_button.grid(row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
        # AI生成任务相关代码已注释掉
        # # AI生成任务选择
        # ttk.Label(task_frame, text="AI生成任务:").grid(row=3, column=0, sticky=tk.W, padx=5)
        # 
        # # 创建AI生成任务列表框
        # ai_frame = ttk.Frame(task_frame)
        # ai_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)
        # 
        # # 配置ai_frame的列权重
        # ai_frame.grid_columnconfigure(0, weight=1)
        # ai_frame.grid_columnconfigure(1, weight=1)
        # 
        # self.ai_cases = {
        #     "web-成功": ("16512", tk.BooleanVar()),
        #     "ctrip-成功": ("16483", tk.BooleanVar()),
        #     "trip-成功": ("14831", tk.BooleanVar()),
        #     "h5-成功": ("14832", tk.BooleanVar())
        # }
        # 
        # # 每行两个选项布局
        # for i, (name, (case_id, var)) in enumerate(self.ai_cases.items()):
        #     row = i // 2  # 计算行号
        #     col = i % 2   # 计算列号
        #     ttk.Checkbutton(ai_frame, text=f"{name} ({case_id})", 
        #                   variable=var).grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
        # 
        # # AI生成任务按钮组
        # ai_btn_frame = ttk.Frame(task_frame)
        # ai_btn_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), padx=5, pady=(10,5))
        # ai_btn_frame.grid_columnconfigure(0, weight=1)
        # ai_btn_frame.grid_columnconfigure(1, weight=1)
        # ai_btn_frame.grid_columnconfigure(2, weight=1)
        # 
        # ttk.Button(ai_btn_frame, text="AI生成", 
        #           command=lambda: self.run_in_thread(self.trigger_ai_generate)).grid(
        #               row=0, column=0, padx=2, pady=1, sticky=(tk.W, tk.E))
        # ttk.Button(ai_btn_frame, text="查看结果", 
        #           command=lambda: self.run_in_thread(self.check_ai_result)).grid(
        #               row=0, column=1, padx=2, pady=1, sticky=(tk.W, tk.E))
        # ttk.Button(ai_btn_frame, text="轮询结果", 
        #           command=lambda: self.run_in_thread(self.poll_ai_result)).grid(
        #               row=0, column=2, padx=2, pady=1, sticky=(tk.W, tk.E))
        
    def create_log_section(self):
        # 创建日志区域标题框架
        title_frame = ttk.Frame(self.main_frame)
        title_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5,0))
        
        ttk.Label(title_frame, text="日志输出").pack(side=tk.LEFT, padx=5)
        ttk.Button(title_frame, text="clear", width=4, 
                  command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="查看所有任务", 
                  command=self.show_all_tasks).pack(side=tk.RIGHT, padx=5)
        
        # 创建日志内容区域
        log_frame = ttk.LabelFrame(self.main_frame)
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0,5))
        
        # 配置log_frame的网格权重
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        # 创建文本框
        self.log_text = tk.Text(log_frame, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text['yscrollcommand'] = scrollbar.set
        
    def log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        
    def clear_log(self):
        """清除日志内容"""
        self.log_text.delete("1.0", tk.END)
        
    def run_in_thread(self, func):
        thread = threading.Thread(target=func)
        thread.daemon = True
        thread.start()
        
    def build_package(self):
        try:
            use_conda = self.env_var.get() == "conda"
            conda_env = self.conda_env.get() if use_conda else None
            
            if use_conda:
                if not self.utils.check_conda():
                    self.log("错误: 未安装conda")
                    return
                if not self.utils.check_conda_env(conda_env):
                    self.log(f"错误: conda环境 '{conda_env}' 不存在")
                    return
            
            self.log("开始构建包...")
            output = self.utils.build_package(use_conda, conda_env)
            self.log("构建完成!")
            self.log(output)
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def install_package(self):
        try:
            use_conda = self.env_var.get() == "conda"
            conda_env = self.conda_env.get() if use_conda else None
            
            if use_conda:
                if not self.utils.check_conda():
                    self.log("错误: 未安装conda")
                    return
                if not self.utils.check_conda_env(conda_env):
                    self.log(f"错误: conda环境 '{conda_env}' 不存在")
                    return
            
            self.log("开始安装包...")
            output = self.utils.install_package(use_conda, conda_env)
            self.log("安装完成!")
            self.log(output)
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def upload_package(self):
        """重新构建并上传包到测试环境"""
        try:
            self.log("开始重新构建并上传包到测试环境...")
            output = self.utils.upload_package()
            self.log("重新构建并上传完成!")
            self.log(output)
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def update_ui_test_repo(self):
        try:
            version = self.utils.get_package_version()
            if not version:
                self.log("错误: 未找到包版本号")
                return
                
            repo_url = "https://git.dev.sh.ctripcorp.com/hotel-mob/hotel-order-detail-ui-test"
            branch = "feat/yangyangpeng/test"
            token = "aRkxdo_xsXkCyK1mSbg-"
            project_id = 108486
            self.log(f"开始更新UI测试仓库 requirements.txt (版本: {version})...")
            result = self.utils.update_requirements(project_id, branch, token, version)
            self.log("更新完成!")
            self.log(json.dumps(result, indent=2))
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def update_ai_repo(self):
        try:
            version = self.utils.get_package_version()
            if not version:
                self.log("错误: 未找到包版本号")
                return
                
            repo_url = "https://git.dev.sh.ctripcorp.com/uiautomationwithai/aigenerate-test"
            branch = "feat/test"
            token = "_ugnErSzTKS-hMz9VPFH"
            project_id = 127690
            self.log(f"开始更新AI生成仓库 requirements.txt (版本: {version})...")
            result = self.utils.update_requirements(project_id, branch, token, version)
            self.log("更新完成!")
            self.log(json.dumps(result, indent=2))
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def trigger_mpaas(self):
        """触发MPAAS任务"""
        try:
            # 获取选中的任务
            selected_jobs = [
                (name, job_id) 
                for name, (job_id, var) in self.mpaas_jobs.items() 
                if var.get()
            ]
            
            if not selected_jobs:
                self.log("请选择要触发的MPAAS任务")
                return
            
            for name, job_id in selected_jobs:
                self.log(f"开始触发MPAAS任务: {name} (Job ID: {job_id})...")
                result = self.utils.trigger_mpaas_job(job_id)
                if result and result.get("status") == "200":
                    task_id = result["data"]["taskId"]
                    task_name = result["data"]["taskName"]
                    # 记录任务ID
                    if job_id not in self.current_task_ids:
                        self.current_task_ids[job_id] = []
                    self.current_task_ids[job_id].append(task_id)
                    self.log(f"{name} 触发成功! Task ID: {task_id}, Task Name: {task_name}")
                    # 显示报告链接
                    report_url = f"https://mpaas.ctripcorp.com/cloudPhone/uiCaseRecord/{task_id}"
                    self.log(f"报告链接: {report_url}")
                else:
                    self.log(f"{name} 触发失败: {json.dumps(result, indent=2)}")
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def trigger_ai_generate(self):
        """触发AI生成任务"""
        try:
            # 获取选中的case
            selected_cases = [
                (name, case_id) 
                for name, (case_id, var) in self.ai_cases.items() 
                if var.get()
            ]
            
            if not selected_cases:
                self.log("请选择要触发的AI生成任务")
                return
            
            for name, case_id in selected_cases:
                self.log(f"开始触发AI生成任务: {name} (Case ID: {case_id})...")
                result = self.utils.trigger_ai_generate(case_id)
                self.log(f"{name} 触发完成!")
                if result:
                    self.log(json.dumps(result, indent=2))
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def clean_build_dirs(self):
        """清理构建目录"""
        try:
            self.log("开始清理构建目录...")
            self.utils.clean_dirs()
            self.log("清理完成！dist 和 build 目录已删除")
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def check_ui_test_version(self):
        """查看UI测试仓库的依赖版本"""
        try:
            project_id = 108486
            branch = "feat/yangyangpeng/test"
            token = "aRkxdo_xsXkCyK1mSbg-"
            
            self.log("开始获取UI测试仓库的requirements.txt内容...")
            result = self.utils.get_repo_version(project_id, branch, token)
            self.log(result['content'])
        except Exception as e:
            self.log(f"错误: {str(e)}")
            
    def check_ai_repo_version(self):
        """查看AI生成仓库的依赖版本"""
        try:
            project_id = 127690
            branch = "main"
            token = "_ugnErSzTKS-hMz9VPFH"
            
            self.log("开始获取AI生成仓库的requirements.txt内容...")
            result = self.utils.get_repo_version(project_id, branch, token)
            self.log(result['content'])
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def check_mpaas_result(self):
        """查看MPAAS任务结果"""
        try:
            # 获取选中的任务
            selected_jobs = [
                (name, job_id) 
                for name, (job_id, var) in self.mpaas_jobs.items() 
                if var.get()
            ]
            
            if not selected_jobs:
                self.log("请选择要查看的MPAAS任务")
                return
            
            for name, job_id in selected_jobs:
                self.log(f"获取MPAAS任务列表: {name} (Job ID: {job_id})...")
                result = self.utils.get_mpaas_tasks(job_id)
                if result and result.get("status") == "200":
                    tasks = result["data"]["content"]
                    # 筛选出当前会话触发的任务
                    current_tasks = [
                        task for task in tasks 
                        if job_id in self.current_task_ids 
                        and task["taskId"] in self.current_task_ids[job_id]
                    ]
                    if current_tasks:
                        for task in current_tasks:
                            self.log(f"任务ID: {task['taskId']}")
                            self.log(f"任务名称: {task['taskName']}")
                            self.log(f"任务状态: {task['taskStatus']}")
                            self.log(f"执行结果: {task['taskResult']}")
                            if task.get('taskCaseResult'):
                                self.log(f"用例结果: {json.dumps(task['taskCaseResult'], indent=2)}")
                            # 显示报告链接
                            report_url = f"https://mpaas.ctripcorp.com/cloudPhone/uiCaseRecord/{task['taskId']}"
                            self.log(f"报告链接: {report_url}")
                            self.log("-" * 50)
                    else:
                        self.log("未找到当前会话触发的任务")
                else:
                    self.log(f"获取任务列表失败: {json.dumps(result, indent=2)}")
        except Exception as e:
            self.log(f"错误: {str(e)}")
            
    def toggle_polling(self):
        """切换轮询状态"""
        if not self.polling:
            # 开始轮询
            self.polling = True
            self.poll_button.configure(text="停止轮询")
            self.polling_thread = threading.Thread(target=self.poll_mpaas_result)
            self.polling_thread.daemon = True
            self.polling_thread.start()
        else:
            # 停止轮询
            self.polling = False
            self.poll_button.configure(text="开始轮询")
            if self.polling_thread:
                self.polling_thread = None
            
    def poll_mpaas_result(self):
        """轮询MPAAS任务结果"""
        try:
            # 获取选中的任务
            selected_jobs = [
                (name, job_id) 
                for name, (job_id, var) in self.mpaas_jobs.items() 
                if var.get()
            ]
            
            if not selected_jobs:
                self.log("请选择要轮询的MPAAS任务")
                self.polling = False
                self.poll_button.configure(text="开始轮询")
                return
            
            while self.polling:
                all_tasks_finished = True
                
                for name, job_id in selected_jobs:
                    if job_id not in self.current_task_ids or not self.current_task_ids[job_id]:
                        self.log(f"{name}: 未找到当前会话触发的任务")
                        continue
                        
                    result = self.utils.get_mpaas_tasks(job_id)
                    if result and result.get("status") == "200":
                        tasks = result["data"]["content"]
                        current_tasks = [
                            task for task in tasks 
                            if task["taskId"] in self.current_task_ids[job_id]
                        ]
                        
                        for task in current_tasks:
                            status = task["taskStatus"]
                            self.log(f"任务ID {task['taskId']} - 状态: {status}")
                            if status not in ["FINISHED", "FAILED", "CANCELED"]:
                                all_tasks_finished = False
                            elif status == "FINISHED":  # 任务完成时显示报告链接
                                report_url = f"https://mpaas.ctripcorp.com/cloudPhone/uiCaseRecord/{task['taskId']}"
                                self.log(f"报告链接: {report_url}")
                    else:
                        self.log(f"获取任务列表失败: {json.dumps(result, indent=2)}")
                        
                if all_tasks_finished:
                    self.log("所有任务已完成")
                    self.polling = False
                    self.poll_button.configure(text="开始轮询")
                    break
                    
                if self.polling:  # 再次检查，避免在sleep期间被停止
                    self.log("等待10秒后重新查询...")
                    time.sleep(10)
                    
        except Exception as e:
            self.log(f"错误: {str(e)}")
            self.polling = False
            self.poll_button.configure(text="开始轮询")
            
    def check_ai_result(self):
        """查看AI生成任务结果"""
        try:
            # 获取选中的case
            selected_cases = [
                (name, case_id) 
                for name, (case_id, var) in self.ai_cases.items() 
                if var.get()
            ]
            
            if not selected_cases:
                self.log("请选择要查看的AI生成任务")
                return
            
            for name, case_id in selected_cases:
                self.log(f"获取AI生成任务结果: {name} (Case ID: {case_id})...")
                result = self.utils.get_ai_result(case_id)
                if result:
                    self.log(json.dumps(result, indent=2))
        except Exception as e:
            self.log(f"错误: {str(e)}")
            
    def poll_ai_result(self):
        """轮询AI生成任务结果"""
        try:
            # 获取选中的case
            selected_cases = [
                (name, case_id) 
                for name, (case_id, var) in self.ai_cases.items() 
                if var.get()
            ]
            
            if not selected_cases:
                self.log("请选择要轮询的AI生成任务")
                return
            
            for name, case_id in selected_cases:
                self.log(f"开始轮询AI生成任务结果: {name} (Case ID: {case_id})...")
                result = self.utils.poll_ai_result(case_id)
                if result:
                    self.log(json.dumps(result, indent=2))
        except Exception as e:
            self.log(f"错误: {str(e)}")
        
    def show_all_tasks(self):
        """显示所有任务列表"""
        # 如果窗口已经存在，就把它提到前面
        if self.task_window is not None:
            self.task_window.lift()
            return
            
        # 创建新窗口
        self.task_window = tk.Toplevel(self.root)
        self.task_window.title("任务列表")
        
        # 窗口关闭时的处理
        def on_closing():
            self.task_window.destroy()
            self.task_window = None
            
        self.task_window.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 获取主窗口位置和大小
        main_x = self.root.winfo_x()
        main_y = self.root.winfo_y()
        main_width = self.root.winfo_width()
        main_height = self.root.winfo_height()
        
        # 设置任务窗口位置和大小
        window_width = 800
        window_height = main_height
        x = main_x + main_width
        y = main_y
        self.task_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建任务列表
        task_frame = ttk.Frame(self.task_window, padding="5")
        task_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建水平滚动容器
        container = ttk.Frame(task_frame)
        container.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ("任务ID", "任务名称", "状态", "结果", "成功率", "报告链接")
        tree = ttk.Treeview(container, columns=columns, show="headings")
        
        # 设置列标题和宽度
        column_widths = {
            "任务ID": 80,
            "任务名称": 200,
            "状态": 100,
            "结果": 100,
            "成功率": 100,
            "报告链接": 200
        }
        
        for col, width in column_widths.items():
            tree.heading(col, text=col)
            tree.column(col, width=width, minwidth=width)
        
        # 添加垂直滚动条
        vsb = ttk.Scrollbar(container, orient="vertical", command=tree.yview)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加水平滚动条
        hsb = ttk.Scrollbar(task_frame, orient="horizontal", command=tree.xview)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加刷新按钮
        refresh_btn = ttk.Button(self.task_window, text="刷新", 
                               command=lambda: self.refresh_task_list(tree))
        refresh_btn.pack(pady=5)
        
        # 初始加载任务列表
        self.refresh_task_list(tree)
        
        # 双击事件处理
        tree.bind("<Double-1>", lambda e: self.open_task_report(tree))
        # 单击事件处理（用于链接点击）
        tree.bind("<Button-1>", lambda e: self.handle_click(tree, e))
        
    def refresh_task_list(self, tree):
        """刷新任务列表"""
        # 清空现有内容
        for item in tree.get_children():
            tree.delete(item)
            
        # 获取所有任务的最新状态
        for job_id in self.current_task_ids:
            result = self.utils.get_mpaas_tasks(job_id)
            if result and result.get("status") == "200":
                tasks = result["data"]["content"]
                # 筛选出当前会话触发的任务
                current_tasks = [
                    task for task in tasks 
                    if task["taskId"] in self.current_task_ids[job_id]
                ]
                
                # 添加到树形视图
                for task in current_tasks:
                    # 计算成功率
                    success_rate = "N/A"
                    if task.get('taskCaseResult'):
                        case_result = task['taskCaseResult']
                        total = case_result.get('total', 0)
                        success_count = case_result.get('successCount', 0)
                        if total > 0:
                            success_rate = f"{(success_count / total * 100):.1f}%"
                    
                    # 构建报告链接
                    report_url = f"https://mpaas.ctripcorp.com/cloudPhone/uiCaseRecord/{task['taskId']}"
                    
                    tree.insert("", tk.END, values=(
                        task["taskId"],
                        task["taskName"],
                        task["taskStatus"],
                        task["taskResult"],
                        success_rate,
                        report_url
                    ))
                    
    def handle_click(self, tree, event):
        """处理点击事件"""
        region = tree.identify("region", event.x, event.y)
        if region == "cell":
            column = tree.identify_column(event.x)
            column_id = int(column[1]) - 1  # 转换为0基索引
            if column_id == 5:  # 报告链接列
                item = tree.identify_row(event.y)
                if item:
                    values = tree.item(item)["values"]
                    report_url = values[5]
                    import webbrowser
                    webbrowser.open(report_url)
                    
    def open_task_report(self, tree):
        """打开任务报告（双击任意列）"""
        selected_item = tree.selection()
        if not selected_item:
            return
            
        # 获取选中项的任务ID
        task_id = tree.item(selected_item[0])["values"][0]
        
        # 构建报告URL
        report_url = f"https://mpaas.ctripcorp.com/cloudPhone/uiCaseRecord/{task_id}"
        
        # 使用默认浏览器打开报告
        import webbrowser
        webbrowser.open(report_url)

def check_dependencies():
    """检查并安装必要的依赖"""
    # 移除此函数，因为依赖检查已经移到实际使用时

def main():
    root = tk.Tk()
    app = DebugToolUI(root)
    root.mainloop()

if __name__ == "__main__":
    main() 