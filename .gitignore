*.py[cod]

# Packages
*.egg
*.egg-info
dist
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib64
__pycache__/


labuiframe.egg-info

 labuiframe/__pycache__
 labuiframe/lib/__pycache__
 labuiframe/lib/utils/__pycache__

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject
.vs/
tmp/
*.log
_site
apps
_build/
*.spec
htmlcov/
cover/
.idea/
.DS_Store

# test results
log/
pocounit-results/

# screenshot
screenshot/
labconfig.ini

.specstory
.vscode

