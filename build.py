#!/usr/bin/env python
import os
import sys
import shutil
import subprocess
from pathlib import Path

def show_help():
    """显示帮助信息"""
    print("使用方法: python build.py [命令] [conda环境名称]")
    print("支持的命令:")
    print("  help                 - 显示此帮助信息")
    print("  upload               - 构建并上传包到测试环境")
    print("  test <conda环境名称>  - 构建并安装到本地环境 (需要指定conda环境名称)")
    print("")
    print("示例:")
    print("  python build.py help")
    print("  python build.py upload")
    print("  python build.py test hotellab39")

def clean_dirs():
    """清理构建目录"""
    for dir_name in ['dist', 'build']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)

def check_conda():
    """检查是否安装了conda"""
    try:
        if os.system('conda --version') != 0:
            raise FileNotFoundError
    except FileNotFoundError:
        print("错误: 未安装conda，请先安装conda")
        sys.exit(1)

def check_conda_env(env_name):
    """检查指定的conda环境是否存在"""
    result = os.popen('conda env list').read()
    if env_name not in result:
        print(f"错误: conda环境 '{env_name}' 不存在")
        print("可用的conda环境:")
        print(result)
        sys.exit(1)

def upload_package():
    """构建并上传包到测试环境"""
    print("开始构建并上传到测试环境...")
    clean_dirs()
    os.system('python setup-test.py sdist bdist_wheel')
    os.system('pip install twine')
    os.system('twine upload --repository internal dist/*')
    print("测试包已上传完成")
    clean_dirs()

def install_local(env_name):
    """构建并安装到本地环境"""
    check_conda()
    check_conda_env(env_name)
    
    clean_dirs()
    os.system('python setup.py sdist bdist_wheel')
    
    # 构建安装命令
    install_cmd = (
        f'conda run -n {env_name} pip install '
        f'--find-links http://pypi.infosec.ctripcorp.com/packages/ '
        f'--trusted-host pypi.infosec.ctripcorp.com '
        f'--index-url http://mirrors.ops.ctripcorp.com/latest/pypi/simple/ '
        f'--trusted-host mirrors.ops.ctripcorp.com '
    )
    
    # 获取wheel文件
    wheel_file = next(Path('dist').glob('*.whl'))
    install_cmd += str(wheel_file)
    
    # 执行安装命令
    os.system(install_cmd)
    clean_dirs()

def main():
    # 切换到脚本所在目录
    os.chdir(Path(__file__).parent.absolute())
    
    if len(sys.argv) < 2:
        show_help()
        sys.exit(0)

    command = sys.argv[1]
    
    if command == "help":
        show_help()
    elif command == "upload":
        upload_package()
    elif command == "test":
        if len(sys.argv) < 3:
            print("错误: test命令需要指定conda环境名称")
            print("用法: python build.py test <conda环境名称>")
            sys.exit(1)
        install_local(sys.argv[2])
    else:
        print(f"错误: 未知的命令 '{command}'")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    main()