import os
import platform
import subprocess

def find_system_adb():
    """尝试在系统中找到adb路径"""
    # 常见的adb路径
    common_paths = [
        "/usr/local/bin/adb",  # macOS通过homebrew安装
        "/opt/homebrew/bin/adb",  # Apple Silicon Macs
        "C:\\Program Files\\Android\\platform-tools\\adb.exe",  # Windows
        "C:\\Program Files (x86)\\Android\\platform-tools\\adb.exe",  # Windows
        os.path.expanduser("~/Library/Android/sdk/platform-tools/adb"),  # macOS Android SDK
        os.path.expanduser("~/Android/Sdk/platform-tools/adb"),  # Linux Android SDK
    ]
    
    system = platform.system()
    for path in common_paths:
        if os.path.exists(path):
            print(f"找到adb: {path}")
            return path
    
    # 尝试通过which命令查找（Unix/Linux/macOS）
    if system != "Windows":
        try:
            result = subprocess.run(['which', 'adb'], capture_output=True, text=True)
            if result.returncode == 0:
                path = result.stdout.strip()
                if path:
                    print(f"通过which命令找到adb: {path}")
                    return path
        except Exception as e:
            print(f"执行which命令时出错: {e}")
    
    # 尝试通过where命令查找（Windows）
    if system == "Windows":
        try:
            result = subprocess.run(['where', 'adb'], capture_output=True, text=True)
            if result.returncode == 0:
                paths = result.stdout.strip().split('\n')
                if paths:
                    path = paths[0].strip()
                    print(f"通过where命令找到adb: {path}")
                    return path
        except Exception as e:
            print(f"执行where命令时出错: {e}")
    
    print("未找到adb可执行文件")
    return None

if __name__ == "__main__":
    adb_path = find_system_adb()
    if adb_path:
        print(f"\nadb路径: {adb_path}")
        print("\n您可以通过以下方式在airtest中使用此adb:")
        print(f"1. 设置环境变量: export ANDROID_HOME={os.path.dirname(os.path.dirname(adb_path))}")
        print(f"2. 在代码中显式指定: connect_device('Android:///?adb_path={adb_path}')")
    else:
        print("\n没有找到adb。您可能需要先安装Android SDK或platform-tools。") 