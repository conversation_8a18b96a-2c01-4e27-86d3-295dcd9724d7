# coding=utf-8

import os
from setuptools import setup, find_packages
from datetime import datetime

def parse_requirements(filename='requirements.txt'):
    """ load requirements from a pip requirements file. (replacing from pip.req import parse_requirements)"""
    lineiter = (line.strip() for line in open(filename))
    return [line for line in lineiter if line and not line.startswith("#")]


project_name = find_packages()[0]
if '.' in project_name:
    project_name = project_name.split('.', 1)[0]

if os.path.exists('requirements.txt'):
    reqs = parse_requirements()
else:
    reqs = []

# 获取当前时间并格式化为版本号
version = datetime.now().strftime("%Y.%m.%d.%H%M%S").replace('.0', '.').replace('2025', '2001')

# 打测试包，可以执行以下命令
# 测试包版本命名规则：2000.9.30.155340（2000年为固定头）
# version = '2000.1.1.31'
# python setup.py sdist bdist_wheel
# twine upload --repository internal dist/*

setup(
    name='labuiframe',
    version=version,
    description='A test automation project using poco and pocounit.',
    packages=find_packages(),
    include_package_data=True,
    install_requires=reqs
)
